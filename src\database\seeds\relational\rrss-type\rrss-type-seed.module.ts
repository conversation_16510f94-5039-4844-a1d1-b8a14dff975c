import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { rrssTypeEntity } from '../../../../rrss-types/infrastructure/persistence/relational/entities/rrss-type.entity';
import { rrssTypeSeedService } from './rrss-type-seed.service';

@Module({
  imports: [TypeOrmModule.forFeature([rrssTypeEntity])],
  providers: [rrssTypeSeedService],
  exports: [rrssTypeSeedService],
})
export class rrssTypeSeedModule {}
