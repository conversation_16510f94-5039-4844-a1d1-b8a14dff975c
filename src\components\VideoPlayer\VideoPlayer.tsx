'use client';

import React, { useEffect, useRef, useCallback } from 'react';
import { Stream, type StreamPlayerApi } from '@cloudflare/stream-react';
import { useTranslations } from 'next-intl';

type VideoPlayerProps = {
  videoId: string;
  controls?: boolean;
  playing?: boolean;
  onEnded?: () => void;
  className?: string;
  onTimeUpdate?: (e: { currentTime: number; duration: number }) => void;
  muted?: boolean;
  fullscreen?: boolean;
  onError?: (error: Error) => void;
  title?: string;
};

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoId,
  controls = true,
  playing = false,
  onEnded,
  className = '',
  onTimeUpdate,
  muted = true,
  fullscreen = false,
  onError,
  title = 'Video content',
}) => {
  const tVideoPlayer = useTranslations('VideoPlayer');
  const streamRef = useRef<StreamPlayerApi>();
  const videoRef = useRef<HTMLVideoElement>(null);

  // Actualiza el tiempo de reproducción y llama al callback externo
  const handleTimeUpdate = useCallback(() => {
    try {
      if (streamRef.current && onTimeUpdate) {
        const { currentTime, duration } = streamRef.current;
        if (duration) {
          onTimeUpdate({ currentTime, duration });
        }
      }
    } catch (error) {
      console.error('Error updating video time:', error);
      onError?.(new Error(tVideoPlayer('timeUpdateError')));
    }
  }, [onTimeUpdate, onError, tVideoPlayer]);

  useEffect(() => {
    if (!streamRef.current) return;
    const player = streamRef.current;
    player.addEventListener('timeupdate', handleTimeUpdate);

    return () => {
      player.removeEventListener('timeupdate', handleTimeUpdate);
    };
  }, [handleTimeUpdate]);

  useEffect(() => {
    if (!streamRef.current) return;
    const handlePlayback = async () => {
      try {
        if (playing) {
          await streamRef.current?.play();
        } else {
          streamRef.current?.pause();
        }
      } catch (error) {
        console.error('Error controlling video playback:', error);
        onError?.(new Error(tVideoPlayer('playbackError')));
      }
    };
    void handlePlayback();
  }, [playing, onError, tVideoPlayer]);

  useEffect(() => {
    if (videoRef.current) {
      if (playing) {
        videoRef.current.play().catch(e => {
          console.error('Error autostart video:', e);
        });
      } else {
        videoRef.current.pause();
      }
    }
  }, [playing, videoId]);

  const handleStreamError = () => {
    onError?.(new Error(tVideoPlayer('streamError')));
  };

  const handleEnded = () => {
    if (onEnded) {
      onEnded();
    }
  };

  return (
    <div
      className={`${className} ${fullscreen ? 'fixed inset-0 z-50 bg-black' : 'w-screen md:w-[440px] max-w-full h-full'}`}
      role="region"
      aria-label="Video player"
    >
      {/* Use native video as fallback if Stream fails */}
      {videoId.startsWith('http') ? (
        // eslint-disable-next-line jsx-a11y/media-has-caption
        <video
          ref={videoRef}
          src={videoId}
          controls={controls}
          className={`w-full max-h-[80vh] ${className}`}
          onEnded={handleEnded}
          playsInline
          aria-label={title}
          title={title}
        />
      ) : (
        <Stream
          src={videoId}
          streamRef={streamRef}
          autoplay
          responsive
          controls={controls}
          muted={muted}
          preload
          onEnded={handleEnded}
          onError={handleStreamError}
        />
      )}
    </div>
  );
};

export default VideoPlayer;
