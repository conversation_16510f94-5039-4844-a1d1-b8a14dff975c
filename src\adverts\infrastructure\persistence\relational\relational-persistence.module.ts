import { Module } from '@nestjs/common';
import { AdvertRepository } from '../advert.repository';
import { AdvertRelationalRepository } from './repositories/advert.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertEntity } from './entities/advert.entity';
import { AdvertLinkEntity } from '../../../../advert-links/infrastructure/persistence/relational/entities/advert-link.entity';
import { CacheServiceHelper } from '../../../../helpers/cache/cache.service';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertEntity, AdvertLinkEntity])],
  providers: [
    {
      provide: AdvertRepository,
      useClass: AdvertRelationalRepository,
    },
    CacheServiceHelper,
  ],
  exports: [AdvertRepository, CacheServiceHelper],
})
export class RelationalAdvertPersistenceModule {}
