import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NationalityEntity } from '../../../../nationalities/infrastructure/persistence/relational/entities/nationality.entity';

@Injectable()
export class NationalitySeedService {
  constructor(
    @InjectRepository(NationalityEntity)
    private repository: Repository<NationalityEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({
          code: `1`,
          name: `American`,
          en: `American`,
          es: `Estadounidense`,
        }),
        this.repository.create({
          code: `94`,
          name: `Andorran`,
          en: `Andorran`,
          es: `Andorrana`,
        }),
        this.repository.create({
          code: `2`,
          name: `Argentine`,
          en: `Argentine`,
          es: `Argentina`,
        }),
        this.repository.create({
          code: `4`,
          name: `Austrian`,
          en: `Austrian`,
          es: `Austriaca`,
        }),
        this.repository.create({
          code: `92`,
          name: `Afghan`,
          en: `Afghan`,
          es: `Afghana`,
        }),
        this.repository.create({
          code: `67`,
          name: `Albanian`,
          en: `Albanian`,
          es: `Albanesa`,
        }),
        this.repository.create({
          code: `93`,
          name: `Algerian`,
          en: `Algerian`,
          es: `Argelina`,
        }),
        this.repository.create({
          code: `95`,
          name: `Angolan`,
          en: `Angolan`,
          es: `Angoleña`,
        }),
        this.repository.create({
          code: `96`,
          name: `Antiguans`,
          en: `Antiguans`,
          es: `Antiguana`,
        }),
        this.repository.create({
          code: `97`,
          name: `Armenian`,
          en: `Armenian`,
          es: `ArmenianArmenia`,
        }),
        this.repository.create({
          code: `3`,
          name: `Australian`,
          en: `Australian`,
          es: `Australiana`,
        }),
        this.repository.create({
          code: `98`,
          name: `Azerbaijani`,
          en: `Azerbaijani`,
          es: `Azerbaiyana`,
        }),
        this.repository.create({
          code: `99`,
          name: `Bahamian`,
          en: `Bahamian`,
          es: `Bahameña`,
        }),
        this.repository.create({
          code: `100`,
          name: `Bahraini`,
          en: `Bahraini`,
          es: `Bareiní`,
        }),
        this.repository.create({
          code: `5`,
          name: `Bangladeshi`,
          en: `Bangladeshi`,
          es: `Bangladesí`,
        }),
        this.repository.create({
          code: `101`,
          name: `Barbadian`,
          en: `Barbadian`,
          es: `Barbadensea`,
        }),
        this.repository.create({
          code: `102`,
          name: `Batswana`,
          en: `Batswana`,
          es: `Botsuana`,
        }),
        this.repository.create({
          code: `68`,
          name: `Belarusian`,
          en: `Belarusian`,
          es: `Bielorrusa`,
        }),
        this.repository.create({
          code: `6`,
          name: `Belgian`,
          en: `Belgian`,
          es: `Belga`,
        }),
        this.repository.create({
          code: `103`,
          name: `Belizean`,
          en: `Belizean`,
          es: `Beliceña`,
        }),
        this.repository.create({
          code: `104`,
          name: `Beninese`,
          en: `Beninese`,
          es: `Beninesa`,
        }),
        this.repository.create({
          code: `105`,
          name: `Bhutanese`,
          en: `Bhutanese`,
          es: `Butanesa`,
        }),
        this.repository.create({
          code: `106`,
          name: `Bolivian`,
          en: `Bolivian`,
          es: `Boliviana`,
        }),
        this.repository.create({
          code: `69`,
          name: `Bosnian`,
          en: `Bosnian`,
          es: `Bosnia`,
        }),
        this.repository.create({
          code: `7`,
          name: `Brazilian`,
          en: `Brazilian`,
          es: `Brasileña`,
        }),
        this.repository.create({
          code: `88`,
          name: `British`,
          en: `British`,
          es: `Británica`,
        }),
        this.repository.create({
          code: `107`,
          name: `Bruneian`,
          en: `Bruneian`,
          es: `Bruneana`,
        }),
        this.repository.create({
          code: `70`,
          name: `Bulgarian`,
          en: `Bulgarian`,
          es: `Búlgara`,
        }),
        this.repository.create({
          code: `108`,
          name: `Burkinabe`,
          en: `Burkinabe`,
          es: `Burkinabé`,
        }),
        this.repository.create({
          code: `8`,
          name: `Burmese`,
          en: `Burmese`,
          es: `Birmanesa`,
        }),
        this.repository.create({
          code: `109`,
          name: `Burundian`,
          en: `Burundian`,
          es: `Burundesa`,
        }),
        this.repository.create({
          code: `110`,
          name: `Cambodian`,
          en: `Cambodian`,
          es: `Camboyana`,
        }),
        this.repository.create({
          code: `111`,
          name: `Cameroonian`,
          en: `Cameroonian`,
          es: `Camerunesa`,
        }),
        this.repository.create({
          code: `71`,
          name: `Canadian`,
          en: `Canadian`,
          es: `Canadiense`,
        }),
        this.repository.create({
          code: `112`,
          name: `CapeVerdean`,
          en: `CapeVerdean`,
          es: `Caboverdiana`,
        }),
        this.repository.create({
          code: `9`,
          name: `Caribbean`,
          en: `Caribbean`,
          es: `Caribeña`,
        }),
        this.repository.create({
          code: `113`,
          name: `CentralAfrican`,
          en: `CentralAfrican`,
          es: `Centroafricana`,
        }),
        this.repository.create({
          code: `114`,
          name: `Chadian`,
          en: `Chadian`,
          es: `Chadiana`,
        }),
        this.repository.create({
          code: `82`,
          name: `Chilean`,
          en: `Chilean`,
          es: `Chilena`,
        }),
        this.repository.create({
          code: `10`,
          name: `Chinese`,
          en: `Chinese`,
          es: `China`,
        }),
        this.repository.create({
          code: `11`,
          name: `Colombian`,
          en: `Colombian`,
          es: `Colombiana`,
        }),
        this.repository.create({
          code: `115`,
          name: `Comoran`,
          en: `Comoran`,
          es: `Comorense`,
        }),
        this.repository.create({
          code: `116`,
          name: `Congolese`,
          en: `Congolese`,
          es: `Congoleña`,
        }),
        this.repository.create({
          code: `89`,
          name: `CostaRican`,
          en: `CostaRican`,
          es: `Costarricense`,
        }),
        this.repository.create({
          code: `117`,
          name: `Croatian`,
          en: `Croatian`,
          es: `Croata`,
        }),
        this.repository.create({
          code: `12`,
          name: `Cuban`,
          en: `Cuban`,
          es: `Cubana`,
        }),
        this.repository.create({
          code: `13`,
          name: `Cypriot`,
          en: `Cypriot`,
          es: `Chipriota`,
        }),
        this.repository.create({
          code: `14`,
          name: `Czech`,
          en: `Czech`,
          es: `Checa`,
        }),
        this.repository.create({
          code: `15`,
          name: `Danish`,
          en: `Danish`,
          es: `Danesa`,
        }),
        this.repository.create({
          code: `118`,
          name: `Djibouti`,
          en: `Djibouti`,
          es: `Yibutiana`,
        }),
        this.repository.create({
          code: `119`,
          name: `Dominican`,
          en: `Dominican`,
          es: `Dominicana`,
        }),
        this.repository.create({
          code: `16`,
          name: `Dutch`,
          en: `Dutch`,
          es: `Holandesa`,
        }),
        this.repository.create({
          code: `120`,
          name: `EastTimorese`,
          en: `EastTimorese`,
          es: `Timorense`,
        }),
        this.repository.create({
          code: `121`,
          name: `Ecuadorean`,
          en: `Ecuadorean`,
          es: `Ecuatoriana`,
        }),
        this.repository.create({
          code: `17`,
          name: `Egyptian`,
          en: `Egyptian`,
          es: `Egipcia`,
        }),
        this.repository.create({
          code: `122`,
          name: `Emirian`,
          en: `Emirian`,
          es: `Emiratí`,
        }),
        this.repository.create({
          code: `18`,
          name: `English`,
          en: `English`,
          es: `Inglesa`,
        }),
        this.repository.create({
          code: `123`,
          name: `EquatorialGuinean`,
          en: `EquatorialGuinean`,
          es: `Ecuatoguineana`,
        }),
        this.repository.create({
          code: `124`,
          name: `Eritrean`,
          en: `Eritrean`,
          es: `Eritrea`,
        }),
        this.repository.create({
          code: `19`,
          name: `Estonian`,
          en: `Estonian`,
          es: `Estonia`,
        }),
        this.repository.create({
          code: `125`,
          name: `Ethiopian`,
          en: `Ethiopian`,
          es: `Etíope`,
        }),
        this.repository.create({
          code: `126`,
          name: `Fijian`,
          en: `Fijian`,
          es: `Fiyiana`,
        }),
        this.repository.create({
          code: `20`,
          name: `Filipino`,
          en: `Filipino`,
          es: `Filipina`,
        }),
        this.repository.create({
          code: `21`,
          name: `Finnish`,
          en: `Finnish`,
          es: `Finlandesa`,
        }),
        this.repository.create({
          code: `22`,
          name: `French`,
          en: `French`,
          es: `Francesa`,
        }),
        this.repository.create({
          code: `127`,
          name: `Gabonese`,
          en: `Gabonese`,
          es: `Gabonesa`,
        }),
        this.repository.create({
          code: `128`,
          name: `Gambian`,
          en: `Gambian`,
          es: `Gambiana`,
        }),
        this.repository.create({
          code: `129`,
          name: `Georgian`,
          en: `Georgian`,
          es: `Georgiana`,
        }),
        this.repository.create({
          code: `23`,
          name: `German`,
          en: `German`,
          es: `Alemana`,
        }),
        this.repository.create({
          code: `130`,
          name: `Ghanaian`,
          en: `Ghanaian`,
          es: `Ghanesa`,
        }),
        this.repository.create({
          code: `24`,
          name: `Greek`,
          en: `Greek`,
          es: `Griega`,
        }),
        this.repository.create({
          code: `131`,
          name: `Grenadian`,
          en: `Grenadian`,
          es: `Granadina`,
        }),
        this.repository.create({
          code: `132`,
          name: `Guatemalan`,
          en: `Guatemalan`,
          es: `Guatemalteca`,
        }),
        this.repository.create({
          code: `133`,
          name: `Guinea-Bissauan`,
          en: `Guinea-Bissauan`,
          es: `Guineana bissauense`,
        }),
        this.repository.create({
          code: `134`,
          name: `Guinean`,
          en: `Guinean`,
          es: `Guineana`,
        }),
        this.repository.create({
          code: `135`,
          name: `Guyanese`,
          en: `Guyanese`,
          es: `Guyanesa`,
        }),
        this.repository.create({
          code: `136`,
          name: `Haitian`,
          en: `Haitian`,
          es: `Haitiana`,
        }),
        this.repository.create({
          code: `72`,
          name: `Herzegovinian`,
          en: `Herzegovinian`,
          es: `Bosnio-herzegovina`,
        }),
        this.repository.create({
          code: `137`,
          name: `Honduran`,
          en: `Honduran`,
          es: `Hondureña`,
        }),
        this.repository.create({
          code: `25`,
          name: `Hungarian`,
          en: `Hungarian`,
          es: `Húngara`,
        }),
        this.repository.create({
          code: `138`,
          name: `I-Kiribati`,
          en: `I-Kiribati`,
          es: `Kiribatiana`,
        }),
        this.repository.create({
          code: `26`,
          name: `Icelandic`,
          en: `Icelandic`,
          es: `Islandesa`,
        }),
        this.repository.create({
          code: `27`,
          name: `Indian`,
          en: `Indian`,
          es: `India`,
        }),
        this.repository.create({
          code: `28`,
          name: `Indonesian`,
          en: `Indonesian`,
          es: `Indonesa`,
        }),
        this.repository.create({
          code: `73`,
          name: `Iranian`,
          en: `Iranian`,
          es: `Iraní`,
        }),
        this.repository.create({
          code: `139`,
          name: `Iraqi`,
          en: `Iraqi`,
          es: `Iraqi`,
        }),
        this.repository.create({
          code: `29`,
          name: `Irish`,
          en: `Irish`,
          es: `Irlandesa`,
        }),
        this.repository.create({
          code: `74`,
          name: `Israeli`,
          en: `Israeli`,
          es: `Israeli`,
        }),
        this.repository.create({
          code: `30`,
          name: `Italian`,
          en: `Italian`,
          es: `Italiana`,
        }),
        this.repository.create({
          code: `140`,
          name: `Ivorian`,
          en: `Ivorian`,
          es: `Marfileña`,
        }),
        this.repository.create({
          code: `31`,
          name: `Jamaican`,
          en: `Jamaican`,
          es: `Jamaiquina`,
        }),
        this.repository.create({
          code: `32`,
          name: `Japanese`,
          en: `Japanese`,
          es: `Japonesa`,
        }),
        this.repository.create({
          code: `141`,
          name: `Jordanian`,
          en: `Jordanian`,
          es: `Jordana`,
        }),
        this.repository.create({
          code: `142`,
          name: `Kazakhstani`,
          en: `Kazakhstani`,
          es: `Kazaja`,
        }),
        this.repository.create({
          code: `143`,
          name: `Kenyan`,
          en: `Kenyan`,
          es: `Keniana`,
        }),
        this.repository.create({
          code: `144`,
          name: `KittianandNevisian`,
          en: `KittianandNevisian`,
          es: `Sancristobaleña-nevisiana`,
        }),
        this.repository.create({
          code: `33`,
          name: `Korean`,
          en: `Korean`,
          es: `Coreana`,
        }),
        this.repository.create({
          code: `145`,
          name: `Kuwaiti`,
          en: `Kuwaiti`,
          es: `Kuwaití`,
        }),
        this.repository.create({
          code: `146`,
          name: `Kyrgyz`,
          en: `Kyrgyz`,
          es: `Kirguisa`,
        }),
        this.repository.create({
          code: `34`,
          name: `Laotian`,
          en: `Laotian`,
          es: `Laosiana`,
        }),
        this.repository.create({
          code: `35`,
          name: `Latvian`,
          en: `Latvian`,
          es: `Letona`,
        }),
        this.repository.create({
          code: `147`,
          name: `Lebanese`,
          en: `Lebanese`,
          es: `Libanesa`,
        }),
        this.repository.create({
          code: `148`,
          name: `Liberian`,
          en: `Liberian`,
          es: `Liberiana`,
        }),
        this.repository.create({
          code: `149`,
          name: `Libyan`,
          en: `Libyan`,
          es: `Libia`,
        }),
        this.repository.create({
          code: `150`,
          name: `Liechtensteiner`,
          en: `Liechtensteiner`,
          es: `Liechtensteinera`,
        }),
        this.repository.create({
          code: `36`,
          name: `Lithuanian`,
          en: `Lithuanian`,
          es: `Lituana`,
        }),
        this.repository.create({
          code: `37`,
          name: `Luxembourger`,
          en: `Luxembourger`,
          es: `Luxemburguesa`,
        }),
        this.repository.create({
          code: `75`,
          name: `Macedonian`,
          en: `Macedonian`,
          es: `Macedonia`,
        }),
        this.repository.create({
          code: `151`,
          name: `Malagasy`,
          en: `Malagasy`,
          es: `Malgache`,
        }),
        this.repository.create({
          code: `152`,
          name: `Malawian`,
          en: `Malawian`,
          es: `Malauí`,
        }),
        this.repository.create({
          code: `39`,
          name: `Malaysian`,
          en: `Malaysian`,
          es: `Malaya`,
        }),
        this.repository.create({
          code: `153`,
          name: `Maldivian`,
          en: `Maldivian`,
          es: `Maldiva`,
        }),
        this.repository.create({
          code: `154`,
          name: `Malian`,
          en: `Malian`,
          es: `Maliana`,
        }),
        this.repository.create({
          code: `38`,
          name: `Maltese`,
          en: `Maltese`,
          es: `Maltesa`,
        }),
        this.repository.create({
          code: `155`,
          name: `Marshallese`,
          en: `Marshallese`,
          es: `Marshallesa`,
        }),
        this.repository.create({
          code: `156`,
          name: `Mauritanian`,
          en: `Mauritanian`,
          es: `Mauritana`,
        }),
        this.repository.create({
          code: `83`,
          name: `Mauritian`,
          en: `Mauritian`,
          es: `Mauriciana`,
        }),
        this.repository.create({
          code: `40`,
          name: `Mexican`,
          en: `Mexican`,
          es: `Mexicana`,
        }),
        this.repository.create({
          code: `157`,
          name: `Micronesian`,
          en: `Micronesian`,
          es: `Micronesia`,
        }),
        this.repository.create({
          code: `76`,
          name: `Moldavian`,
          en: `Moldavian`,
          es: `Moldava`,
        }),
        this.repository.create({
          code: `91`,
          name: `Monagasque`,
          en: `Monagasque`,
          es: `Monegasca`,
        }),
        this.repository.create({
          code: `158`,
          name: `Mongolian`,
          en: `Mongolian`,
          es: `Mongola`,
        }),
        this.repository.create({
          code: `77`,
          name: `Montenegrin`,
          en: `Montenegrin`,
          es: `Montenegrina`,
        }),
        this.repository.create({
          code: `41`,
          name: `Moroccan`,
          en: `Moroccan`,
          es: `Marroquí`,
        }),
        this.repository.create({
          code: `159`,
          name: `Mosotho`,
          en: `Mosotho`,
          es: `Basotho`,
        }),
        this.repository.create({
          code: `161`,
          name: `Mozambican`,
          en: `Mozambican`,
          es: `Mozambiqueña`,
        }),
        this.repository.create({
          code: `162`,
          name: `Namibian`,
          en: `Namibian`,
          es: `Namibia`,
        }),
        this.repository.create({
          code: `163`,
          name: `Nauruan`,
          en: `Nauruan`,
          es: `Nauruana`,
        }),
        this.repository.create({
          code: `164`,
          name: `Nepalese`,
          en: `Nepalese`,
          es: `Nepalesa`,
        }),
        this.repository.create({
          code: `42`,
          name: `NewZealander`,
          en: `NewZealander`,
          es: `Neozelandesa`,
        }),
        this.repository.create({
          code: `165`,
          name: `Ni-Vanuatu`,
          en: `Ni-Vanuatu`,
          es: `Vanuatuense`,
        }),
        this.repository.create({
          code: `166`,
          name: `Nicaraguan`,
          en: `Nicaraguan`,
          es: `Nicaragüense`,
        }),
        this.repository.create({
          code: `43`,
          name: `Nigerian`,
          en: `Nigerian`,
          es: `Nigeriana`,
        }),
        this.repository.create({
          code: `167`,
          name: `Nigerien`,
          en: `Nigerien`,
          es: `Nigerina`,
        }),
        this.repository.create({
          code: `168`,
          name: `NorthKorean`,
          en: `NorthKorean`,
          es: `Norcoreana`,
        }),
        this.repository.create({
          code: `200`,
          name: `NorthernIrish`,
          en: `NorthernIrish`,
          es: `Norirlandesa`,
        }),
        this.repository.create({
          code: `44`,
          name: `Norwegian`,
          en: `Norwegian`,
          es: `Noruega`,
        }),
        this.repository.create({
          code: `169`,
          name: `Omani`,
          en: `Omani`,
          es: `Omani`,
        }),
        this.repository.create({
          code: `45`,
          name: `Pakistani`,
          en: `Pakistani`,
          es: `Pakistaní`,
        }),
        this.repository.create({
          code: `170`,
          name: `Palauan`,
          en: `Palauan`,
          es: `Palaosense`,
        }),
        this.repository.create({
          code: `171`,
          name: `Panamanian`,
          en: `Panamanian`,
          es: `Panameña`,
        }),
        this.repository.create({
          code: `172`,
          name: `PapuaNewGuinean`,
          en: `PapuaNewGuinean`,
          es: `Papúa`,
        }),
        this.repository.create({
          code: `86`,
          name: `Paraguayan`,
          en: `Paraguayan`,
          es: `Paraguaya`,
        }),
        this.repository.create({
          code: `203`,
          name: `Persian`,
          en: `Persian`,
          es: `Persa`,
        }),
        this.repository.create({
          code: `46`,
          name: `Peruvian`,
          en: `Peruvian`,
          es: `Peruana`,
        }),
        this.repository.create({
          code: `47`,
          name: `Polish`,
          en: `Polish`,
          es: `Polaca`,
        }),
        this.repository.create({
          code: `48`,
          name: `Portuguese`,
          en: `Portuguese`,
          es: `Portuguesa`,
        }),
        this.repository.create({
          code: `85`,
          name: `PuertoRican`,
          en: `PuertoRican`,
          es: `Puertorriqueña`,
        }),
        this.repository.create({
          code: `173`,
          name: `Qatari`,
          en: `Qatari`,
          es: `Qatari`,
        }),
        this.repository.create({
          code: `78`,
          name: `Romanian`,
          en: `Romanian`,
          es: `Rumana`,
        }),
        this.repository.create({
          code: `49`,
          name: `Russian`,
          en: `Russian`,
          es: `Rusa`,
        }),
        this.repository.create({
          code: `174`,
          name: `Rwandan`,
          en: `Rwandan`,
          es: `Ruandesa`,
        }),
        this.repository.create({
          code: `175`,
          name: `SaintLucian`,
          en: `SaintLucian`,
          es: `Santa Lucía`,
        }),
        this.repository.create({
          code: `176`,
          name: `Salvadoran`,
          en: `Salvadoran`,
          es: `Salvadoreña`,
        }),
        this.repository.create({
          code: `177`,
          name: `Samoan`,
          en: `Samoan`,
          es: `Samoana`,
        }),
        this.repository.create({
          code: `178`,
          name: `SanMarinese`,
          en: `SanMarinese`,
          es: `Sanmarinense`,
        }),
        this.repository.create({
          code: `179`,
          name: `SaoTomean`,
          en: `SaoTomean`,
          es: `Santotomense`,
        }),
        this.repository.create({
          code: `50`,
          name: `Saudi`,
          en: `Saudi`,
          es: `Saudí`,
        }),
        this.repository.create({
          code: `51`,
          name: `Scottish`,
          en: `Scottish`,
          es: `Escocesa`,
        }),
        this.repository.create({
          code: `180`,
          name: `Senegalese`,
          en: `Senegalese`,
          es: `Senegalesa`,
        }),
        this.repository.create({
          code: `79`,
          name: `Serbian`,
          en: `Serbian`,
          es: `Serbia`,
        }),
        this.repository.create({
          code: `90`,
          name: `Seychellian`,
          en: `Seychellian`,
          es: `Seychelense`,
        }),
        this.repository.create({
          code: `181`,
          name: `SierraLeonean`,
          en: `SierraLeonean`,
          es: `Sierraleonesa`,
        }),
        this.repository.create({
          code: `52`,
          name: `Singaporean`,
          en: `Singaporean`,
          es: `Singapurense`,
        }),
        this.repository.create({
          code: `53`,
          name: `Slovak`,
          en: `Slovak`,
          es: `Eslovaca`,
        }),
        this.repository.create({
          code: `54`,
          name: `Slovenian`,
          en: `Slovenian`,
          es: `Eslovena`,
        }),
        this.repository.create({
          code: `182`,
          name: `SolomonIslander`,
          en: `SolomonIslander`,
          es: `Salomonense`,
        }),
        this.repository.create({
          code: `183`,
          name: `Somali`,
          en: `Somali`,
          es: `Somalí`,
        }),
        this.repository.create({
          code: `55`,
          name: `SouthAfrican`,
          en: `SouthAfrican`,
          es: `Sudafricana`,
        }),
        this.repository.create({
          code: `84`,
          name: `SouthAmerican`,
          en: `SouthAmerican`,
          es: `Sudamericana`,
        }),
        this.repository.create({
          code: `184`,
          name: `SouthKorean`,
          en: `SouthKorean`,
          es: `Surcoreana`,
        }),
        this.repository.create({
          code: `56`,
          name: `Spanish`,
          en: `Spanish`,
          es: `Española`,
        }),
        this.repository.create({
          code: `57`,
          name: `SriLankan`,
          en: `SriLankan`,
          es: `Ceilanesa`,
        }),
        this.repository.create({
          code: `185`,
          name: `Sudanese`,
          en: `Sudanese`,
          es: `Sudanesa`,
        }),
        this.repository.create({
          code: `186`,
          name: `Surinamer`,
          en: `Surinamer`,
          es: `Surinamesa`,
        }),
        this.repository.create({
          code: `187`,
          name: `Swazi`,
          en: `Swazi`,
          es: `Suazi`,
        }),
        this.repository.create({
          code: `58`,
          name: `Swedish`,
          en: `Swedish`,
          es: `Sueca`,
        }),
        this.repository.create({
          code: `59`,
          name: `Swiss`,
          en: `Swiss`,
          es: `Suiza`,
        }),
        this.repository.create({
          code: `188`,
          name: `Syrian`,
          en: `Syrian`,
          es: `Siria`,
        }),
        this.repository.create({
          code: `60`,
          name: `Taiwanese`,
          en: `Taiwanese`,
          es: `Taiwanesa`,
        }),
        this.repository.create({
          code: `189`,
          name: `Tajik`,
          en: `Tajik`,
          es: `Tayika`,
        }),
        this.repository.create({
          code: `190`,
          name: `Tanzanian`,
          en: `Tanzanian`,
          es: `Tanzana`,
        }),
        this.repository.create({
          code: `61`,
          name: `Thai`,
          en: `Thai`,
          es: `Tailandesa`,
        }),
        this.repository.create({
          code: `191`,
          name: `Togolese`,
          en: `Togolese`,
          es: `Togolesa`,
        }),
        this.repository.create({
          code: `192`,
          name: `Tongan`,
          en: `Tongan`,
          es: `Tonga`,
        }),
        this.repository.create({
          code: `193`,
          name: `TrinidadianorTobagonian`,
          en: `TrinidadianorTobagonian`,
          es: `Trinitense o Tobagüense`,
        }),
        this.repository.create({
          code: `80`,
          name: `Tunisian`,
          en: `Tunisian`,
          es: `Tunisina`,
        }),
        this.repository.create({
          code: `62`,
          name: `Turkish`,
          en: `Turkish`,
          es: `Turca`,
        }),
        this.repository.create({
          code: `194`,
          name: `Tuvaluan`,
          en: `Tuvaluan`,
          es: `Tuvaluana`,
        }),
        this.repository.create({
          code: `195`,
          name: `Ugandan`,
          en: `Ugandan`,
          es: `Ugandesa`,
        }),
        this.repository.create({
          code: `81`,
          name: `Ukrainian`,
          en: `Ukrainian`,
          es: `Ucraniana`,
        }),
        this.repository.create({
          code: `196`,
          name: `Uruguayan`,
          en: `Uruguayan`,
          es: `Uruguaya`,
        }),
        this.repository.create({
          code: `197`,
          name: `Uzbekistani`,
          en: `Uzbekistani`,
          es: `Uzbeka`,
        }),
        this.repository.create({
          code: `63`,
          name: `Venezuelan`,
          en: `Venezuelan`,
          es: `Venezolana`,
        }),
        this.repository.create({
          code: `64`,
          name: `Vietnamese`,
          en: `Vietnamese`,
          es: `Vietnamita`,
        }),
        this.repository.create({
          code: `65`,
          name: `Welsh`,
          en: `Welsh`,
          es: `Galesa`,
        }),
        this.repository.create({
          code: `198`,
          name: `Yemenite`,
          en: `Yemenite`,
          es: `Yemení`,
        }),
        this.repository.create({
          code: `199`,
          name: `Zambian`,
          en: `Zambian`,
          es: `Zambiana`,
        }),
        this.repository.create({
          code: `66`,
          name: `Zimbabwean`,
          en: `Zimbabwean`,
          es: `Zimbabuense`,
        }),
      ]);
    }
  }
}
