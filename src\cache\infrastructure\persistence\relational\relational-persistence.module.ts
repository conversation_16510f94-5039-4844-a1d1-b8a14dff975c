import { Module } from '@nestjs/common';
import { CacheDataRepository } from '../cache.repository';
import { CacheDataRelationalRepository } from './repositories/cache.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheDataEntity } from './entities/cache.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CacheDataEntity])],
  providers: [
    {
      provide: CacheDataRepository,
      useClass: CacheDataRelationalRepository,
    },
  ],
  exports: [CacheDataRepository],
})
export class RelationalCacheDataPersistenceModule {}
