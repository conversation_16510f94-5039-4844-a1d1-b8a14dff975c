import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateLanguageTableCrawlerField1752276855198
  implements MigrationInterface
{
  name = 'UpdateLanguageTableCrawlerField1752276855198';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "language" ADD "cwCode" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "language" DROP COLUMN "cwCode"`);
  }
}
