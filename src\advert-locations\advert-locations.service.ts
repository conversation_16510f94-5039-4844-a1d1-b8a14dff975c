import { Injectable } from '@nestjs/common';
import { CreateAdvertLocationDto } from './dto/create-advert-location.dto';
import { UpdateAdvertLocationDto } from './dto/update-advert-location.dto';
import { AdvertLocationRepository } from './infrastructure/persistence/advert-location.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { AdvertLocation } from './domain/advert-location';
import { FindAllAdvertLocationsDto } from './dto/find-all-advert-locations.dto';

@Injectable()
export class AdvertLocationsService {
  constructor(
    private readonly advertLocationRepository: AdvertLocationRepository,
  ) {}

  create(createAdvertLocationDto: CreateAdvertLocationDto) {
    return this.advertLocationRepository.create(createAdvertLocationDto);
  }

  findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllAdvertLocationsDto,
  ) {
    return this.advertLocationRepository.findAllWithPagination(
      {
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      },
      filters,
    );
  }

  findAll() {
    return this.advertLocationRepository.findAll();
  }

  findOne(id: AdvertLocation['id']) {
    return this.advertLocationRepository.findById(id);
  }

  update(
    id: AdvertLocation['id'],
    updateAdvertLocationDto: UpdateAdvertLocationDto,
  ) {
    return this.advertLocationRepository.update(id, updateAdvertLocationDto);
  }

  remove(id: AdvertLocation['id']) {
    return this.advertLocationRepository.remove(id);
  }
}
