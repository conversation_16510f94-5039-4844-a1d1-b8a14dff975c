import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ChestSizeSeedService } from './chest-size-seed.service';
import { ChestSizeEntity } from '../../../../chest-sizes/infrastructure/persistence/relational/entities/chest-size.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ChestSizeEntity])],
  providers: [ChestSizeSeedService],
  exports: [ChestSizeSeedService],
})
export class ChestSizeSeedModule {}
