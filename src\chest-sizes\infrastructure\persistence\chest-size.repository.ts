import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { ChestSize } from '../../domain/chest-size';

export abstract class ChestSizeRepository {
  abstract create(
    data: Omit<ChestSize, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<ChestSize>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<ChestSize[]>;

  abstract findAll(): Promise<ChestSize[]>;

  abstract findById(id: ChestSize['id']): Promise<NullableType<ChestSize>>;

  abstract update(
    id: ChestSize['id'],
    payload: DeepPartial<ChestSize>,
  ): Promise<ChestSize | null>;

  abstract remove(id: ChestSize['id']): Promise<void>;
}
