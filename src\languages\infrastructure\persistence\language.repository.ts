import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { Language } from '../../domain/language';

export abstract class LanguageRepository {
  abstract create(
    data: Omit<Language, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Language>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<Language[]>;

  abstract findAll(): Promise<Language[]>;

  abstract findById(id: Language['id']): Promise<NullableType<Language>>;

  abstract update(
    id: Language['id'],
    payload: DeepPartial<Language>,
  ): Promise<Language | null>;

  abstract remove(id: Language['id']): Promise<void>;
}
