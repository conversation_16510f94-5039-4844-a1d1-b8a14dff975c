import { BaseEntityMapper } from '../../../../../utils/mappers/base-entity-mapper';
import { AdvertType } from '../../../../domain/advert-type';
import { AdvertTypeEntity } from '../entities/advert-type.entity';

export class AdvertTypeMapper {
  private static baseMapper = new (class extends BaseEntityMapper<
    AdvertType,
    AdvertTypeEntity
  > {})();

  static toPersistence(domainEntity: AdvertType): AdvertTypeEntity {
    const persistenceEntity = new AdvertTypeEntity();
    return this.baseMapper.mapToPersistenceCommon(
      domainEntity,
      persistenceEntity,
    );
  }

  static toDomain(raw: AdvertTypeEntity): AdvertType {
    const domainEntity = new AdvertType();
    return this.baseMapper.mapToDomainCommon(raw, domainEntity);
  }
}
