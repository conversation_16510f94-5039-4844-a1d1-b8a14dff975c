import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EyeColorEntity } from '../../../../eye-colors/infrastructure/persistence/relational/entities/eye-color.entity';

@Injectable()
export class EyeColorSeedService {
  constructor(
    @InjectRepository(EyeColorEntity)
    private repository: Repository<EyeColorEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: 'Red' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: 'Other' }),
      ]);
    }
  }
}
