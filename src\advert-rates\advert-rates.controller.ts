import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { AdvertRatesService } from './advert-rates.service';
import { CreateAdvertRateDto } from './dto/create-advert-rate.dto';
import { UpdateAdvertRateDto } from './dto/update-advert-rate.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { AdvertRate } from './domain/advert-rate';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllAdvertRatesDto } from './dto/find-all-advert-rates.dto';
import { plainToInstance } from 'class-transformer';

@ApiTags('AdvertRates')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'advert-rates',
  version: '1',
})
export class AdvertRatesController {
  constructor(private readonly advert_ratesService: AdvertRatesService) {}

  @Post()
  @ApiCreatedResponse({
    type: AdvertRate,
  })
  create(@Body() createAdvertRateDto: any) {
    const data = plainToInstance(CreateAdvertRateDto, createAdvertRateDto);
    return this.advert_ratesService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(AdvertRate),
  })
  async findAll(
    @Query() query: FindAllAdvertRatesDto,
  ): Promise<InfinityPaginationResponseDto<AdvertRate>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.advert_ratesService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: AdvertRate,
  })
  async findAllWithoutPagination(): Promise<AdvertRate[]> {
    return await this.advert_ratesService.findAll();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: string) {
    return this.advert_ratesService.findOne(id);
  }

  @Get('advert/:advertId')
  @ApiParam({
    name: 'advertId',
    type: String,
    required: true,
  })
  findAdvertLinksByAdvert(@Param('advertId') advertId: string) {
    return this.advert_ratesService.findMany(advertId);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: AdvertRate,
  })
  update(
    @Param('id') id: string,
    @Body() updateAdvertRateDto: UpdateAdvertRateDto,
  ) {
    return this.advert_ratesService.update(id, updateAdvertRateDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.advert_ratesService.remove(id);
  }
}
