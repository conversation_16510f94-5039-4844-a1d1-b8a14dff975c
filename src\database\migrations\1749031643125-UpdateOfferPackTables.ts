import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOfferPackTables1749031643125 implements MigrationInterface {
  name = 'UpdateOfferPackTables1749031643125';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "offer_pack" DROP COLUMN "initDate"`);
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ADD "startDate" TIMESTAMP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ADD "qtyType" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ADD "price" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ADD "amount" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ADD "currency" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" ADD "qty" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" ADD "qtyType" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" ADD "duration" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" ADD "durationType" character varying NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" DROP COLUMN "durationType"`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" DROP COLUMN "duration"`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" DROP COLUMN "qtyType"`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" DROP COLUMN "qty"`,
    );
    await queryRunner.query(`ALTER TABLE "offer_pack" DROP COLUMN "currency"`);
    await queryRunner.query(`ALTER TABLE "offer_pack" DROP COLUMN "amount"`);
    await queryRunner.query(`ALTER TABLE "offer_pack" DROP COLUMN "price"`);
    await queryRunner.query(`ALTER TABLE "offer_pack" DROP COLUMN "qtyType"`);
    await queryRunner.query(`ALTER TABLE "offer_pack" DROP COLUMN "startDate"`);
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ADD "initDate" TIMESTAMP NOT NULL`,
    );
  }
}
