import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductDetailEntity } from '../../../../products/infrastructure/persistence/relational/entities/product.entity';

@Injectable()
export class ProductDetailSeedService {
  constructor(
    @InjectRepository(ProductDetailEntity)
    private repository: Repository<ProductDetailEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({
          name: '30 dias',
          qty: 1,
          qtyType: 'ITEMS',
          price: 25,
          amount: 25,
          currency: 'EUR',
          duration: 30,
          durationType: 'DAYS',
          product: {
            id: 1,
          },
        }),
        this.repository.create({
          name: '30 dias',
          qty: 1,
          qtyType: 'ITEMS',
          price: 50,
          amount: 50,
          currency: 'EUR',
          duration: 30,
          durationType: 'DAYS',
          product: {
            id: 2,
          },
        }),
        this.repository.create({
          name: '30 dias',
          qty: 1,
          qtyType: 'ITEMS',
          price: 20,
          amount: 20,
          currency: 'EUR',
          duration: 30,
          durationType: 'DAYS',
          product: {
            id: 11,
          },
        }),
        this.repository.create({
          name: '30 dias',
          qty: 1,
          qtyType: 'ITEMS',
          price: 60,
          amount: 60,
          currency: 'EUR',
          duration: 30,
          durationType: 'DAYS',
          product: {
            id: 12,
          },
        }),
        this.repository.create({
          name: '12 horas',
          qty: 1,
          qtyType: 'ITEMS',
          price: 2,
          amount: 2,
          currency: 'EUR',
          duration: 12,
          durationType: 'HOURS',
          product: {
            id: 50,
          },
        }),
        this.repository.create({
          name: '24 horas',
          qty: 1,
          qtyType: 'ITEMS',
          price: 2,
          amount: 2,
          currency: 'EUR',
          duration: 24,
          durationType: 'HOURS',
          product: {
            id: 60,
          },
        }),
        this.repository.create({
          name: '',
          qty: 1,
          qtyType: 'ITEMS',
          price: 3,
          amount: 3,
          currency: 'EUR',
          duration: 0,
          durationType: 'DAYS',
          product: {
            id: 98,
          },
        }),
        this.repository.create({
          name: 'Recarga 100 Hubs',
          qty: 100,
          qtyType: 'EUR',
          price: 100,
          amount: 80,
          currency: 'EUR',
          duration: 365,
          durationType: 'DAYS',
          product: {
            id: 99,
          },
        }),
        this.repository.create({
          name: 'Recarga 200 Hubs',
          qty: 200,
          qtyType: 'EUR',
          price: 200,
          amount: 150,
          currency: 'EUR',
          duration: 365,
          durationType: 'DAYS',
          product: {
            id: 99,
          },
        }),
        this.repository.create({
          name: 'Recarga 500 Hubs',
          qty: 500,
          qtyType: 'EUR',
          price: 500,
          amount: 300,
          currency: 'EUR',
          duration: 365,
          durationType: 'DAYS',
          product: {
            id: 99,
          },
        }),
        this.repository.create({
          name: 'Recarga 1000 Hubs',
          qty: 1000,
          qtyType: 'EUR',
          price: 1000,
          amount: 700,
          currency: 'EUR',
          duration: 365,
          durationType: 'DAYS',
          product: {
            id: 99,
          },
        }),
      ]);
    }
  }
}
