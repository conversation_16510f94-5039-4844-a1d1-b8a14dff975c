import { Module } from '@nestjs/common';
import { AdvertLocationRepository } from '../advert-location.repository';
import { AdvertLocationRelationalRepository } from './repositories/advert-location.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertLocationEntity } from './entities/advert-location.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertLocationEntity])],
  providers: [
    {
      provide: AdvertLocationRepository,
      useClass: AdvertLocationRelationalRepository,
    },
  ],
  exports: [AdvertLocationRepository],
})
export class RelationalAdvertLocationPersistenceModule {}
