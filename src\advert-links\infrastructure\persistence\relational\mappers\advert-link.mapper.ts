import { AdvertLink } from '../../../../domain/advert-link';
import { AdvertLinkEntity } from '../entities/advert-link.entity';

export class AdvertLinkMapper {
  static toDomain(raw: AdvertLinkEntity): AdvertLink {
    const domainEntity = new AdvertLink();
    domainEntity.id = raw.id;
    domainEntity.name = raw.name;
    domainEntity.advert = raw.advert;
    domainEntity.rrssType = raw.rrssType;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: AdvertLink): AdvertLinkEntity {
    const persistenceEntity = new AdvertLinkEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.name = domainEntity.name;
    persistenceEntity.advert = domainEntity.advert;
    persistenceEntity.rrssType = domainEntity.rrssType;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
