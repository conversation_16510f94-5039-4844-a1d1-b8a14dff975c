import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TransactionStatusEntity } from '../../../../transactionstatuses/infrastructure/persistence/relational/entities/transactionstatus.entity';
import { TransactionState } from '../../../../transactionstatuses/domain/transactionstatus';

@Injectable()
export class TransactionStatusSeedService {
  constructor(
    @InjectRepository(TransactionStatusEntity)
    private repository: Repository<TransactionStatusEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({
          id: 1,
          name: 'pending',
          description: 'pending',
          state: TransactionState.PENDING,
        }),
        this.repository.create({
          id: 2,
          name: 'completed',
          description: 'completed',
          state: TransactionState.COMPLETED,
        }),
        this.repository.create({
          id: 3,
          name: 'failed',
          description: 'failed',
          state: TransactionState.FAILED,
        }),
      ]);
    }
  }
}
