import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';

export class Neighborhood {
  @ApiResponseProperty({
    type: Number,
  })
  id: number;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  name: string;

  @ApiResponseProperty({
    type: String,
  })
  slug: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
