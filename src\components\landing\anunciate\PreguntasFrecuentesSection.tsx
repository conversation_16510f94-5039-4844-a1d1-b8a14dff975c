'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

const FaqItem = ({
  question,
  answer,
  isOpen,
  onClick,
}: {
  question: string;
  answer: string;
  isOpen: boolean;
  onClick: () => void;
}) => (
  <div className="border-b border-gray-200 py-4 md:py-6">
    <button
      className="w-full flex justify-between items-center text-left"
      onClick={onClick}
    >
      <h3 className="text-lg md:text-xl font-medium text-gray-800">
        {question}
      </h3>
      <span className="text-xl md:text-2xl text-gray-500">
        {isOpen ? '-' : '+'}
      </span>
    </button>
    {isOpen && (
      <div
        className="mt-4 text-gray-600"
        dangerouslySetInnerHTML={{ __html: answer }}
      />
    )}
  </div>
);

export default function PreguntasFrecuentesSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const t = useTranslations('anunciate.faq');

  const faqs = t.raw('faqs') as { question: string; answer: string }[];

  const handleToggle = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="bg-gray-50 pt-20 md:pt-40 pb-16">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="max-w-2xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-semibold text-gray-800">
            {t('title')}
          </h2>
        </div>
        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <FaqItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isOpen={openIndex === index}
              onClick={() => handleToggle(index)}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
