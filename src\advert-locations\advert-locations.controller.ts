import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { AdvertLocationsService } from './advert-locations.service';
import { CreateAdvertLocationDto } from './dto/create-advert-location.dto';
import { UpdateAdvertLocationDto } from './dto/update-advert-location.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { AdvertLocation } from './domain/advert-location';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllAdvertLocationsDto } from './dto/find-all-advert-locations.dto';
import { plainToInstance } from 'class-transformer';

@ApiTags('AdvertLocations')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'advert-locations',
  version: '1',
})
export class AdvertLocationsController {
  constructor(
    private readonly advertLocationsService: AdvertLocationsService,
  ) {}

  @Post()
  @ApiCreatedResponse({
    type: AdvertLocation,
  })
  create(@Body() createAdvertLocationDto: any) {
    const data = plainToInstance(
      CreateAdvertLocationDto,
      createAdvertLocationDto,
    );
    return this.advertLocationsService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(AdvertLocation),
  })
  async findAll(
    @Query() query: FindAllAdvertLocationsDto,
  ): Promise<InfinityPaginationResponseDto<AdvertLocation>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.advertLocationsService.findAllWithPagination(
        {
          paginationOptions: {
            page,
            limit,
          },
        },
        query,
      ),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: AdvertLocation,
  })
  async findAllWithoutPagination(): Promise<AdvertLocation[]> {
    return await this.advertLocationsService.findAll();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.advertLocationsService.findOne(id);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: AdvertLocation,
  })
  update(@Param('id') id: number, @Body() updateAdvertLocationDto: any) {
    const data = plainToInstance(
      UpdateAdvertLocationDto,
      updateAdvertLocationDto,
    );
    return this.advertLocationsService.update(id, data);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.advertLocationsService.remove(id);
  }
}
