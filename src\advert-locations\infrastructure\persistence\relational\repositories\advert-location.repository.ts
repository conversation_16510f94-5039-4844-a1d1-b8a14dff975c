import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertLocationEntity } from '../entities/advert-location.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { AdvertLocation } from '../../../../domain/advert-location';
import { AdvertLocationRepository } from '../../advert-location.repository';
import { AdvertLocationMapper } from '../mappers/advert-location.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';
import {
  FindAllAdvertLocationsDto,
  DateFields,
  NumberFields,
  RelationalFields,
  StringFields,
} from '../../../../dto/find-all-advert-locations.dto';

@Injectable()
export class AdvertLocationRelationalRepository
  implements AdvertLocationRepository
{
  constructor(
    @InjectRepository(AdvertLocationEntity)
    private readonly advertLocationRepository: Repository<AdvertLocationEntity>,
  ) {}

  async create(data: AdvertLocation): Promise<AdvertLocation> {
    const persistenceModel = AdvertLocationMapper.toPersistence(data);
    const newEntity = await this.advertLocationRepository.save(
      this.advertLocationRepository.create(persistenceModel),
    );
    return AdvertLocationMapper.toDomain(newEntity);
  }

  async findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllAdvertLocationsDto,
  ): Promise<AdvertLocation[]> {
    const query =
      this.advertLocationRepository.createQueryBuilder('advert_location');
    query.leftJoinAndSelect('advert_location.country', 'country');
    query.leftJoinAndSelect('advert_location.region', 'region');
    query.leftJoinAndSelect('advert_location.city', 'city');
    query.leftJoinAndSelect('advert_location.neighborhood', 'neighborhood');

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (RelationalFields.includes(key)) {
            query.andWhere(`${key}.id = :${key}`, { [key]: value });
          } else if (StringFields.includes(key)) {
            query.andWhere(
              `LOWER(advert_location.${key}) LIKE LOWER(:${key})`,
              {
                [key]: `%${value}%`,
              },
            );
          } else if (NumberFields.includes(key)) {
            query.andWhere(`advert_location.${key} = :${key}`, {
              [key]: Number(value),
            });
          } else if (DateFields.includes(key)) {
            query.andWhere(`advert_location.${key} = :${key}`, {
              [key]: new Date(value),
            });
          }
        }
      });
    }
    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);

    const entities = await query.getMany();

    return entities.map((entity) => AdvertLocationMapper.toDomain(entity));
  }

  async findAll(): Promise<AdvertLocation[]> {
    const entities = await this.advertLocationRepository.find({
      relations: ['country', 'region', 'city', 'neighborhood'],
    });

    return entities.map((advertLocation) =>
      AdvertLocationMapper.toDomain(advertLocation),
    );
  }

  async findById(
    id: AdvertLocation['id'],
  ): Promise<NullableType<AdvertLocation>> {
    const entity = await this.advertLocationRepository.findOne({
      where: { id },
      relations: ['country', 'region', 'city', 'neighborhood'],
    });

    return entity ? AdvertLocationMapper.toDomain(entity) : null;
  }

  async update(
    id: AdvertLocation['id'],
    payload: Partial<AdvertLocation>,
  ): Promise<AdvertLocation> {
    const entity = await this.advertLocationRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.advertLocationRepository.save(
      this.advertLocationRepository.create(
        AdvertLocationMapper.toPersistence({
          ...AdvertLocationMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return AdvertLocationMapper.toDomain(updatedEntity);
  }

  async remove(id: AdvertLocation['id']): Promise<void> {
    await this.advertLocationRepository.softDelete(id);
  }
}
