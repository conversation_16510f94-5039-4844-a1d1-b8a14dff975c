'use client';

import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { InputMask } from '@react-input/mask';
import React, { useState } from 'react';
import type { RegisterOptions, UseFormRegister } from 'react-hook-form';

import FieldError from './FieldError';

type AttributeProps = React.DetailedHTMLProps<
  React.InputHTMLAttributes<HTMLInputElement>,
  HTMLInputElement
>;

type typeInput = 'text' | 'password' | 'number' | 'email' | '';

type Props = {
  id: string;
  label: string;
  type?: typeInput;
  name?: string;
  classAdditional?: string;
  register?: UseFormRegister<any>;
  error?: string | undefined;
  rules?: RegisterOptions;
  isRequired?: boolean;
  withIcon?: boolean;
  withMask?: boolean;
  mask?: string;
} & AttributeProps;

const FieldInput = ({
  label,
  id,
  isRequired,
  classAdditional,
  type = 'text',
  name,
  rules,
  error,
  register,
  mask,
  withMask,
  withIcon,
  ...props
}: Props) => {
  const [currentType, setCurrentType] = useState<typeInput>(type);

  const handlePassword = () => {
    const typeIn = currentType === 'password' ? 'text' : 'password';
    setCurrentType(typeIn);
  };

  return (
    <div
      className={`${classAdditional || ''} flex w-full flex-col items-center justify-start`}
    >
      <div className="relative w-full">
        <label className={`label-primary dark:text-white`} htmlFor={id}>
          {label}
          {isRequired && <b className="text-[#f15c5c] ">*</b>}
        </label>
        {withMask ? (
          <InputMask
            mask={mask}
            replacement={{ _: /\d/ }}
            id={name ?? id}
            required={isRequired}
            type={currentType}
            autoComplete="current-password"
            className={`${
              id === 'password' || id === 'confirmPassword' ? 'pr-10' : 'pr-4'
            } ${withIcon ? 'pl-10' : ''} custom-input dark:text-white ${error ? 'border-[#c13515] bg-[#FEF8F6]' : 'border-gray-300'}`}
            {...(register && register(name as string, rules))}
            name={name}
            suppressHydrationWarning
            {...props}
          />
        ) : (
          <input
            id={name ?? id}
            required={isRequired}
            autoComplete="current-password"
            className={`${
              id === 'password' || id === 'confirmPassword' ? 'pr-10' : 'pr-4'
            } ${withIcon ? 'pl-10' : ''} custom-input dark:text-white ${error ? 'border-[#c13515] bg-[#FEF8F6]' : 'border-gray-300'}`}
            {...(register && register(name as string, rules))}
            name={name}
            suppressHydrationWarning
            {...props}
            type={currentType}
          />
        )}
        {id === 'password' || id === 'confirmPassword' ? (
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-lg transition-all  hover:scale-110 active:scale-95"
            onClick={handlePassword}
          >
            {currentType === type ? <EyeOutlined /> : <EyeInvisibleOutlined />}
          </button>
        ) : null}
      </div>
      <FieldError error={error} />
    </div>
  );
};

export default FieldInput;
