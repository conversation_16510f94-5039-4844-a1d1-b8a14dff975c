import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { Country } from '../../domain/country';
import { FindAllCountriesDto } from '../../dto/find-all-countries.dto';

export abstract class CountryRepository {
  abstract create(
    data: Omit<Country, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Country>;

  abstract findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCountriesDto,
  ): Promise<Country[]>;

  abstract findAll(): Promise<Country[]>;

  abstract findAllAndCount(): Promise<any>;

  abstract findById(id: Country['id']): Promise<NullableType<Country>>;

  abstract update(
    id: Country['id'],
    payload: DeepPartial<Country>,
  ): Promise<Country | null>;

  abstract remove(id: Country['id']): Promise<void>;
}
