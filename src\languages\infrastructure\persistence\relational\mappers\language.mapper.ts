import { Language } from '../../../../domain/language';
import { LanguageEntity } from '../entities/language.entity';

export class LanguageMapper {
  static toDomain(raw: LanguageEntity): Language {
    const domainEntity = new Language();
    domainEntity.id = raw.id;
    domainEntity.code = raw.code;
    domainEntity.name = raw.name;
    domainEntity.cwCode = raw.cwCode;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: Language): LanguageEntity {
    const persistenceEntity = new LanguageEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.code = domainEntity.code;
    persistenceEntity.name = domainEntity.name;
    persistenceEntity.cwCode = domainEntity.cwCode;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
