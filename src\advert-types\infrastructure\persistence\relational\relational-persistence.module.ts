import { Module } from '@nestjs/common';
import { AdvertTypeRepository } from '../advert-type.repository';
import { AdvertTypeRelationalRepository } from './repositories/advert-type.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertTypeEntity } from './entities/advert-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertTypeEntity])],
  providers: [
    {
      provide: AdvertTypeRepository,
      useClass: AdvertTypeRelationalRepository,
    },
  ],
  exports: [AdvertTypeRepository],
})
export class RelationalAdvertTypePersistenceModule {}
