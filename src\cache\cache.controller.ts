import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { CacheDataService } from './cache.service';
import { CreateCacheDataDto } from './dto/create-cache.dto';
import { UpdateCacheDataDto } from './dto/update-cache.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { CacheData } from './domain/cache';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllCacheDataDto } from './dto/find-all-caches.dto';
import { plainToInstance } from 'class-transformer';

@ApiTags('Cache')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'cache',
  version: '1',
})
export class CacheDataController {
  constructor(private readonly cacheDataService: CacheDataService) {}

  @Post()
  @ApiCreatedResponse({ type: CacheData })
  create(@Body() createCacheDataDto: any) {
    const data = plainToInstance(CreateCacheDataDto, createCacheDataDto);
    return this.cacheDataService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(CacheData),
  })
  async findAll(
    @Query() query: FindAllCacheDataDto,
  ): Promise<InfinityPaginationResponseDto<CacheData>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    const result = await this.cacheDataService.findAllWithPagination(
      {
        paginationOptions: {
          page,
          limit,
        },
      },
      query,
    );

    return infinityPagination(result.data, { page, limit }, result.totalCount);
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: string) {
    return this.cacheDataService.findOne(id);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: CacheData,
  })
  update(
    @Param('id') id: string,
    @Body() updateCacheDataDto: UpdateCacheDataDto,
  ) {
    return this.cacheDataService.update(id, updateCacheDataDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.cacheDataService.remove(id);
  }
}
