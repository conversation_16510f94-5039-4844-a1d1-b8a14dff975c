import { Inject, Injectable } from '@nestjs/common';
import { CreateHairColorDto } from './dto/create-hair-color.dto';
import { UpdateHairColorDto } from './dto/update-hair-color.dto';
import { HairColorRepository } from './infrastructure/persistence/hair-color.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { HairColor } from './domain/hair-color';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class HairColorsService {
  constructor(
    private readonly hair_colorRepository: HairColorRepository,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createHairColorDto: CreateHairColorDto) {
    return this.hair_colorRepository.create(createHairColorDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.hair_colorRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  async findAll() {
    const _key = 'CACHE-HAIR-COLORS-ALL';
    const cachedData = await this.cacheService.get<{ name: string }>(_key);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.hair_colorRepository.findAll();
    if (data) {
      return await this.cacheService.set(_key, data);
    }
  }

  findOne(id: HairColor['id']) {
    return this.hair_colorRepository.findById(id);
  }

  update(id: HairColor['id'], updateHairColorDto: UpdateHairColorDto) {
    return this.hair_colorRepository.update(id, updateHairColorDto);
  }

  remove(id: HairColor['id']) {
    return this.hair_colorRepository.remove(id);
  }
}
