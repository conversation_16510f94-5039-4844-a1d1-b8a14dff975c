'use client';

import React from 'react';

export const ProfessionalIcon = ({
  selected: _selected,
}: {
  selected: boolean;
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className={`h-10 w-10 transition-colors duration-300 text-[#F15C5C]`}
    viewBox="0 0 20 20"
    fill="currentColor"
  >
    <path
      fillRule="evenodd"
      d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
      clipRule="evenodd"
    />
  </svg>
);

export const ClientIcon = ({ selected: _selected }: { selected: boolean }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className={`h-10 w-10 transition-colors duration-300 text-[#F15C5C]`}
    viewBox="0 0 20 20"
    fill="currentColor"
  >
    <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
    <path d="M14.553 4.33A2 2 0 0116 6v6a2 2 0 01-2 2h-1.447A4.5 4.5 0 0014.553 4.33z" />
  </svg>
);

interface RoleCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  selected: boolean;
  onClick: () => void;
}

export const RoleCard = ({
  icon,
  title,
  description,
  selected,
  onClick,
}: RoleCardProps) => (
  <button
    onClick={onClick}
    className={`w-full p-4 sm:p-6 text-left sm:text-center border rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#F15C5C] ${
      selected
        ? 'bg-red-50 border-[#F15C5C] ring-2 ring-[#F15C5C]'
        : 'bg-white text-gray-800 border-gray-200 hover:border-red-300'
    }`}
  >
    <div className="flex flex-row sm:flex-col items-center gap-4 sm:gap-2">
      {icon}
      <div className="flex-grow">
        <h3 className={`font-semibold text-gray-800`}>{title}</h3>
        <p className={`text-sm mt-1 text-gray-500`}>{description}</p>
      </div>
    </div>
  </button>
);
