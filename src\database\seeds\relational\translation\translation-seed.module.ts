import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { TranslationSeedService } from './translation-seed.service';
import { TranslationEntity } from '../../../../translations/infrastructure/persistence/relational/entities/translation.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TranslationEntity])],
  providers: [TranslationSeedService],
  exports: [TranslationSeedService],
})
export class TranslationSeedModule {}
