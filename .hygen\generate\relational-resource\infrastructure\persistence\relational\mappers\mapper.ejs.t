---
to: src/<%= h.inflection.transform(name, ['pluralize', 'underscore', 'dasherize']) %>/infrastructure/persistence/relational/mappers/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.mapper.ts
---
import { BaseEntityMapper } from '../../../../../utils/mappers/base-entity-mapper';
import { <%= name %> } from '../../../../domain/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>';
import { <%= name %>Entity } from '../entities/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.entity';

export class <%= name %>Mapper {

  private static baseMapper = new (class extends BaseEntityMapper<
    <%= name %>,
    <%= name %>Entity
  > {})();

  static toPersistence(domainEntity: <%= name %>): <%= name %>Entity {
    const persistenceEntity = new <%= name %>Entity();

    this.baseMapper.mapToPersistenceCommon(domainEntity, persistenceEntity);

    // ----- MORE FIELDS HERE -----

    persistenceEntity.description = domainEntity.description;

    // ----------------------------

    return persistenceEntity;
  }

  static toDomain(raw: <%= name %>Entity): <%= name %> {
    const domainEntity = new <%= name %>();

    this.baseMapper.mapToDomainCommon(raw, domainEntity);

    // ----- MORE FIELDS HERE -----

    domainEntity.description = raw. description;

    // ----------------------------

    return domainEntity;
  }
}
