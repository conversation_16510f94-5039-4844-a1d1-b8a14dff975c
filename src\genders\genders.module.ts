import { Module } from '@nestjs/common';
import { GendersService } from './genders.service';
import { GendersController } from './genders.controller';
import { RelationalGenderPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [RelationalGenderPersistenceModule],
  controllers: [GendersController],
  providers: [GendersService],
  exports: [GendersService, RelationalGenderPersistenceModule],
})
export class GendersModule {}
