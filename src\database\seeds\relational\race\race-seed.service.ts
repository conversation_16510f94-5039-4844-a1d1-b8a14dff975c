import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RaceEntity } from '../../../../races/infrastructure/persistence/relational/entities/race.entity';

@Injectable()
export class RaceSeedService {
  constructor(
    @InjectRepository(RaceEntity)
    private repository: Repository<RaceEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ name: 'European' }),
        this.repository.create({ name: 'Asian' }),
        this.repository.create({ name: 'Latin' }),
        this.repository.create({ name: 'African' }),
        this.repository.create({ name: 'Arab' }),
        this.repository.create({ name: 'Native' }),
        this.repository.create({ name: 'Multiracial' }),
        this.repository.create({ name: 'Other' }),
      ]);
    }
  }
}
