import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { CityEntity } from '../infraestructure/persistence/relational/entities/city.entity';

export class CreateNeighborhoodDto {
  @ApiProperty({ type: String })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  slug: string;

  @ApiResponseProperty({ type: CityEntity })
  city: CityEntity;
}
