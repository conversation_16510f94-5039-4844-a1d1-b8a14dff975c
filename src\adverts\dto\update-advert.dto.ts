import { PartialType } from '@nestjs/swagger';
import { CreateAdvertDto } from './create-advert.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Gender } from '../../genders/domain/gender';
import { User } from '../../users/domain/user';
import { AdvertType } from '../../advert-types/domain/advert-type';
import { Nationality } from '../../nationalities/domain/nationality';
import { ChestSize } from '../../chest-sizes/domain/chest-size';
import { Service } from '../../services/domain/service';
import { Language } from '../../languages/domain/language';
import { AdvertStatus } from '../../advert-statuses/domain/advert-status';
import { Orientation } from '../../orientations/domain/orientation';
import { EyeColor } from '../../eye-colors/domain/eye-color';
import { HairColor } from '../../hair-colors/domain/hair-color';
import { Race } from '../../races/domain/race';
import { AdvertFile } from '../../advert-files/domain/advert-file';
import { AdvertLink } from '../../advert-links/domain/advert-link';
import { AdvertRate } from '../../advert-rates/domain/advert-rate';
import { IsNotEmpty } from 'class-validator';
import { AdvertLocation } from '../../advert-locations/domain/advert-location';

export class UpdateAdvertDto extends PartialType(CreateAdvertDto) {
  @ApiProperty({ type: String })
  @IsNotEmpty()
  profileName: string;

  @ApiProperty({ type: String })
  slug: string;

  @ApiProperty({ type: String })
  phoneNumber: string;

  @ApiProperty({ type: Boolean })
  whatsapp: boolean;

  @ApiProperty({ type: String })
  introduction: string;

  @ApiProperty({ type: Gender })
  gender: Gender;

  @ApiProperty({ type: AdvertType })
  advertType: AdvertType;

  @ApiProperty({ type: Number })
  birthDate: number;

  @ApiProperty({ type: Number })
  height: number;

  @ApiProperty({ type: Number })
  breastSize: number;

  @ApiProperty({ type: ChestSize })
  chestSize: ChestSize;

  @ApiProperty({ type: Number })
  waist: number; /* cintura */

  @ApiProperty({ type: Number })
  hips: number;

  @ApiProperty({ type: Orientation })
  orientation: Orientation;

  @ApiProperty({ type: EyeColor })
  eyeColor: EyeColor;

  @ApiProperty({ type: HairColor })
  hairColor: HairColor;

  @ApiProperty({ type: Race })
  race: Race;

  @ApiProperty({ type: Boolean })
  smoke: boolean;

  @ApiProperty({ type: String })
  location: string;

  @ApiProperty({ type: String })
  email: string;

  @ApiProperty({ type: String })
  www: string;

  @ApiProperty({ type: String })
  timetable: string;

  @ApiProperty({ type: Nationality })
  nationality: Nationality;

  @ApiProperty({ type: Service })
  services: Service[];

  @ApiProperty({ type: Language })
  languages: Language[];

  @ApiProperty({ type: AdvertStatus })
  status: AdvertStatus;

  @ApiProperty({ type: Boolean })
  featured: boolean;

  @ApiProperty({ type: Number })
  rating: number;

  @ApiProperty({ type: User })
  user: User;

  @ApiProperty({ type: AdvertFile })
  advertFiles: AdvertFile[];

  @ApiProperty({ type: AdvertLink })
  advertLinks: AdvertLink[];

  @ApiProperty({ type: AdvertRate })
  advertRates: AdvertRate[];

  @ApiProperty({ type: AdvertLocation })
  advertLocations: AdvertLocation[];

  @ApiProperty({ type: Boolean })
  oTopAdvert: boolean;

  @ApiProperty({ type: Boolean })
  oTopDoubleAdvert: boolean;

  @ApiProperty({ type: Boolean })
  oDoubleAdvert: boolean;

  @ApiProperty({ type: Boolean })
  oAvailableNow: boolean;

  @ApiProperty({ type: Boolean })
  oReactivate: boolean;

  @ApiProperty({ type: Boolean })
  oTopStories: boolean;
}
