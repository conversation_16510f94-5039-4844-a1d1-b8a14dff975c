import { Inject, Injectable } from '@nestjs/common';
import { RegionRepository } from '../infraestructure/persistence/region.repository';
import { CreateRegionDto } from '../dto/create-region.dto';
import { IPaginationOptions } from '../../utils/types/pagination-options';
import { Region } from '../domain/region';
import { UpdateRegionDto } from '../dto/update-region.dto';
import { FindAllRegionsDto } from '../dto/find-all-regions.dto';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheServiceHelper } from '../../helpers/cache/cache.service';

@Injectable()
export class RegionsService {
  constructor(
    private readonly regionRepository: RegionRepository,
    private readonly cacheServiceHelper: CacheServiceHelper,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createRegionDto: CreateRegionDto) {
    return this.regionRepository.create(createRegionDto);
  }

  findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllRegionsDto,
  ) {
    return this.regionRepository.findAllWithPagination(
      {
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      },
      filters,
    );
  }

  findAll() {
    return this.regionRepository.findAll();
  }

  async findAllAndCount() {
    const _cacheKey = 'CACHE-REGIONS-ALLCOUNT';
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'regionsService.findAllAndCount',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.regionRepository.findAllAndCount();
    if (data) {
      await this.cacheService.set(_cacheKey, data);
      return data;
    }
  }

  findOne(id: Region['id']) {
    return this.regionRepository.findById(id);
  }

  update(id: Region['id'], updateRegionDto: UpdateRegionDto) {
    return this.regionRepository.update(id, updateRegionDto);
  }

  remove(id: Region['id']) {
    return this.regionRepository.remove(id);
  }
}
