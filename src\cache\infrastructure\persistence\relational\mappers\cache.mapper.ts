import { CacheData } from '../../../../domain/cache';
import { CacheDataEntity } from '../entities/cache.entity';

export class CacheDataMapper {
  static toDomain(raw: CacheDataEntity): CacheData {
    const domainEntity = new CacheData();
    domainEntity.id = raw.id;
    domainEntity.cacheDate = raw.cacheDate;
    domainEntity.description = raw.description;
    domainEntity.cacheKey = raw.cacheKey;
    domainEntity.user = raw.user;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: CacheData): CacheDataEntity {
    const persistenceEntity = new CacheDataEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.cacheDate = domainEntity.cacheDate;
    persistenceEntity.description = domainEntity.description;
    persistenceEntity.cacheKey = domainEntity.cacheKey;
    persistenceEntity.user = domainEntity.user;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
