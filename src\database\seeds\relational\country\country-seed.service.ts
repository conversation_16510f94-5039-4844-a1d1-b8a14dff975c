import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CountryEntity } from '../../../../location/infraestructure/persistence/relational/entities/country.entity';

@Injectable()
export class CountrySeedService {
  constructor(
    @InjectRepository(CountryEntity)
    private repository: Repository<CountryEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({
          id: 1,
          name: 'España',
          code: 'ES',
          slug: 'espana',
        }),
        this.repository.create({
          id: 99,
          name: 'all',
          code: '...',
          slug: 'all',
        }),
      ]);
    }
  }
}
