import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CacheDataEntity } from '../entities/cache.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { CacheData } from '../../../../domain/cache';
import { CacheDataRepository } from '../../cache.repository';
import { CacheDataMapper } from '../mappers/cache.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';
import {
  FindAllCacheDataDto,
  <PERSON><PERSON><PERSON>Fields,
  NumberFields,
  RelationalFields,
  StringFields,
} from '../../../../dto/find-all-caches.dto';

@Injectable()
export class CacheDataRelationalRepository implements CacheDataRepository {
  constructor(
    @InjectRepository(CacheDataEntity)
    private readonly repository: Repository<CacheDataEntity>,
  ) {}

  async create(data: CacheData): Promise<CacheData> {
    const persistenceModel = CacheDataMapper.toPersistence(data);
    const newEntity = await this.repository.save(
      this.repository.create(persistenceModel),
    );
    return CacheDataMapper.toDomain(newEntity);
  }

  async findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCacheDataDto,
  ) {
    const query = this.repository.createQueryBuilder('cache');
    query.leftJoinAndSelect('cache.user', 'user');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page, limit, ...updatedDto } = filters;
    if (updatedDto) {
      Object.entries(updatedDto).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (RelationalFields.includes(key)) {
            query.andWhere(`${key}.id = :${key}`, { [key]: value });
          } else if (StringFields.includes(key)) {
            query.andWhere(`LOWER(cache.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          } else if (NumberFields.includes(key)) {
            query.andWhere(`cache.${key} = :${key}`, {
              [key]: Number(value),
            });
          } else if (BooleanFields.includes(key)) {
            query.andWhere(`cache.${key} = :${key}`, {
              [key]: value,
            });
          }
        }
      });
    }

    const [, totalCount] = await query.getManyAndCount();

    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);
    query.addOrderBy('cache.cacheDate', 'DESC');

    const results = await query.getMany();

    return {
      data: results.map((result) => CacheDataMapper.toDomain(result)),
      totalCount: totalCount,
    };
  }

  async findById(id: CacheData['id']): Promise<NullableType<CacheData>> {
    const entity = await this.repository.findOne({
      where: { id },
    });

    return entity ? CacheDataMapper.toDomain(entity) : null;
  }

  async findAllByUser(
    userId: string,
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
  ) {
    const query = this.repository.createQueryBuilder('cache');
    query.leftJoinAndSelect('cache.user', 'user');
    query.andWhere(`user.id = :userId`, { userId: userId });

    const [, totalCount] = await query.getManyAndCount();

    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);
    query.addOrderBy('cache.cacheDate', 'DESC');

    const results = await query.getMany();

    return {
      data: results.map((result) => CacheDataMapper.toDomain(result)),
      totalCount: totalCount,
    };
  }

  async update(
    id: CacheData['id'],
    payload: Partial<CacheData>,
  ): Promise<CacheData> {
    const entity = await this.repository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.repository.save(
      this.repository.create(
        CacheDataMapper.toPersistence({
          ...CacheDataMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return CacheDataMapper.toDomain(updatedEntity);
  }

  async remove(id: CacheData['id']): Promise<void> {
    await this.repository.softDelete(id);
  }
}
