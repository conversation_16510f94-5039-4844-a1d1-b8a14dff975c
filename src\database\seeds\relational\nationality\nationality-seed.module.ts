import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { NationalitySeedService } from './nationality-seed.service';
import { NationalityEntity } from '../../../../nationalities/infrastructure/persistence/relational/entities/nationality.entity';

@Module({
  imports: [TypeOrmModule.forFeature([NationalityEntity])],
  providers: [NationalitySeedService],
  exports: [NationalitySeedService],
})
export class NationalitySeedModule {}
