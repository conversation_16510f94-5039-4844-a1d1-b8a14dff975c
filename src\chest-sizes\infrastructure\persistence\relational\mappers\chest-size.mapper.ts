import { BaseEntityMapper } from '../../../../../utils/mappers/base-entity-mapper';
import { ChestSize } from '../../../../domain/chest-size';
import { ChestSizeEntity } from '../entities/chest-size.entity';

export class ChestSizeMapper {
  private static baseMapper = new (class extends BaseEntityMapper<
    ChestSize,
    ChestSizeEntity
  > {})();

  static toPersistence(domainEntity: ChestSize): ChestSizeEntity {
    const persistenceEntity = new ChestSizeEntity();
    return this.baseMapper.mapToPersistenceCommon(
      domainEntity,
      persistenceEntity,
    );
  }

  static toDomain(raw: ChestSizeEntity): ChestSize {
    const domainEntity = new ChestSize();
    return this.baseMapper.mapToDomainCommon(raw, domainEntity);
  }
}
