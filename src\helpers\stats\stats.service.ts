import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';

@Injectable()
export class StatsService {
  constructor(private readonly datasource: DataSource) {}

  addAdvertVisit(type: string, advertSlug: string) {
    // TYPES
    // VISIT
    // WHATSAPP
    // SHOWPHONE
    const query = `UPDATE advert SET "rating" = "rating" + 1 WHERE "slug" = '${advertSlug}'`;
    void this.datasource.query(query);
  }
}
