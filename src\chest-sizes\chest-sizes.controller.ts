import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { ChestSizesService } from './chest-sizes.service';
import { CreateChestSizeDto } from './dto/create-chest-size.dto';
import { UpdateChestSizeDto } from './dto/update-chest-size.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { ChestSize } from './domain/chest-size';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllChestSizesDto } from './dto/find-all-chest-sizes.dto';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiTags('ChestSizes')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'chest-sizes',
  version: '1',
})
export class ChestSizesController {
  constructor(private readonly chest_sizesService: ChestSizesService) {}

  @Post()
  @ApiCreatedResponse({
    type: ChestSize,
  })
  create(@Body() createChestSizeDto: CreateChestSizeDto) {
    return this.chest_sizesService.create(createChestSizeDto);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(ChestSize),
  })
  async findAll(
    @Query() query: FindAllChestSizesDto,
  ): Promise<InfinityPaginationResponseDto<ChestSize>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.chest_sizesService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('all')
  @ApiOkResponse({
    type: ChestSize,
  })
  async findAllWithoutPagination() {
    return await this.chest_sizesService.findAll();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.chest_sizesService.findOne(id);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: ChestSize,
  })
  update(
    @Param('id') id: number,
    @Body() updateChestSizeDto: UpdateChestSizeDto,
  ) {
    return this.chest_sizesService.update(id, updateChestSizeDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.chest_sizesService.remove(id);
  }
}
