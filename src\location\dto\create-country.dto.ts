import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateCountryDto {
  @ApiProperty({ type: Number })
  @IsOptional()
  id: number;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: '', type: String })
  @IsNotEmpty()
  code: string;

  @ApiProperty({ example: '', type: String })
  @IsNotEmpty()
  slug: string;
}
