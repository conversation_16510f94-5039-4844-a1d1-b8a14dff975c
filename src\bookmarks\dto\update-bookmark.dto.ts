import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateBookmarkDto } from './create-bookmark.dto';
import { User } from '../../users/domain/user';
import { Advert } from '../../adverts/domain/advert';

export class UpdateBookmarkDto extends PartialType(CreateBookmarkDto) {
  @ApiProperty({
    type: String,
  })
  name: string;

  @ApiProperty({
    type: Advert,
  })
  advert: Advert;

  @ApiProperty({
    type: User,
  })
  user: User;
}
