import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { CacheData } from '../../domain/cache';
import { FindAllCacheDataDto } from '../../dto/find-all-caches.dto';

export abstract class CacheDataRepository {
  abstract create(
    data: Omit<CacheData, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<CacheData>;

  abstract findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCacheDataDto,
  );

  abstract findById(id: CacheData['id']): Promise<NullableType<CacheData>>;

  abstract findAllByUser(
    userId: string,
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCacheDataDto,
  );

  abstract update(
    id: CacheData['id'],
    payload: DeepPartial<CacheData>,
  ): Promise<CacheData | null>;

  abstract remove(id: CacheData['id']): Promise<void>;
}
