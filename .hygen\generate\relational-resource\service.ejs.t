---
to: src/<%= h.inflection.transform(name, ['pluralize', 'underscore', 'dasherize']) %>/<%= h.inflection.transform(name, ['pluralize', 'underscore', 'dasherize']) %>.service.ts
---
import { Inject, Injectable } from '@nestjs/common';
import { Create<%= name %>Dto } from './dto/create-<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.dto';
import { Update<%= name %>Dto } from './dto/update-<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.dto';
import { <%= name %>Repository } from './infrastructure/persistence/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.repository';
import { <%= name %> } from './domain/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>';
import { FindAllDto } from './dto/find-all-<%= h.inflection.transform(name, ['pluralize', 'underscore', 'dasherize']) %>.dto';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Injectable()
export class <%= h.inflection.transform(name, ['pluralize']) %>Service {
  constructor(
    private readonly cacheServiceHelper: CacheServiceHelper,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
    private readonly <%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository: <%= name %>Repository,
  ) {}

  create(create<%= name %>Dto: Create<%= name %>Dto) {
    return this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.create(create<%= name %>Dto);
  }

  findAllPaginated(query: FindAllDto) {
    return this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.findAll(query, true);
  }

  findAll(query: FindAllDto) {
    return this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.findAll(query, false);
  }

  findOne(id: <%= name %>['id']) {
    return this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.findById(id);
  }

  update(id: <%= name %>['id'], update<%= name %>Dto: Update<%= name %>Dto) {
    return this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.update(id, update<%= name %>Dto);
  }

  remove(id: <%= name %>['id']) {
    return this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.remove(id);
  }
}
