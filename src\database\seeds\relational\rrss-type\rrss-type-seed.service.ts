import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { rrssTypeEntity } from '../../../../rrss-types/infrastructure/persistence/relational/entities/rrss-type.entity';
import { Repository } from 'typeorm';

@Injectable()
export class rrssTypeSeedService {
  constructor(
    @InjectRepository(rrssTypeEntity)
    private repository: Repository<rrssTypeEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (count === 0) {
      await this.repository.save([
        this.repository.create({ name: 'Facebook' }),
        this.repository.create({ name: 'X' }),
        this.repository.create({ name: 'Instagram' }),
        this.repository.create({ name: 'TikTok' }),
        this.repository.create({ name: 'YouTube' }),
        this.repository.create({ name: 'Others' }),
        this.repository.create({ name: 'Telegram' }),
      ]);
    }
  }
}
