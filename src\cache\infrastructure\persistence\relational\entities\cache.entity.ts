import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { User } from '../../../../../users/domain/user';
import { UserEntity } from '../../../../../users/infrastructure/persistence/relational/entities/user.entity';

@Entity({
  name: 'cache',
})
export class CacheDataEntity extends EntityRelationalHelper {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiResponseProperty({ type: Date })
  @Column({ default: null })
  cacheDate: Date;

  @ApiResponseProperty({ type: String })
  @Column({ default: null })
  description: string;

  @ApiResponseProperty({ type: String })
  @Column({ default: null })
  cacheKey: string;

  @ApiResponseProperty({ type: () => UserEntity })
  @ManyToOne(() => UserEntity, { eager: true })
  user: User;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
