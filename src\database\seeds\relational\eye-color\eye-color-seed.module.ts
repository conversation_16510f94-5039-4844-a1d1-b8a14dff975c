import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { EyeColorSeedService } from './eye-color-seed.service';
import { EyeColorEntity } from '../../../../eye-colors/infrastructure/persistence/relational/entities/eye-color.entity';

@Module({
  imports: [TypeOrmModule.forFeature([EyeColorEntity])],
  providers: [EyeColorSeedService],
  exports: [EyeColorSeedService],
})
export class EyeColorSeedModule {}
