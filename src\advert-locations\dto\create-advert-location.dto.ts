import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { NeighborhoodEntity } from '../../location/infraestructure/persistence/relational/entities/neighborhood.entity';
import { CityEntity } from '../../location/infraestructure/persistence/relational/entities/city.entity';
import { RegionEntity } from '../../location/infraestructure/persistence/relational/entities/region.entity';
import { CountryEntity } from '../../location/infraestructure/persistence/relational/entities/country.entity';
import { Advert } from '../../adverts/domain/advert';

export class CreateAdvertLocationDto {
  @ApiProperty({ type: Number })
  @IsNotEmpty()
  id: number;

  @ApiProperty({ type: Advert })
  advert: Advert;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: String })
  url: string;

  @ApiProperty({ type: String })
  googleApiResponse: string;

  @ApiProperty({
    type: () => NeighborhoodEntity,
  })
  neighborhood: NeighborhoodEntity;

  @ApiProperty({
    type: () => CityEntity,
  })
  city: CityEntity;

  @ApiProperty({
    type: () => RegionEntity,
  })
  region: RegionEntity;

  @ApiProperty({
    type: () => CountryEntity,
  })
  country: CountryEntity;
}
