import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { HairColor } from '../../domain/hair-color';

export abstract class HairColorRepository {
  abstract create(
    data: Omit<HairColor, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<HairColor>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<HairColor[]>;

  abstract findAll(): Promise<HairColor[]>;

  abstract findById(id: HairColor['id']): Promise<NullableType<HairColor>>;

  abstract update(
    id: HairColor['id'],
    payload: DeepPartial<HairColor>,
  ): Promise<HairColor | null>;

  abstract remove(id: HairColor['id']): Promise<void>;
}
