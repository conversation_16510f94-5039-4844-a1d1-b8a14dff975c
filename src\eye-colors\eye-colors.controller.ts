import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { EyeColorsService } from './eye-colors.service';
import { CreateEyeColorDto } from './dto/create-eye-color.dto';
import { UpdateEyeColorDto } from './dto/update-eye-color.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { EyeColor } from './domain/eye-color';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllEyeColorsDto } from './dto/find-all-eye-colors.dto';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiTags('EyeColors')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'eye-colors',
  version: '1',
})
export class EyeColorsController {
  constructor(private readonly eye_colorsService: EyeColorsService) {}

  @Post()
  @ApiCreatedResponse({
    type: EyeColor,
  })
  create(@Body() createEyeColorDto: CreateEyeColorDto) {
    return this.eye_colorsService.create(createEyeColorDto);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(EyeColor),
  })
  async findAll(
    @Query() query: FindAllEyeColorsDto,
  ): Promise<InfinityPaginationResponseDto<EyeColor>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.eye_colorsService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('all')
  @ApiOkResponse({
    type: EyeColor,
  })
  async findAllWithoutPagination() {
    return await this.eye_colorsService.findAll();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.eye_colorsService.findOne(id);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: EyeColor,
  })
  update(
    @Param('id') id: number,
    @Body() updateEyeColorDto: UpdateEyeColorDto,
  ) {
    return this.eye_colorsService.update(id, updateEyeColorDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.eye_colorsService.remove(id);
  }
}
