import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RateTypeEntity } from '../../../../rate-types/infrastructure/persistence/relational/entities/rate-type.entity';
import { RateTypeSeedService } from './rate-type-seed.service';

@Module({
  imports: [TypeOrmModule.forFeature([RateTypeEntity])],
  providers: [RateTypeSeedService],
  exports: [RateTypeSeedService],
})
export class rateTypeSeedModule {}
