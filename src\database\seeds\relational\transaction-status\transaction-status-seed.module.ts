import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { TransactionStatusSeedService } from './transaction-status-seed.service';
import { TransactionStatusEntity } from '../../../../transactionstatuses/infrastructure/persistence/relational/entities/transactionstatus.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TransactionStatusEntity])],
  providers: [TransactionStatusSeedService],
  exports: [TransactionStatusSeedService],
})
export class TransactionStatusSeedModule {}
