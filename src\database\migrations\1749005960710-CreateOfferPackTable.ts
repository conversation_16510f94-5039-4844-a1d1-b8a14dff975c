import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateOfferPackTable1749005960710 implements MigrationInterface {
  name = 'CreateOfferPackTable1749005960710';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "offer_pack" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL DEFAULT '', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "description" character varying NOT NULL DEFAULT '', "initDate" TIMESTAMP NOT NULL, "endDate" TIMESTAMP NOT NULL, "qty" integer NOT NULL, "activationType" character varying NOT NULL, "active" boolean NOT NULL, CONSTRAINT "PK_472ef73facca863558849601744" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "offer_pack"`);
  }
}
