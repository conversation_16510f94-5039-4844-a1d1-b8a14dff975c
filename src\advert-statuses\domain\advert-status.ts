import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';

export class AdvertStatus {
  @ApiResponseProperty({
    type: Number,
  })
  id: number;

  @ApiResponseProperty({
    type: String,
    example: 'ACTIVE',
  })
  name: string;

  @ApiResponseProperty({
    type: String,
    example: '#ffffff',
  })
  bcolor: string;

  @ApiResponseProperty({
    type: String,
    example: '#ffffff',
  })
  fcolor: string;

  @ApiResponseProperty({
    type: String,
    example: '#ffffff',
  })
  icolor: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
