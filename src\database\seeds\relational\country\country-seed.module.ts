import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CountrySeedService } from './country-seed.service';
import { CountryEntity } from '../../../../location/infraestructure/persistence/relational/entities/country.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CountryEntity])],
  providers: [CountrySeedService],
  exports: [CountrySeedService],
})
export class CountrySeedModule {}
