import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { Bookmark } from '../../domain/bookmark';

export abstract class BookmarkRepository {
  abstract create(
    data: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Bookmark>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<Bookmark[]>;

  abstract findAll(): Promise<Bookmark[]>;

  abstract findById(id: Bookmark['id']): Promise<NullableType<Bookmark>>;

  abstract findMany(userId: string): Promise<NullableType<Bookmark[]>>;

  abstract update(
    id: Bookmark['id'],
    payload: DeepPartial<Bookmark>,
  ): Promise<Bookmark | null>;

  abstract remove(id: Bookmark['id']): Promise<void>;
}
