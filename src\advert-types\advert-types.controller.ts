import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { AdvertTypesService } from './advert-types.service';
import { CreateAdvertTypeDto } from './dto/create-advert-type.dto';
import { UpdateAdvertTypeDto } from './dto/update-advert-type.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { AdvertType } from './domain/advert-type';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllAdvertTypesDto } from './dto/find-all-advert-types.dto';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiTags('AdvertTypes')
@Controller({
  path: 'advert-types',
  version: '1',
})
export class AdvertTypesController {
  constructor(private readonly advert_typesService: AdvertTypesService) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiCreatedResponse({
    type: AdvertType,
  })
  create(@Body() createAdvertTypeDto: CreateAdvertTypeDto) {
    return this.advert_typesService.create(createAdvertTypeDto);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(AdvertType),
  })
  async findAll(
    @Query() query: FindAllAdvertTypesDto,
  ): Promise<InfinityPaginationResponseDto<AdvertType>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.advert_typesService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: AdvertType,
  })
  async findAllWithoutPagination(): Promise<AdvertType[]> {
    return await this.advert_typesService.findAll();
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('allcount-service')
  @ApiParam({
    name: 'slug',
    type: String,
    required: true,
  })
  async findAllCountWithoutPagination(@Query() query: any): Promise<any[]> {
    return await this.advert_typesService.findAllAndCountQuery(query);
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.advert_typesService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: AdvertType,
  })
  update(
    @Param('id') id: number,
    @Body() updateAdvertTypeDto: UpdateAdvertTypeDto,
  ) {
    return this.advert_typesService.update(id, updateAdvertTypeDto);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.advert_typesService.remove(id);
  }
}
