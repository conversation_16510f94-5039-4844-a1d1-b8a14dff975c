import Link from 'next/link';
import { getTranslations } from 'next-intl/server';
import RegisterForm from '@/components/forms/auth/Register';

export async function generateMetadata({
  params,
}: {
  params: { locale: string };
}) {
  const t = await getTranslations({ locale: params.locale, namespace: 'auth' });
  return {
    title: t('register-title'),
    description: t('have-account'),
    robots: {
      index: false,
      follow: true,
    },
    alternates: {
      canonical: '/sign-up',
    },
  };
}

export default async function SignUpPage() {
  const t = await getTranslations('auth');

  return (
    <div className="w-full max-w-lg">
      <div className="text-center">
        <h2 className="text-2xl lg:text-3xl font-bold text-gray-900">
          {t('register-title')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('have-account')}{' '}
          <Link
            href="/sign-in"
            className="font-medium text-[#F15C5C] hover:text-red-500"
          >
            {t('login')}
          </Link>
        </p>
      </div>

      <div className="mt-8">
        <RegisterForm />
      </div>

      <div className="mt-6 text-center text-xs text-gray-500">
        <p>
          {t('terms.prefix')}{' '}
          <Link
            href="/terms-and-conditions"
            className="underline hover:text-gray-700"
          >
            {t('terms.terms')}
          </Link>{' '}
          &{' '}
          <Link
            href="/privacy-policy"
            className="underline hover:text-gray-700"
          >
            {t('terms.privacy')}
          </Link>
          .
        </p>
      </div>
    </div>
  );
}
