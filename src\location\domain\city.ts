import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { RegionEntity } from '../infraestructure/persistence/relational/entities/region.entity';

export class City {
  @ApiResponseProperty({
    type: Number,
  })
  id: number;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  name: string;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  code: string;

  @ApiResponseProperty({
    type: String,
  })
  slug: string;

  @ApiResponseProperty({
    type: String,
  })
  defaultLocation: string;

  @ApiResponseProperty({
    type: () => RegionEntity,
  })
  region: RegionEntity;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
