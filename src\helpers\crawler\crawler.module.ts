import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CrawlerService } from './crawler.service';
import { CrawlerController } from './crawler.controller';
import { MediaUploadService } from '../cloudfare/video-upload.service';
import { GoogleApiService } from '../google-api/google-api.service';
import { HttpModule } from '@nestjs/axios';
import { CacheServiceHelper } from '../cache/cache.service';

@Module({
  imports: [HttpModule],
  controllers: [CrawlerController],
  providers: [
    CrawlerService,
    MediaUploadService,
    GoogleApiService,
    CacheServiceHelper,
  ],
  exports: [
    CrawlerService,
    MediaUploadService,
    GoogleApiService,
    CacheServiceHelper,
  ],
})
export class CrawlerModule {}
