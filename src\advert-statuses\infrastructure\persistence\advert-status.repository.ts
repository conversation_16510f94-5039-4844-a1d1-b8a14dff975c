import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { AdvertStatus } from '../../domain/advert-status';

export abstract class AdvertStatusRepository {
  abstract create(
    data: Omit<AdvertStatus, 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<AdvertStatus>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertStatus[]>;

  abstract findById(
    id: AdvertStatus['id'],
  ): Promise<NullableType<AdvertStatus>>;

  abstract update(
    id: AdvertStatus['id'],
    payload: DeepPartial<AdvertStatus>,
  ): Promise<AdvertStatus | null>;

  abstract remove(id: AdvertStatus['id']): Promise<void>;
}
