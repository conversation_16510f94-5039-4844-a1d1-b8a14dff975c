import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertLinkEntity } from '../entities/advert-link.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { AdvertLink } from '../../../../domain/advert-link';
import { AdvertLinkRepository } from '../../advert-link.repository';
import { AdvertLinkMapper } from '../mappers/advert-link.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class AdvertLinkRelationalRepository implements AdvertLinkRepository {
  constructor(
    @InjectRepository(AdvertLinkEntity)
    private readonly advert_linkRepository: Repository<AdvertLinkEntity>,
  ) {}

  async create(data: AdvertLink): Promise<AdvertLink> {
    const persistenceModel = AdvertLinkMapper.toPersistence(data);
    const newEntity = await this.advert_linkRepository.save(
      this.advert_linkRepository.create(persistenceModel),
    );
    return AdvertLinkMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertLink[]> {
    const entities = await this.advert_linkRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((element) => AdvertLinkMapper.toDomain(element));
  }

  async findAll(): Promise<AdvertLink[]> {
    const entities = await this.advert_linkRepository.find();

    return entities.map((element) => AdvertLinkMapper.toDomain(element));
  }

  async findById(id: AdvertLink['id']): Promise<NullableType<AdvertLink>> {
    const entity = await this.advert_linkRepository.findOne({
      where: { id },
    });

    return entity ? AdvertLinkMapper.toDomain(entity) : null;
  }

  async findMany(advertId: string): Promise<NullableType<AdvertLink[]>> {
    return await this.advert_linkRepository.find({
      where: { advert: { id: advertId } },
    });
  }

  async update(
    id: AdvertLink['id'],
    payload: Partial<AdvertLink>,
  ): Promise<AdvertLink> {
    const entity = await this.advert_linkRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.advert_linkRepository.save(
      this.advert_linkRepository.create(
        AdvertLinkMapper.toPersistence({
          ...AdvertLinkMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return AdvertLinkMapper.toDomain(updatedEntity);
  }

  async remove(id: AdvertLink['id']): Promise<void> {
    await this.advert_linkRepository.softDelete(id);
  }
}
