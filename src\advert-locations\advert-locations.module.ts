import { Module } from '@nestjs/common';
import { AdvertLocationsService } from './advert-locations.service';
import { AdvertLocationsController } from './advert-locations.controller';
import { RelationalAdvertLocationPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [RelationalAdvertLocationPersistenceModule],
  controllers: [AdvertLocationsController],
  providers: [AdvertLocationsService],
  exports: [AdvertLocationsService, RelationalAdvertLocationPersistenceModule],
})
export class AdvertLocationsModule {}
