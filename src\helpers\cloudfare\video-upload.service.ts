import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import process from 'node:process';
import * as tus from 'tus-js-client';
import Cloudflare from 'cloudflare';

@Injectable()
export class MediaUploadService {
  private readonly logger = new Logger(MediaUploadService.name);

  private readonly accountId =
    process.env.CLOUDFARE_ACCOUNT_ID ?? '13fd7eb3f450cf72aceed0d227fbf1ad';
  private readonly apiToken =
    process.env.CLOUDFARE_API_TOKEN ??
    '0-a-rBNXna8TCy2mLE7QfxPFhj14mOBXF5_8OExt';

  async uploadVideo(dataFile: Express.Multer.File): Promise<string> {
    const fileStream = dataFile.buffer;
    const fileSize = dataFile.size;
    const fileName = dataFile.originalname;
    const fileType = dataFile.mimetype;
    let mediaId = '';

    return new Promise((resolve, reject) => {
      const upload = new tus.Upload(fileStream, {
        endpoint: `https://api.cloudflare.com/client/v4/accounts/${this.accountId}/stream`,
        headers: {
          Authorization: `Bearer ${this.apiToken}`,
        },
        chunkSize: 50 * 1024 * 1024,
        retryDelays: [0, 3000, 5000, 10000, 20000],
        metadata: {
          name: fileName,
          filetype: fileType,
        },
        uploadSize: fileSize,
        onError: (error: Error) => {
          this.logger.error(`Upload failed: ${error.message}`);
          reject(error);
        },
        onProgress: (bytesUploaded: number, bytesTotal: number) => {
          const percentage = ((bytesUploaded / bytesTotal) * 100).toFixed(2);
          this.logger.log(`Upload progress: ${percentage}%`);
        },
        onSuccess: () => {
          this.logger.log('Upload finished successfully');
          resolve(mediaId);
        },
        onAfterResponse: (req, res) => {
          return new Promise<void>((resolve) => {
            const mediaIdHeader = res.getHeader('stream-media-id');
            if (mediaIdHeader) {
              mediaId = mediaIdHeader as string;
            }
            resolve();
          });
        },
      });

      upload.start();
    });
  }

  async getDirectUploadUrl(): Promise<string | null> {
    const client = new Cloudflare({
      apiToken: this.apiToken,
    });

    const directUpload = await client.images.v2.directUploads.create({
      account_id: this.accountId,
    });

    return directUpload.uploadURL ?? null;
  }

  async uploadImageToDirectUrl(
    uploadUrl: string,
    fileBuffer: Buffer,
    fileName: string,
  ) {
    const formData = new FormData();
    formData.append('file', new Blob([fileBuffer]), fileName);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const result = await response.json();
      throw new InternalServerErrorException(
        'Error al subir la imagen',
        result,
      );
    }

    return response.json();
  }
}
