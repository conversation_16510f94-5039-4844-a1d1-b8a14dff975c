import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { Advert } from '../../adverts/domain/advert';
import { Type } from 'class-transformer';
import { RateType } from '../../rate-types/domain/rate-type';

export class CreateAdvertRateDto {
  @ApiPropertyOptional({ type: Advert })
  @Type(() => Advert)
  advert: Advert;

  @ApiPropertyOptional({ type: RateType })
  @Type(() => RateType)
  rateType: RateType;

  @ApiProperty({ example: 0, type: Number })
  @IsNotEmpty()
  amount: number;
}
