import { Module } from '@nestjs/common';
import { HairColorsService } from './hair-colors.service';
import { HairColorsController } from './hair-colors.controller';
import { RelationalHairColorPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [RelationalHairColorPersistenceModule],
  controllers: [HairColorsController],
  providers: [HairColorsService],
  exports: [HairColorsService, RelationalHairColorPersistenceModule],
})
export class HairColorsModule {}
