import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { NeighborhoodEntity } from '../../../../../location/infraestructure/persistence/relational/entities/neighborhood.entity';
import { CityEntity } from '../../../../../location/infraestructure/persistence/relational/entities/city.entity';
import { RegionEntity } from '../../../../../location/infraestructure/persistence/relational/entities/region.entity';
import { CountryEntity } from '../../../../../location/infraestructure/persistence/relational/entities/country.entity';
import { AdvertEntity } from '../../../../../adverts/infrastructure/persistence/relational/entities/advert.entity';
import { Advert } from '../../../../../adverts/domain/advert';

@Entity({
  name: 'advert_location',
})
export class AdvertLocationEntity extends EntityRelationalHelper {
  @ApiResponseProperty({
    type: Number,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiResponseProperty({
    type: () => AdvertEntity,
  })
  @ManyToOne(() => AdvertEntity, (advert) => advert.advertLocations)
  @ApiProperty({ type: () => Advert })
  advert: Advert;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  name: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  url: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column('text')
  googleApiResponse: string;

  @ApiResponseProperty({
    type: () => NeighborhoodEntity,
  })
  @ManyToOne(
    () => NeighborhoodEntity,
    (neighborhood) => neighborhood.advertLocations,
  )
  neighborhood: NeighborhoodEntity;

  @ApiResponseProperty({
    type: () => CityEntity,
  })
  @ManyToOne(() => CityEntity, (city) => city.advertLocations)
  city: CityEntity;

  @ApiResponseProperty({
    type: () => RegionEntity,
  })
  @ManyToOne(() => RegionEntity, (region) => region.advertLocations)
  region: RegionEntity;

  @ApiResponseProperty({
    type: () => CountryEntity,
  })
  @ManyToOne(() => CountryEntity, (country) => country.advertLocations)
  country: CountryEntity;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
