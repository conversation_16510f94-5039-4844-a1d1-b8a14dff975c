import { Module } from '@nestjs/common';
import { GenderRepository } from '../gender.repository';
import { GenderRelationalRepository } from './repositories/gender.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GenderEntity } from './entities/gender.entity';

@Module({
  imports: [TypeOrmModule.forFeature([GenderEntity])],
  providers: [
    {
      provide: GenderRepository,
      useClass: GenderRelationalRepository,
    },
  ],
  exports: [GenderRepository],
})
export class RelationalGenderPersistenceModule {}
