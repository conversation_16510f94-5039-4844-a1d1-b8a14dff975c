import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { AdvertType } from '../../domain/advert-type';

export abstract class AdvertTypeRepository {
  abstract create(
    data: Omit<AdvertType, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<AdvertType>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertType[]>;

  abstract findAll(): Promise<AdvertType[]>;

  abstract findAllAndCountQuery(queryParams: any): Promise<any>;

  abstract findById(id: AdvertType['id']): Promise<NullableType<AdvertType>>;

  abstract update(
    id: AdvertType['id'],
    payload: DeepPartial<AdvertType>,
  ): Promise<AdvertType | null>;

  abstract remove(id: AdvertType['id']): Promise<void>;
}
