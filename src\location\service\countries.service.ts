import { Injectable } from '@nestjs/common';
import { CountryRepository } from '../infraestructure/persistence/country.repository';
import { CreateCountryDto } from '../dto/create-country.dto';
import { IPaginationOptions } from '../../utils/types/pagination-options';
import { Country } from '../domain/country';
import { UpdateCountryDto } from '../dto/update-country.dto';
import { FindAllCountriesDto } from '../dto/find-all-countries.dto';

@Injectable()
export class CountriesService {
  constructor(private readonly countryRepository: CountryRepository) {}

  create(createCountryDto: CreateCountryDto) {
    return this.countryRepository.create(createCountryDto);
  }

  findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCountriesDto,
  ) {
    return this.countryRepository.findAllWithPagination(
      {
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      },
      filters,
    );
  }

  findAll() {
    return this.countryRepository.findAll();
  }

  findAllAndCount() {
    return this.countryRepository.findAllAndCount();
  }

  findOne(id: Country['id']) {
    return this.countryRepository.findById(id);
  }

  update(id: Country['id'], updateRegionDto: UpdateCountryDto) {
    return this.countryRepository.update(id, updateRegionDto);
  }

  remove(id: Country['id']) {
    return this.countryRepository.remove(id);
  }
}
