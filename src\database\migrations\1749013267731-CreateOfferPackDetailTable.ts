import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateOfferPackDetailTable1749013267731
  implements MigrationInterface
{
  name = 'CreateOfferPackDetailTable1749013267731';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "offer_pack_detail" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL DEFAULT '', "description" character varying NOT NULL DEFAULT '', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "offerPackId" uuid, "productId" integer, CONSTRAINT "PK_ca7a13179861ccba1a7a6208bb1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" ADD CONSTRAINT "FK_90650edb0d451938d227a51a6ac" FOREIGN KEY ("offerPackId") REFERENCES "offer_pack"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" ADD CONSTRAINT "FK_952a5e3103521b4f70961ad8351" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" DROP CONSTRAINT "FK_952a5e3103521b4f70961ad8351"`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack_detail" DROP CONSTRAINT "FK_90650edb0d451938d227a51a6ac"`,
    );
    await queryRunner.query(`DROP TABLE "offer_pack_detail"`);
  }
}
