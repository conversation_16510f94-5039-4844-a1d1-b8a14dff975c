import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CountriesService } from '../service/countries.service';
import { CreateCountryDto } from '../dto/create-country.dto';
import { UpdateCountryDto } from '../dto/update-country.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { Country } from '../domain/country';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../../utils/infinity-pagination';
import { FindAllCountriesDto } from '../dto/find-all-countries.dto';
import { plainToInstance } from 'class-transformer';

@ApiTags('Countries')
@Controller({
  path: 'countries',
  version: '1',
})
export class CountriesController {
  constructor(private readonly countriesService: CountriesService) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiCreatedResponse({
    type: Country,
  })
  create(@Body() createCountryDto: any) {
    const data = plainToInstance(CreateCountryDto, createCountryDto);
    return this.countriesService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(Country),
  })
  async findAll(
    @Query() query: FindAllCountriesDto,
  ): Promise<InfinityPaginationResponseDto<Country>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.countriesService.findAllWithPagination(
        {
          paginationOptions: {
            page,
            limit,
          },
        },
        query,
      ),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: Country,
  })
  async findAllWithoutPagination(): Promise<Country[]> {
    return await this.countriesService.findAll();
  }

  @Get('allcount')
  async findAllCountWithoutPagination(): Promise<any[]> {
    return await this.countriesService.findAllAndCount();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.countriesService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  @ApiOkResponse({
    type: Country,
  })
  update(@Param('id') id: number, @Body() updateCountryDto: any) {
    const data = plainToInstance(UpdateCountryDto, updateCountryDto);

    return this.countriesService.update(id, data);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.countriesService.remove(id);
  }
}
