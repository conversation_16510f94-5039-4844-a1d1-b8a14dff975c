import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import * as process from 'node:process';
import { DataSource } from 'typeorm';

@Injectable()
export class GoogleApiService {
  private readonly googleApiUrl =
    process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_URL ??
    'https://maps.googleapis.com/maps/api/geocode/json';
  private readonly apiKey =
    process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ??
    'AIzaSyDVTn-JBD40YPCKJNp9XhLXYMshxFtKL3k';
  constructor(
    private readonly httpService: HttpService,
    private readonly datasource: DataSource,
  ) {}

  async getGeocodeByPlaceId(language: string, placeId: string): Promise<any> {
    const url = `${this.googleApiUrl}?language=${language}&place_id=${encodeURIComponent(placeId)}&key=${this.apiKey}`;
    try {
      const response = await lastValueFrom(this.httpService.get(url));
      return response.data;
    } catch (error) {
      throw new Error(`Failed to fetch geocode: ${error.message}`);
    }
  }

  async getGeocodeByPlaceName(
    language: string,
    placeName: string,
  ): Promise<any> {
    const url = `${this.googleApiUrl}?language=${language}&address=${encodeURIComponent(placeName)}&key=${this.apiKey}`;
    try {
      const response = await lastValueFrom(this.httpService.get(url));
      return response.data;
    } catch (error) {
      throw new Error(`Failed to fetch geocode: ${error.message}`);
    }
  }

  async transformToLocation(geoCodeData: any): Promise<any> {
    const parsedGoogleData = {};
    if (geoCodeData) {
      if (geoCodeData.results) {
        parsedGoogleData['googleApiResponse'] = geoCodeData.results;
        for (const data of geoCodeData.results) {
          parsedGoogleData['placeId'] = data.place_id;
          parsedGoogleData['formattedAddress'] = data.formatted_address;
          for (const component of data.address_components) {
            if (component.types.some((type) => type === 'locality')) {
              parsedGoogleData['neighborhood'] = component.long_name;
              parsedGoogleData['neighborhoodId'] = await this.getLocation(
                'neighborhood',
                'name',
                component.long_name,
              );
            } else if (
              component.types.some(
                (type) => type === 'administrative_area_level_2',
              )
            ) {
              const locationCity = await this.getLocation(
                'city',
                'code',
                component.short_name,
              );
              if (locationCity) {
                parsedGoogleData['city'] = component.long_name;
                parsedGoogleData['cityId'] = locationCity;
              }
              const locationRegion = await this.getLocation(
                'region',
                'code',
                component.short_name,
              );
              if (locationRegion) {
                parsedGoogleData['region'] = component.long_name;
                parsedGoogleData['regionId'] = locationRegion;
              }
            } else if (
              component.types.some(
                (type) => type === 'administrative_area_level_4',
              )
            ) {
              if (parsedGoogleData['cityId'] === undefined) {
                parsedGoogleData['city'] = component.short_name;
                parsedGoogleData['cityId'] = await this.getLocation(
                  'city',
                  'name',
                  component.short_name,
                );
              }
            } else if (
              component.types.some(
                (type) => type === 'administrative_area_level_1',
              )
            ) {
              parsedGoogleData['region'] = component.long_name;
              parsedGoogleData['regionId'] = await this.getLocation(
                'region',
                'code',
                component.short_name,
              );
            } else if (component.types.some((type) => type === 'country')) {
              parsedGoogleData['country'] = component.long_name;
              parsedGoogleData['countryCode'] = component.short_name;
              parsedGoogleData['countryId'] = await this.getLocation(
                'countries',
                'code',
                component.short_name,
              );
            }
          }
        }
        if (parsedGoogleData['country']) {
          const countryId = await this.verifyCountry(
            parsedGoogleData['country'],
            parsedGoogleData['countryCode'],
          );
          if (countryId != '') {
            parsedGoogleData['countryId'] = countryId;
          }
        }
        if (parsedGoogleData['region'] && parsedGoogleData['countryId']) {
          const regionId = await this.verifyRegion(
            parsedGoogleData['region'],
            parsedGoogleData['countryId'],
          );
          if (regionId != '') {
            parsedGoogleData['regionId'] = regionId;
          }
        }
        if (parsedGoogleData['city'] && parsedGoogleData['regionId']) {
          const cityId = await this.verifyCity(
            parsedGoogleData['city'],
            parsedGoogleData['regionId'],
          );
          if (cityId != '') {
            parsedGoogleData['cityId'] = cityId;
          }
        }
        if (parsedGoogleData['neighborhood'] && parsedGoogleData['cityId']) {
          const neighborhoodId = await this.verifyNeighborhood(
            parsedGoogleData['neighborhood'],
            parsedGoogleData['cityId'],
          );
          if (neighborhoodId != '') {
            parsedGoogleData['neighborhoodId'] = neighborhoodId;
          }
        }
      }
    }
    return parsedGoogleData;
  }

  async verifyNeighborhood(name: string, cityId: string): Promise<string> {
    const exists = await this.getLocation('neighborhood', 'name', name);
    if (!exists) {
      await this.insertNeighborhood(cityId, name);
      return await this.getLocation('neighborhood', 'name', name);
    } else {
      return exists;
    }
  }

  async verifyCity(
    name: string,
    regionId: string,
    code: string = '',
  ): Promise<string> {
    const exists = await this.getLocation('city', 'name', name);
    if (!exists) {
      await this.insertCity(regionId, name, code);
      return await this.getLocation('city', 'name', name);
    } else {
      return exists;
    }
  }

  async verifyRegion(
    name: string,
    countryId: string,
    code: string = '',
  ): Promise<string> {
    const exists = await this.getLocation('region', 'name', name);
    if (!exists) {
      await this.insertRegion(countryId, name, code);
      return await this.getLocation('region', 'name', name);
    } else {
      return exists;
    }
  }

  async verifyCountry(name: string, code: string): Promise<string> {
    const exists = await this.getLocation('countries', 'name', name);
    if (!exists) {
      await this.insertCountry(name, code);
      return await this.getLocation('countries', 'name', name);
    } else {
      return exists;
    }
  }

  async getLocation(table: string, fieldName: string, fieldValue: string) {
    const query = `SELECT * FROM ${table} WHERE "${fieldName}" = '${fieldValue}'`;
    const result = await this.datasource.query(query);
    if (result.length > 0) {
      return result[0].id;
    } else {
      return null;
    }
  }

  async insertNeighborhood(cityId: string, neighborhoodName: string) {
    const slug = this.generateSlug(neighborhoodName);
    const query = `INSERT INTO neighborhood ("name", "city_id", "slug") VALUES ('${neighborhoodName}','${cityId}','${slug}')`;
    await this.datasource.query(query);
  }

  async insertCity(regionId: string, cityName: string, cityCode: string) {
    const slug = this.generateSlug(cityName);
    const query = `INSERT INTO city ("name", "region_id", "code", "slug") VALUES ('${cityName}','${regionId}','${cityCode}','${slug}')`;
    await this.datasource.query(query);
  }

  async insertRegion(
    countryId: string,
    regionName: string,
    regionCode: string,
  ) {
    const slug = this.generateSlug(regionName);
    const query = `INSERT INTO region ("name", "country_id", "code", "slug") VALUES ('${regionName}','${countryId}','${regionCode}','${slug}')`;
    await this.datasource.query(query);
  }

  async insertCountry(countryName: string, countryCode: string) {
    const slug = this.generateSlug(countryName);
    const query = `INSERT INTO countries ("name", "code", "slug") VALUES ('${countryName}','${countryCode}','${slug}')`;
    await this.datasource.query(query);
  }

  generateSlug(slug: string): string {
    return slug
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '');
  }
}
