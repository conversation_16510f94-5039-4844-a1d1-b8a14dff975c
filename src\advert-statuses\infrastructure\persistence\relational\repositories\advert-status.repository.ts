import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertStatusEntity } from '../entities/advert-status.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { AdvertStatus } from '../../../../domain/advert-status';
import { AdvertStatusRepository } from '../../advert-status.repository';
import { AdvertStatusMapper } from '../mappers/advert-status.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class AdvertStatusRelationalRepository
  implements AdvertStatusRepository
{
  constructor(
    @InjectRepository(AdvertStatusEntity)
    private readonly advert_statusRepository: Repository<AdvertStatusEntity>,
  ) {}

  async create(data: AdvertStatus): Promise<AdvertStatus> {
    const persistenceModel = AdvertStatusMapper.toPersistence(data);
    const newEntity = await this.advert_statusRepository.save(
      this.advert_statusRepository.create(persistenceModel),
    );
    return AdvertStatusMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertStatus[]> {
    const entities = await this.advert_statusRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((user) => AdvertStatusMapper.toDomain(user));
  }

  async findById(id: AdvertStatus['id']): Promise<NullableType<AdvertStatus>> {
    const entity = await this.advert_statusRepository.findOne({
      where: { id },
    });

    return entity ? AdvertStatusMapper.toDomain(entity) : null;
  }

  async update(
    id: AdvertStatus['id'],
    payload: Partial<AdvertStatus>,
  ): Promise<AdvertStatus> {
    const entity = await this.advert_statusRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.advert_statusRepository.save(
      this.advert_statusRepository.create(
        AdvertStatusMapper.toPersistence({
          ...AdvertStatusMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return AdvertStatusMapper.toDomain(updatedEntity);
  }

  async remove(id: AdvertStatus['id']): Promise<void> {
    await this.advert_statusRepository.delete(id);
  }
}
