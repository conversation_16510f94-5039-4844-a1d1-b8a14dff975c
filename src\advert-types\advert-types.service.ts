import { Inject, Injectable } from '@nestjs/common';
import { CreateAdvertTypeDto } from './dto/create-advert-type.dto';
import { UpdateAdvertTypeDto } from './dto/update-advert-type.dto';
import { AdvertTypeRepository } from './infrastructure/persistence/advert-type.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { AdvertType } from './domain/advert-type';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Injectable()
export class AdvertTypesService {
  constructor(
    private readonly advert_typeRepository: AdvertTypeRepository,
    private readonly cacheServiceHelper: CacheServiceHelper,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createAdvertTypeDto: CreateAdvertTypeDto) {
    return this.advert_typeRepository.create(createAdvertTypeDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.advert_typeRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  findAll() {
    return this.advert_typeRepository.findAll();
  }

  async findAllAndCountQuery(query: any) {
    let _cacheKey = 'CACHE-ADVERT-TYPES-SERVICES-ALLCOUNT';
    if (query?.slug) {
      _cacheKey += '-' + query.slug;
    }
    /* this.cacheServiceHelper.addUsedCacheKey('advertTypesService.findAllAndCountQuery', _cacheKey, '162fff65-e440-402d-91eb-649400b79052'); */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.advert_typeRepository.findAllAndCountQuery(query);
    if (data) {
      return await this.cacheService.set(_cacheKey, data);
    }
  }

  findOne(id: AdvertType['id']) {
    return this.advert_typeRepository.findById(id);
  }

  update(id: AdvertType['id'], updateAdvertTypeDto: UpdateAdvertTypeDto) {
    return this.advert_typeRepository.update(id, updateAdvertTypeDto);
  }

  remove(id: AdvertType['id']) {
    return this.advert_typeRepository.remove(id);
  }
}
