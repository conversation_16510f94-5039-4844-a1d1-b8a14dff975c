import { Injectable } from '@nestjs/common';
import * as puppeteer from 'puppeteer';

@Injectable()
export class CrawlerService {
  constructor() {}

  async scrapeGemidosTV() {
    const browser = await puppeteer.launch({ headless: false });
    const page = await browser.newPage();

    await page.goto('https://gemidos.tv/', { waitUntil: 'networkidle2' });

    // Esperar que los elementos se carguen
    await page.waitForSelector('.listing-pub', { timeout: 60000 });

    // Extraer información de cada publicación
    const listings = await page.$$eval('.listing-pub', (elements) => {
      return elements.map((el) => {
        const name =
          el.querySelector('.info-title')?.textContent?.trim() || 'Sin nombre';
        const age =
          el.querySelector('.info-title .age')?.textContent?.trim() ||
          'Sin edad';
        const location =
          el.querySelector('.info-region')?.textContent?.trim() ||
          'Sin ubicación';
        const profileUrl =
          el.querySelector('.listing-link')?.getAttribute('href') || 'Sin URL';
        const imageUrl =
          el.querySelector('.listing-image')?.getAttribute('src') ||
          'Sin imagen';
        const description =
          el.querySelector('.cover .d-none')?.textContent?.trim() ||
          'Sin descripción';

        return { name, age, location, profileUrl, imageUrl, description };
      });
    });

    await browser.close();

    const detailedListings: any = [];

    if (listings.length > 0) {
      const firstFiveListings = listings.slice(0, 5); // Obtiene los primeros 5 elementos

      for (const listing of firstFiveListings) {
        const info = await this.scrapeGemidosTVDetailPage(listing);
        detailedListings.push(info);
      }

      return detailedListings;
    }
  }

  async scrapeGemidosTVDetailPage(listing: any) {
    const browser = await puppeteer.launch({ headless: false });
    const page = await browser.newPage();

    await page.goto(listing.profileUrl, { waitUntil: 'networkidle2' });

    const pubInfo = await this.scrapeGemidosTVDetailPagePubInfo(page);
    const bookData = await this.scrapeGemidosTVDetailPageBook(page);
    const mapsData = await this.scrapeGemidosTVDetailMaps(page);

    await browser.close();

    return {
      data: listing,
      pubInfo: pubInfo,
      bookData: bookData,
      mapsData: mapsData,
    };
  }

  async scrapeGemidosTVDetailPagePubInfo(page: any) {
    await page.waitForSelector('.pub-info', { timeout: 60000 });

    return await page.evaluate(() => {
      const getText = (selector: string) =>
        document.querySelector(selector)?.textContent?.trim() ||
        'No disponible';

      const whatsapp =
        document.querySelector('.pub-phone span')?.textContent?.trim() ||
        'No disponible';
      const age = getText('.pub-tags-item.number:nth-child(2)');
      const height = getText('.pub-tags-item.number:nth-child(3)');
      const weight = getText('.pub-tags-item.number:nth-child(4)');
      const measurements = getText('.pub-tags-item.number:nth-child(5)');
      const nationality = getText('.pub-tags-item:nth-child(6)');
      const hair = getText('.pub-tags-item:nth-child(7)');
      const eyes = getText('.pub-tags-item:nth-child(8)');
      const skin = getText('.pub-tags-item:nth-child(9)');
      const depilation = getText('.pub-tags-item:nth-child(10)');
      const bodyType = getText('.pub-tags-item:nth-child(11)');
      const bust = getText('.pub-tags-item:nth-child(12)');
      const butt = getText('.pub-tags-item:nth-child(13)');
      const biotype = getText('.pub-tags-item:nth-child(14)');

      // Extraer servicios
      const servicesElements = Array.from(
        document.querySelectorAll('.pub-tags-item:not(.number)'),
      );
      const services = servicesElements
        .map((el) => el.textContent?.trim())
        .filter(Boolean);

      return {
        whatsapp,
        age,
        height,
        weight,
        measurements,
        nationality,
        hair,
        eyes,
        skin,
        depilation,
        bodyType,
        bust,
        butt,
        biotype,
        services,
      };
    });
  }

  async scrapeGemidosTVDetailPageBook(page: any) {
    await page.waitForSelector('.pub-book-item', { timeout: 60000 });

    return await page.evaluate(() => {
      const getMediaInfo = (mediaElement: Element) => {
        const mediaType = mediaElement.getAttribute('data-media_type');
        const mediaId = mediaElement.getAttribute('data-media_id');

        const mediaInfo = {
          type: mediaType,
          id: mediaId,
          imageUrl: '',
          videoUrl: '',
          description: '',
          name: '',
        };

        if (mediaType === 'video') {
          const thumbnailUrl = mediaElement
            .querySelector('meta[itemprop="thumbnailUrl"]')
            ?.getAttribute('content');
          const videoUrl = mediaElement
            .querySelector('meta[itemprop="contentUrl"]')
            ?.getAttribute('content');
          const title = mediaElement
            .querySelector('meta[itemprop="name"]')
            ?.getAttribute('content');
          const description = mediaElement
            .querySelector('meta[itemprop="description"]')
            ?.getAttribute('content');

          mediaInfo.imageUrl = thumbnailUrl || 'No disponible';
          mediaInfo.videoUrl = videoUrl || 'No disponible';
          mediaInfo.name = title || 'No disponible';
          mediaInfo.description = description || 'No disponible';
        }

        if (mediaType === 'photo') {
          const photoUrl = mediaElement
            .querySelector('img.mfp-img')
            ?.getAttribute('src');

          mediaInfo.imageUrl = photoUrl || 'No disponible';
        }

        return mediaInfo;
      };

      const allMedia = Array.from(document.querySelectorAll('.pub-book-item'));

      return allMedia.map(getMediaInfo);
    });
  }

  async scrapeGemidosTVDetailMaps(page: any) {
    await page.waitForSelector('.pub-map', { timeout: 60000 });

    return await page.evaluate(() => {
      const getText = (selector: string) =>
        document.querySelector(selector)?.textContent?.trim() ||
        'No disponible';

      const location = getText('.pub-map-label + h5 strong');
      const googleMapsLink =
        document.querySelector('.pub-map iframe')?.getAttribute('data-src') ||
        'No disponible';
      const directionsLink =
        document
          .querySelector('.fa-route')
          ?.parentElement?.getAttribute('href') || 'No disponible';

      // Extraer los tipos de encuentros
      const encountersElements = Array.from(
        document.querySelectorAll('.pub-location-tag'),
      );
      const encounters = encountersElements
        .map((el) => el.textContent?.trim())
        .filter(Boolean);

      return {
        location,
        googleMapsLink,
        directionsLink,
        encounters,
      };
    });
  }

  async scrapeEuroGirlsDetailPage(url: string) {
    const browser = await puppeteer.launch({ headless: false });
    const page = await browser.newPage();

    await page.goto(url, { waitUntil: 'networkidle2' });

    await page.waitForSelector('.params', { timeout: 60000 });

    // Extraemos los datos de la sección".params"
    const paramsData = await page.evaluate(() => {
      const params = document.querySelectorAll('.params div');

      const getDetail = (span: string) => {
        const element = Array.from(params).find((div) => {
          const spanElement = div.querySelector('span');
          return spanElement && spanElement.textContent?.includes(span);
        });
        return element
          ? element.querySelector('strong')?.textContent
          : 'No disponible';
      };

      return {
        gender: getDetail('Género'),
        age: getDetail('Edad'),
        location: getDetail('Lugar'),
        eyes: getDetail('Ojos'),
        hairColor: getDetail('Color del pelo'),
        hairLength: getDetail('Longitud del pelo'),
        pubicHair: getDetail('Vello púbico'),
        breastSize: getDetail('Tamaño del pecho'),
        breastType: getDetail('Tipo de pecho'),
        travels: getDetail('Viaja'),
        weight: getDetail('Peso'),
        height: getDetail('Altura'),
        ethnicity: getDetail('Identidad étnica'),
        orientation: getDetail('Orientación'),
        smoker: getDetail('Fumadora'),
        tattoo: getDetail('Tatuaje'),
        piercing: getDetail('Piercing'),
        nationality: getDetail('Nacionalidad'),
        languages: getDetail('Idiomas'),
        services: getDetail('Servicios'),
        availability: getDetail('Disponible para'),
        appointments: getDetail('Citas con'),
      };
    });
    await browser.close();
    return paramsData;
  }
}
