import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { BookmarksService } from './bookmarks.service';
import { CreateBookmarkDto } from './dto/create-bookmark.dto';
import { UpdateBookmarkDto } from './dto/update-bookmark.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { Bookmark } from './domain/bookmark';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllBookmarksDto } from './dto/find-all-bookmarks.dto';
import { plainToInstance } from 'class-transformer';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiTags('Bookmarks')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'bookmarks',
  version: '1',
})
export class BookmarksController {
  constructor(private readonly bookmarksService: BookmarksService) {}

  @Post()
  @ApiCreatedResponse({
    type: Bookmark,
  })
  create(@Body() createBookmarkDto: any) {
    const data = plainToInstance(CreateBookmarkDto, createBookmarkDto);
    return this.bookmarksService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(Bookmark),
  })
  async findAll(
    @Query() query: FindAllBookmarksDto,
  ): Promise<InfinityPaginationResponseDto<Bookmark>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.bookmarksService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: Bookmark,
  })
  async findAllWithoutPagination(): Promise<Bookmark[]> {
    return await this.bookmarksService.findAll();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: string) {
    return this.bookmarksService.findOne(id);
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('user/:userId')
  @ApiParam({
    name: 'userId',
    type: String,
    required: true,
  })
  findDataByUser(@Param('userId') userId: string) {
    return this.bookmarksService.findMany(userId);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: Bookmark,
  })
  update(
    @Param('id') id: string,
    @Body() updateBookmarkDto: UpdateBookmarkDto,
  ) {
    return this.bookmarksService.update(id, updateBookmarkDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.bookmarksService.remove(id);
  }
}
