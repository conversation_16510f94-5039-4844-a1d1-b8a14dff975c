import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';

@Entity({
  name: 'advert_status',
})
export class AdvertStatusEntity extends EntityRelationalHelper {
  @ApiResponseProperty({
    type: Number,
  })
  @PrimaryColumn()
  id: number;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  name: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: '#FFFFFF' })
  bcolor: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: '#000000' })
  fcolor: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: '#FF0000' })
  icolor: string;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
