import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindAllCacheDataDto {
  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 1))
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 50))
  @IsNumber()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsOptional()
  cacheDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  cacheKey?: string;

  @ApiPropertyOptional()
  @IsOptional()
  user?: string;
}

export const InFields = [''];

export const RelationalFields = ['user'];

export const DateFields = ['cacheDate'];

export const StringFields = ['description', 'cacheKey'];

export const NumberFields = [''];

export const BooleanFields = [''];
