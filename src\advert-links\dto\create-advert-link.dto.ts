import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { Advert } from '../../adverts/domain/advert';
import { rrssType } from '../../rrss-types/domain/rrss-type';

export class CreateAdvertLinkDto {
  @ApiProperty({ type: Advert })
  advert: Advert;

  @ApiProperty({ type: rrssType })
  rrssType: rrssType;

  @ApiProperty({ example: '', type: String })
  @IsNotEmpty()
  name: string;
}
