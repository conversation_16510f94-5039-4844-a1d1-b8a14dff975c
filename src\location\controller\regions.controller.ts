import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { RegionsService } from '../service/regions.service';
import { CreateRegionDto } from '../dto/create-region.dto';
import { UpdateRegionDto } from '../dto/update-region.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { Region } from '../domain/region';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../../utils/infinity-pagination';
import { FindAllRegionsDto } from '../dto/find-all-regions.dto';
import { plainToInstance } from 'class-transformer';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiTags('Regions')
@Controller({
  path: 'regions',
  version: '1',
})
export class RegionsController {
  constructor(private readonly regionsService: RegionsService) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiCreatedResponse({
    type: Region,
  })
  create(@Body() createRegionDto: any) {
    const data = plainToInstance(CreateRegionDto, createRegionDto);
    return this.regionsService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(Region),
  })
  async findAll(
    @Query() query: FindAllRegionsDto,
  ): Promise<InfinityPaginationResponseDto<Region>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.regionsService.findAllWithPagination(
        {
          paginationOptions: {
            page,
            limit,
          },
        },
        query,
      ),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: Region,
  })
  async findAllWithoutPagination(): Promise<Region[]> {
    return await this.regionsService.findAll();
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('allcount')
  async findAllCountWithoutPagination(): Promise<any[]> {
    return await this.regionsService.findAllAndCount();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.regionsService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  @ApiOkResponse({
    type: Region,
  })
  update(@Param('id') id: number, @Body() updateRegionDto: any) {
    const data = plainToInstance(UpdateRegionDto, updateRegionDto);

    return this.regionsService.update(id, data);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.regionsService.remove(id);
  }
}
