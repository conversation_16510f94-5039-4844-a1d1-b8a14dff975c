import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { RegionEntity } from './region.entity';
import { AdvertLocationEntity } from '../../../../../advert-locations/infrastructure/persistence/relational/entities/advert-location.entity';

@Entity({
  name: 'countries',
})
export class CountryEntity extends EntityRelationalHelper {
  @ApiResponseProperty({
    type: Number,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  name: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  code: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  slug: string;

  @OneToMany(() => RegionEntity, (region) => region.country, { cascade: true })
  regions: RegionEntity[];

  @OneToMany(
    () => AdvertLocationEntity,
    (advertLocation) => advertLocation.country,
  )
  advertLocations: AdvertLocationEntity[];

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
