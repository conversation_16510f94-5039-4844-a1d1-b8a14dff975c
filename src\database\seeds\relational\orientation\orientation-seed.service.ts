import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrientationEntity } from '../../../../orientations/infrastructure/persistence/relational/entities/orientation.entity';

@Injectable()
export class OrientationSeedService {
  constructor(
    @InjectRepository(OrientationEntity)
    private repository: Repository<OrientationEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ name: 'Heterosexual' }),
        this.repository.create({ name: 'Homosexual' }),
        this.repository.create({ name: 'Bisexual' }),
        this.repository.create({ name: 'Pansexual' }),
        this.repository.create({ name: 'Other' }),
      ]);
    }
  }
}
