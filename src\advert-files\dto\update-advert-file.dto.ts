import { PartialType } from '@nestjs/swagger';
import { ApiProperty } from '@nestjs/swagger';
import { CreateAdvertFileDto } from './create-advert-file.dto';
import { Advert } from '../../adverts/domain/advert';
import { Subscription } from '../../subscriptions/domain/subscription';

export class UpdateAdvertFileDto extends PartialType(CreateAdvertFileDto) {
  @ApiProperty({ type: Advert })
  advert: Advert;

  @ApiProperty({ type: Number })
  type: number;

  @ApiProperty({ type: String })
  mimeType: string;

  @ApiProperty({ type: String })
  file: string;

  @ApiProperty({ type: Boolean })
  main: boolean;

  @ApiProperty({ type: Boolean })
  validated: boolean;

  @ApiProperty({ type: Date })
  validatedAt: Date;

  @ApiProperty({ type: Subscription })
  subscription: Subscription;
}
