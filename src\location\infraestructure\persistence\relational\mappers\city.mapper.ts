import { City } from '../../../../domain/city';
import { CityEntity } from '../entities/city.entity';

export class CityMapper {
  static toDomain(raw: CityEntity): City {
    const domainEntity = new City();
    domainEntity.id = raw.id;
    domainEntity.name = raw.name;
    domainEntity.code = raw.code;
    domainEntity.slug = raw.slug;
    domainEntity.defaultLocation = raw.defaultLocation;
    domainEntity.region = raw.region;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: City): CityEntity {
    const persistenceEntity = new CityEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.name = domainEntity.name;
    persistenceEntity.code = domainEntity.code;
    persistenceEntity.slug = domainEntity.slug;
    persistenceEntity.defaultLocation = domainEntity.defaultLocation;
    persistenceEntity.region = domainEntity.region;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
