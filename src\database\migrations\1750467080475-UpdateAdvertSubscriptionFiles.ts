import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAdvertSubscriptionFiles1750467080475
  implements MigrationInterface
{
  name = 'UpdateAdvertSubscriptionFiles1750467080475';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."keytypeANDkeyvalue"`);
    await queryRunner.query(
      `ALTER TABLE "advert_file" ADD "subscriptionId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_file" ADD CONSTRAINT "FK_61ca53e43f2c71212a6dbd02bd7" FOREIGN KEY ("subscriptionId") REFERENCES "subscription"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "advert_file" DROP CONSTRAINT "FK_61ca53e43f2c71212a6dbd02bd7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_file" DROP COLUMN "subscriptionId"`,
    );
    await queryRunner.query(
      `CREATE INDEX "keytypeANDkeyvalue" ON "statistic" ("keyType", "keyValue") `,
    );
  }
}
