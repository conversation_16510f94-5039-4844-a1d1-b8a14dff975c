import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CitiesService } from '../service/cities.service';
import { CreateCityDto } from '../dto/create-city.dto';
import { UpdateCityDto } from '../dto/update-city.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { City } from '../domain/city';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../../utils/infinity-pagination';
import { FindAllCitiesDto } from '../dto/find-all-cities.dto';
import { plainToInstance } from 'class-transformer';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiTags('Cities')
@Controller({
  path: 'cities',
  version: '1',
})
export class CitiesController {
  constructor(private readonly citiesService: CitiesService) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiCreatedResponse({
    type: City,
  })
  create(@Body() createCityDto: any) {
    const data = plainToInstance(CreateCityDto, createCityDto);
    return this.citiesService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(City),
  })
  async findAll(
    @Query() query: FindAllCitiesDto,
  ): Promise<InfinityPaginationResponseDto<City>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.citiesService.findAllWithPagination(
        {
          paginationOptions: {
            page,
            limit,
          },
        },
        query,
      ),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: City,
  })
  async findAllWithoutPagination(): Promise<City[]> {
    return await this.citiesService.findAll();
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('allcount')
  async findAllCountWithoutPagination(): Promise<any[]> {
    return await this.citiesService.findAllAndCount();
  }

  @Get('allcount-region')
  @ApiParam({
    name: 'slug',
    type: String,
    required: true,
  })
  async findAllCountWithoutPaginationByRegion(
    @Query() query: any,
  ): Promise<any[]> {
    return await this.citiesService.findAllAndCountQuery(query);
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.citiesService.findOne(id);
  }

  @Get('slug/:slug')
  @ApiParam({
    name: 'slug',
    type: String,
    required: true,
  })
  findOneBySlug(@Param('slug') slug: string) {
    return this.citiesService.findOneBySlug(slug);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  @ApiOkResponse({
    type: City,
  })
  update(@Param('id') id: number, @Body() updateCityDto: any) {
    const data = plainToInstance(UpdateCityDto, updateCityDto);
    return this.citiesService.update(id, data);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.citiesService.remove(id);
  }
}
