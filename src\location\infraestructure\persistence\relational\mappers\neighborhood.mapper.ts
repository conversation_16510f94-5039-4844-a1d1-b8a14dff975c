import { BaseEntityMapper } from '../../../../../utils/mappers/base-entity-mapper';
import { Neighborhood } from '../../../../domain/neighborhood';
import { NeighborhoodEntity } from '../entities/neighborhood.entity';

export class NeighborhoodMapper {
  private static baseMapper = new (class extends BaseEntityMapper<
    Neighborhood,
    NeighborhoodEntity
  > {})();

  static toPersistence(domainEntity: Neighborhood): NeighborhoodEntity {
    const persistenceEntity = new NeighborhoodEntity();
    return this.baseMapper.mapToPersistenceCommon(
      domainEntity,
      persistenceEntity,
    );
  }

  static toDomain(raw: NeighborhoodEntity): Neighborhood {
    const domainEntity = new Neighborhood();
    return this.baseMapper.mapToDomainCommon(raw, domainEntity);
  }
}
