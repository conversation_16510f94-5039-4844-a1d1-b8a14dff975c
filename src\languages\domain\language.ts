import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';

const idType = Number;

export class Language {
  @ApiResponseProperty({
    type: idType,
  })
  id: number;

  @ApiResponseProperty({
    type: String,
    example: 'ES_ES',
  })
  code: string;

  @ApiResponseProperty({
    type: String,
    example: 'Spanish',
  })
  name: string;

  @ApiResponseProperty({
    type: String,
    example: 'Español',
  })
  cwCode: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
