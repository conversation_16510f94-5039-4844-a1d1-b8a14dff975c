import { Module } from '@nestjs/common';
import { EyeColorsService } from './eye-colors.service';
import { EyeColorsController } from './eye-colors.controller';
import { RelationalEyeColorPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [RelationalEyeColorPersistenceModule],
  controllers: [EyeColorsController],
  providers: [EyeColorsService],
  exports: [EyeColorsService, RelationalEyeColorPersistenceModule],
})
export class EyeColorsModule {}
