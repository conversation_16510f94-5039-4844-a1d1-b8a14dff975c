import { Modu<PERSON> } from '@nestjs/common';
import { EyeColorRepository } from '../eye-color.repository';
import { EyeColorRelationalRepository } from './repositories/eye-color.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EyeColorEntity } from './entities/eye-color.entity';

@Module({
  imports: [TypeOrmModule.forFeature([EyeColorEntity])],
  providers: [
    {
      provide: EyeColorRepository,
      useClass: EyeColorRelationalRepository,
    },
  ],
  exports: [EyeColorRepository],
})
export class RelationalEyeColorPersistenceModule {}
