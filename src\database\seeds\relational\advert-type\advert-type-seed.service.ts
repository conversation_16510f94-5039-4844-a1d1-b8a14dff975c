import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertTypeEntity } from '../../../../advert-types/infrastructure/persistence/relational/entities/advert-type.entity';

@Injectable()
export class AdvertTypeSeedService {
  constructor(
    @InjectRepository(AdvertTypeEntity)
    private repository: Repository<AdvertTypeEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ id: 1, name: 'ESCORT' }),
        this.repository.create({ id: 2, name: 'MASSAGE' }),
        this.repository.create({ id: 3, name: 'DOMINATION' }),
      ]);
    }
  }
}
