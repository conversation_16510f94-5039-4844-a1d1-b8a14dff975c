import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import * as puppeteer from 'puppeteer';
import { MediaUploadService } from '../cloudfare/video-upload.service';
import axios from 'axios';
import mime from 'mime-types';
import { extname } from 'path';
import { Readable } from 'stream';
import * as streamifier from 'streamifier';
import { fileType } from '../../advert-files/dto/advert-file-type.dto';
import { GoogleApiService } from '../google-api/google-api.service';
import moment from 'moment/moment';
import { CacheServiceHelper } from '../cache/cache.service';

@Injectable()
export class CrawlerService {
  constructor(
    private readonly datasource: DataSource,
    private readonly mediaUploadService: MediaUploadService,
    private readonly googleApiService: GoogleApiService,
    private readonly cacheServiceHelper: CacheServiceHelper,
  ) {}

  tServices = {
    '69': '69',
    analgiving: 'Dar sexo anal',
    analreceiving: 'Recibir sexo anal',
    cimcuminmouth: 'CIM (Eyacular en la boca)',
    cimwithcondom: 'CIM (con condón)',
    cobcumonbody: 'COB (Eyacular en el cuerpo)',
    couples: 'Parejas',
    crossdressing: 'Transvestismo',
    deepthroat: 'Boca profunda',
    escortfans: 'EscortFans',
    facials: 'Faciales',
    fingering: 'Dedos',
    footfetish: 'Fetiche de pies',
    frenchkissing: 'Beso francés',
    gfegirlfriendexperience: 'Experiencia de novia (GFE)',
    handjob: 'Masturbación manual',
    kissingnotongue: 'Besar (sin lengua)',
    lapdancing: 'Bailes en el regazo',
    nippleplay: 'Juegos con pezones',
    owooralwithoutcondom: 'OWO (Oral sin condón)',
    oralwithcondom: 'Oral (con condón)',
    psepornstarexperience: 'Experiencia de pornstar (PSE)',
    pegging: 'Pegging',
    phonesex: 'Sexo telefónico',
    reverseoral: 'Oral invertido',
    rimming: 'Beso negro',
    rimminggiving: 'Dar beso negro',
    rimmingreceiving: 'Recibir beso negro',
    roleplay: 'Juego de roles',
    russian: 'Rusa',
    sextoys: 'Juguetes sexuales',
    sexting: 'Sexting',
    sharedshowers: 'Duchas compartidas',
    showersandbathtubgames: 'Juegos en la ducha y la bañera',
    squirting: 'Eyaculación femenina',
    strapon: 'Strap-on',
    submissive: 'Sumisa',
    teabagging: 'Cajitas chinas',
    tieandtease: 'Atar y provocar',
    uniforms: 'Uniformes',
    watersports: 'Juegos de orina',
    antistressmassage: 'Masaje anti-estrés',
    aromatherapy: 'Aromaterapia',
    bodytobodymassage: 'Masaje corporal',
    clothedmassage: 'Masaje con ropa',
    craniosacraltherapy: 'Terapia craneosacral',
    deeptissuemassage: 'Masaje de tejido profundo',
    footmassage: 'Masaje de pies',
    fourhandsmassage: 'Masaje de cuatro manos',
    genitalmassage: 'Masaje genital',
    happyending: 'Final feliz',
    hotstonemassage: 'Masaje con piedras calientes',
    lomihawaiianmassage: 'Masaje Lomi hawaiano',
    lymphaticmassage: 'Masaje linfático',
    malegrooming: 'Aseo masculino',
    massage: 'Masaje',
    nakedmassage: 'Masaje desnudo',
    nurumassage: 'Masaje Nuru',
    prostatemassage: 'Masaje prostático',
    reflexology: 'Reflexología',
    shiatsu: 'Shiatsu',
    soapymassage: 'Masaje con jabón',
    sportsmassage: 'Masaje deportivo',
    swedishmassage: 'Masaje sueco',
    tantramassage: 'Masaje tantra',
    tantriclingammassage: 'Masaje Lingam tántrico',
    thaimassage: 'Masaje tailandés',
    turkishbathmassage: 'Masaje de baño turco',
    ultimateyonimassage: 'Masaje Yoni definitivo',
    bodyworship: 'Adoración corporal',
    bondage: 'Bondage',
    burn: 'Quemadura',
    cbtcockandballtorture: 'CBT (Tortura de pene y testículos)',
    canewhipping: 'Caño / Latigazo',
    chastitycontrol: 'Control de castidad',
    costumes: 'Disfraces',
    degradation: 'Degradación',
    dogtraining: 'Entrenamiento de perro',
    domination: 'Dominación',
    doubledomme: 'Doble dominadora',
    electrics: 'Electroestimulación',
    facesitting: 'Sentarse en la cara',
    faceslapping: 'Bofetada en la cara',
    feminisation: 'Feminización',
    fetishes: 'Fetiches',
    financialdomination: 'Dominación financiera',
    fisting: 'Fisting',
    flogging: 'Flagelación',
    gagshoods: 'Gag y capuchas',
    handcuffs: 'Esposas',
    humiliation: 'Humillación',
    inflictingpain: 'Infligir dolor',
    interrogations: 'Interrogatorios',
    latexpvc: 'Latex PVC',
    mummification: 'Momificación',
    peggingdomination: 'Dominación de Pegging',
    periodplay: 'Juego de la menstruación',
    sensorydeprivation: 'Privación sensorial',
    sissyfication: 'Sissyficación',
    slavesaccepted: 'Esclavos aceptados',
    smothering: 'Sofocación',
    spanking: 'Azotes',
    spitting: 'Escupir',
    training: 'Entrenamiento',
    trampling: 'Pisoteo',
    wrestling: 'Lucha',
  };

  async scrapeErosGuia(query: any): Promise<any> {
    const browser = await puppeteer.launch({ headless: false });
    const page = await browser.newPage();

    const urlTo = query?.url
      ? query.url
      : 'https://www.erosguia.com/escorts-madrid';

    const startSlice = query.startSlice ? query.startSlice : 1;
    const endSlice = query.endSlice ? query.endSlice : 100;

    // Navegar a la página principal
    await page.goto(urlTo, {
      waitUntil: 'networkidle2',
    });

    // Esperar a que se carguen los anuncios
    await page.waitForSelector('.group.flex.flex-col');

    // Extraer información de los anuncios (solo los primeros 5)
    const listings = await page.$$eval(
      '.group.flex.flex-col',
      (elements, start, end) => {
        return elements.slice(start, end).map((el) => {
          const profileUrl =
            el.querySelector('a.ficha-item')?.getAttribute('href') || 'Sin URL';
          const imageUrl =
            el.querySelector('img')?.getAttribute('src') || 'Sin imagen';
          const name =
            el.querySelector('.ad-name')?.textContent?.trim() || 'Sin nombre';
          const nationality =
            el.querySelector('.ad-nationality')?.textContent?.trim() ||
            'Sin nacionalidad';
          const age =
            el.querySelector('.text-yellow')?.textContent?.trim() || 'Sin edad';
          const whatsappLink =
            el.querySelector('.ficha-floating-btn')?.getAttribute('href') ||
            'Sin WhatsApp';

          return { name, nationality, age, profileUrl, imageUrl, whatsappLink };
        });
      },
      startSlice,
      endSlice,
    );

    await browser.close();

    const detailedListings: any = [];

    if (listings.length > 0) {
      for (const listing of listings) {
        const info = await this.scrapeDetailPage(listing.profileUrl);
        detailedListings.push(info);
      }

      return detailedListings;
    }

    return listings;
  }

  async scrapeDetailPage(url: string): Promise<any> {
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();

    await page.goto(url, { waitUntil: 'domcontentloaded' });

    await page.waitForSelector('.ficha-imagenes', { timeout: 60000 });

    const data = await page.evaluate(() => {
      const getText = (labelText: string): string => {
        const label = Array.from(
          document.querySelectorAll('.ficha-info .text-base.font-semibold'),
        ).find((el) => el.textContent?.includes(labelText));
        return label ? label.nextElementSibling?.textContent?.trim() ?? '' : '';
      };

      const name = document
        .querySelector('h1.title-ad span:first-child')
        ?.textContent?.trim();
      const profession = document
        .querySelector('h1.title-ad span:nth-child(2)')
        ?.textContent?.trim();
      const city = getText('Ciudad');
      const nationality = getText('Nacionalidad');
      const age = getText('Edad');
      const weight = getText('Peso');
      const height = getText('Estatura');
      const measurements = getText('Medidas');
      const languages = getText('Idiomas');

      // Obtener las aficiones
      const hobbiesElements = Array.from(
        document.querySelectorAll('.ficha-services .bg-red-light'),
      );
      const hobbies = hobbiesElements.map((el) => el.textContent?.trim());

      // Obtener la descripción de la escort
      const description = document
        .querySelector('.ficha-about .prose.text-justify')
        ?.textContent?.trim();

      // Fecha de publicación
      const postedOn = document
        .querySelector('.ficha-time')
        ?.textContent?.trim();

      // Obtener las imágenes
      const imageElements = Array.from(
        document.querySelectorAll('.ficha-imagenes img'),
      ) as HTMLImageElement[];
      const images = imageElements.map((img) => img.src);

      const videoElements = Array.from(
        document.querySelectorAll('.ficha-imagen.ficha-video video source'),
      ) as HTMLSourceElement[];
      const videos = videoElements.map((video) => video.src);

      const phoneNumberElement = document.querySelector(
        '.ficha-floating-call-btn span',
      );
      const whatsappLinkElement = document.querySelector(
        '.ficha-floating-whatsapp-btn',
      );

      // Comprobamos si los elementos existen y si es el tipo esperado
      // Comprobamos si el elemento de número de teléfono existe y si es un HTMLElement
      const phoneNumber =
        phoneNumberElement && phoneNumberElement instanceof HTMLElement
          ? phoneNumberElement.innerText
          : null;

      // Comprobamos si el elemento de WhatsApp es un HTMLAnchorElement antes de acceder a 'href'
      const whatsappLink =
        whatsappLinkElement && whatsappLinkElement instanceof HTMLAnchorElement
          ? whatsappLinkElement.href
          : null;

      return {
        name,
        profession,
        city,
        nationality,
        age,
        weight,
        height,
        measurements,
        languages,
        hobbies,
        description,
        postedOn,
        images,
        videos,
        phoneNumber,
        whatsappLink,
      };
    });

    await browser.close();

    const _advertId = await this.insertAdvertTMPData(data);
    if (_advertId) {
      const startDate = moment.utc().format('YYYY-MM-DD HH:mm:ss');
      const endDate = moment
        .utc()
        .add(3, 'months')
        .format('YYYY-MM-DD HH:mm:ss');
      const query = `INSERT INTO subscription ("startDate", "endDate", "status", "userId", "productId", "advertId") VALUES (
            '${startDate}',
            '${endDate}',
            'active',
            '162fff65-e440-402d-91eb-649400b79052',
            1,
            '${_advertId}'
          )`;
      await this.datasource.query(query);
      await this.cacheServiceHelper.resetCacheByAdvertId(`'${_advertId}'`);
    }

    return data;
  }

  async insertAdvertTMPData(data: any) {
    const measurements = this.extractMeasurements(data.measurements);
    const languages = this.extractLanguages(data.languages);
    const slug = this.generateSlug(data.name, data.phoneNumber);
    const timetable = {
      allDayTotal: true,
      schedules: [
        { label: 'sunday', notLabor: false, allDay: true, hours: [] },
        { label: 'monday', notLabor: false, allDay: true, hours: [] },
        { label: 'tuesday', notLabor: false, allDay: true, hours: [] },
        { label: 'wednesday', notLabor: false, allDay: true, hours: [] },
        { label: 'thursday', notLabor: false, allDay: true, hours: [] },
        { label: 'friday', notLabor: false, allDay: true, hours: [] },
        { label: 'saturday', notLabor: false, allDay: true, hours: [] },
      ],
    };
    const nationality = await this.getNationalityId(data.nationality);
    const lugar = data.city;
    const sinComa = lugar.replace(/,/g, '');
    const city = sinComa.trim();
    const rPhone =
      '+34' +
      (data.phoneNumber || '') // Si phoneNumber es undefined, usa una cadena vacía
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '') // Eliminamos puntos y comas también aquí
        .slice(0, 10);

    const searchForDuplicateAdvertId = await this.getAdvertId(slug);
    if (!searchForDuplicateAdvertId) {
      const query = `INSERT INTO advert (
        "profileName", "phoneNumber", "introduction",
        "height", "breastSize", "waist", "hips",
        "genderId", "advertTypeId",
        "chestSizeId",
        "nationalityId",
        "userId", "statusId",
        "smoke", "location", "email", "www", 
        "orientationId", "eyeColorId", "hairColorId", "raceId",
        "timetable", "featured", "rating", "birthDate", "whatsapp", "slug"
        ) VALUES (
          '${data.name}',
          '${rPhone}',
          '${data.description}', 
          '${this.extractNumber(data.height)}',
          ${measurements.breastSize},
          ${measurements.waist},
          ${measurements.hips},
          2, /* gender FEMALE */
          1, /* advertTypeId */
          4, /* chestSizeId NORMAL */
          ${nationality},
          '162fff65-e440-402d-91eb-649400b79052', /* userId */
          1, /* statusId 10:REVIEW ACTIVATION  1:active*/
          false, /* smoke FALSE */
          '${city}',
          '${slug}@escortshub.net', /* email */
          'www.escortshub.net/${slug}', /* www */
          5, /* orientationId OTHER */
          10, /* eyeColorId OTHER */
          11, /* hairColorId OTHER */
          8, /* raceId OTHER */
          '${JSON.stringify(timetable)}', /* timetable */
          false, /* featured */
          '0', /* rating */
          '${this.extractNumber(data.age)}',
          true, /* whatsapp */
          '${slug}'
        )`;
      await this.datasource.query(query);

      const advertId = await this.getAdvertId(slug);
      if (advertId) {
        // LOCATION
        const googleData = await this.googleApiService.getGeocodeByPlaceName(
          'es',
          city,
        );
        const locationData =
          await this.googleApiService.transformToLocation(googleData);
        if (locationData) {
          await this.insertAdvertLocation(
            advertId,
            city,
            locationData.placeId,
            locationData.neighborhoodId ?? 1,
            locationData.cityId,
            locationData.regionId,
          );
        }

        // LANGUAGES
        if (languages.length > 0) {
          for (const [, _lang] of languages.entries()) {
            const languageId = await this.getLanguageId(_lang);
            if (languageId) {
              await this.insertAdvertLanguage(advertId, languageId);
            }
          }
        } else {
          await this.insertAdvertLanguage(advertId, 13); // DEFAULT SPANISH
        }

        // Añadimos el servicio de EscortsHUB para cada tipo de servicio para garantizar la integridad de la base de datos
        await this.insertAdvertService(advertId, 1);
        await this.insertAdvertService(advertId, 2);
        await this.insertAdvertService(advertId, 3);
        if (data.hobbies.length > 0) {
          for (const [, _hobb] of data.hobbies.entries()) {
            const serviceSlug = this.findClosestMatches(_hobb, this.tServices);
            if (serviceSlug.length > 0) {
              for (const _serviceSlug of serviceSlug) {
                if (_serviceSlug) {
                  const serviceId = await this.getServiceId(_serviceSlug);
                  if (serviceId) {
                    if (!(await this.getAdvertService(advertId, serviceId))) {
                      await this.insertAdvertService(advertId, serviceId);
                    }
                  }
                }
              }
            } else {
              const slug = this.generateServiceSlug(_hobb);
              const serviceId = await this.getServiceId(slug);
              if (serviceId) {
                await this.insertAdvertService(advertId, serviceId);
              } else {
                const newService = await this.insertService(_hobb, slug);
                if (newService) {
                  await this.insertAdvertService(advertId, newService);
                  this.tServices[slug] = _hobb;
                }
              }
            }
          }
        }

        await this.insertAdvertRate(advertId, 1);

        for (const [index, _image] of data.images.entries()) {
          const isFirstElement = index === 0;
          const imageUploaded = await this.uploadImage(_image, slug);
          if (imageUploaded) {
            await this.insertAdvertFile(
              advertId,
              imageUploaded.uploadFile,
              imageUploaded.mimeType,
              isFirstElement,
              index,
              fileType.IMAGE,
            );
          }
        }

        for (const [index, _video] of data.videos.entries()) {
          const isFirstElement = index === 0;
          const videoUploaded = await this.uploadVideo(_video, slug);
          if (videoUploaded) {
            await this.insertAdvertFile(
              advertId,
              videoUploaded.uploadFile,
              videoUploaded.mimeType,
              isFirstElement,
              index,
              fileType.VIDEO,
            );
          }
        }

        return advertId;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  async insertAdvertFile(
    advertId: string,
    url: string,
    mimeType: string,
    mainFile: boolean,
    orderFile: number,
    fileType: number,
  ) {
    const query = `INSERT INTO advert_file ("order", "main", "validated", "advertId", "file", "type", "mimeType") VALUES (
          ${orderFile},
          ${mainFile},
          false, 
          '${advertId}',
          '${url}',
          ${fileType}, 
          '${mimeType}'
        )`;
    await this.datasource.query(query);
  }

  async insertAdvertLanguage(advertId: string, languageId: number) {
    const query = `INSERT INTO advert_languages ("advertId", "languageId") VALUES (
          '${advertId}',
          ${languageId} 
        )`;
    await this.datasource.query(query);
  }

  async insertAdvertService(advertId: string, serviceId: number) {
    const query = `INSERT INTO advert_services ("advertId", "serviceId") VALUES (
          '${advertId}',
          ${serviceId} 
        )`;
    await this.datasource.query(query);
  }

  async insertAdvertRate(advertId: string, rateTypeId: number) {
    const query = `INSERT INTO advert_rate ("advertId", "rateTypeId", "amount") VALUES (
          '${advertId}',
          ${rateTypeId},
          1 
        )`;
    await this.datasource.query(query);
  }

  async insertService(name: string, slug: string) {
    const query = `INSERT INTO service ("name", "serviceTypeId", "slug", "en", "es") VALUES (
          '${name}',
          99,
          '${slug}',
          '${name}',
          '${name}'
        )`;
    await this.datasource.query(query);
    return this.getServiceId(slug);
  }

  async insertAdvertLocation(
    advertId: string,
    cityName: string,
    cityUrl: string,
    neighborhoodId: string,
    cityId: number,
    regionId: number,
  ) {
    const query = `INSERT INTO advert_location ("name", "url", "advertId", "neighborhoodId", "cityId", "regionId", "countryId", "googleApiResponse") VALUES (
          '${cityName}',
          '${cityUrl}',
          '${advertId}',
          ${neighborhoodId},
          ${cityId},
          ${regionId},
          1, ' '
        )`;
    await this.datasource.query(query);
  }

  async getAdvertId(slug: string) {
    const query = `SELECT * FROM advert WHERE "slug" = '${slug}'`;
    const result = await this.datasource.query(query);
    if (result.length > 0) {
      return result[0].id;
    } else {
      return null;
    }
  }

  async getAdvertService(advertId: string, serviceId: number) {
    const query = `SELECT * FROM advert_services WHERE "advertId" = '${advertId}' AND "serviceId" = ${serviceId}`;
    const result = await this.datasource.query(query);
    return result.length > 0;
  }

  async getLanguageId(name: string) {
    const query = `SELECT * FROM language WHERE "cwCode" LIKE $1`;
    const values = [`%${name}%`];
    const result = await this.datasource.query(query, values);
    if (result.length > 0) {
      return result[0].id;
    } else {
      return null;
    }
  }

  async getServiceId(slug: string) {
    const query = `SELECT * FROM service WHERE "slug" LIKE $1`;
    const values = [`%${slug}%`];
    const result = await this.datasource.query(query, values);
    if (result.length > 0) {
      return result[0].id;
    } else {
      return null;
    }
  }

  async getNationalityId(name: string) {
    const query = `SELECT * FROM nationality WHERE "es" LIKE $1`;
    const values = [`%${name}%`];
    const result = await this.datasource.query(query, values);
    if (result.length > 0) {
      return result[0].id;
    } else {
      return 1;
    }
  }

  async getLocationIds(city: string) {
    const citySlug = (city || '') // Si profileName es undefined, usa una cadena vacía
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '');

    const query = `SELECT * FROM city WHERE "slug" LIKE $1`;
    const values = [`%${citySlug}%`]; // Agregamos los '%' para buscar coincidencias parciales
    const result = await this.datasource.query(query, values);
    if (result.length > 0) {
      return {
        defaultLocation: result[0].defaultLocation,
        cityId: result[0].id,
        regionId: result[0].region_id,
        countryId: 1,
      };
    } else {
      return {
        defaultLocation: null,
        cityId: null,
        regionId: null,
        countryId: null,
      };
    }
  }

  async uploadImage(imageUrl: string, slug: string) {
    let uploadFile = '';
    let mimeType = '';

    try {
      // Paso 1: Descargar la imagen como un buffer
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
      });
      const imageBuffer = Buffer.from(response.data);
      mimeType =
        response.headers['content-type'] ||
        mime.lookup(extname(imageUrl)) ||
        'application/octet-stream';

      if (!imageBuffer) {
        console.error('No se pudo convertir la imagen en buffer.');
      }

      // Paso 2: Obtener direct upload URL de Cloudflare
      const uploadURL = await this.mediaUploadService.getDirectUploadUrl();

      if (uploadURL) {
        // Paso 3: Subir la imagen a Cloudflare desde el buffer
        const uploadResult =
          await this.mediaUploadService.uploadImageToDirectUrl(
            uploadURL,
            imageBuffer, // Pasamos el buffer en vez de un archivo
            slug,
          );

        if (uploadResult) {
          uploadFile = uploadResult?.result?.variants[0];
        } else {
          uploadFile = '';
        }
      } else {
        console.error('Error al obtener la URL de upload Direct');
      }
    } catch (error) {
      console.error('Error al subir la imagen:', error);
    }
    return { uploadFile, mimeType };
  }

  async uploadVideo(videoUrl: string, slug: string) {
    let uploadFile = '';
    let mimeType = '';

    try {
      // Paso 1: Descargar la imagen como un buffer
      const response = await axios.get(videoUrl, {
        responseType: 'arraybuffer',
      });

      mimeType =
        response.headers['content-type'] ||
        mime.lookup(extname(videoUrl)) ||
        'application/octet-stream';

      const videoBuffer = Buffer.from(response.data);

      const videoStream: Readable = streamifier.createReadStream(videoBuffer);

      // Simular un objeto de tipo Express.Multer.File
      const videoFile: Express.Multer.File = {
        fieldname: 'video',
        originalname: `${slug}.mp4`,
        encoding: '7bit',
        mimetype: mimeType,
        buffer: videoBuffer,
        size: videoBuffer.length,
        destination: '',
        filename: '',
        path: '',
        stream: videoStream, // 🔹 Ahora stream es un Readable Stream válido
      };

      if (!videoFile) {
        console.error('No se pudo convertir el video en file.');
        return;
      }

      const uploadResult = await this.mediaUploadService.uploadVideo(videoFile);
      if (uploadResult) {
        uploadFile = uploadResult;
      } else {
        uploadFile = '';
      }
    } catch (error) {
      console.error('Error al subir el video:', error);
    }
    return { uploadFile, mimeType };
  }

  extractNumber(text: string): number {
    if (!text.trim()) return 0; // Si la cadena está vacía o solo tiene espacios, retorna 0

    const match = text.match(/\d+/); // Encuentra el primer número en la cadena
    return match ? parseInt(match[0], 10) : 0;
  }

  generateSlug(profileName: string, phoneNumber: string): string {
    const rName = (profileName || '') // Si profileName es undefined, usa una cadena vacía
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '') // Eliminamos puntos y comas también aquí
      .slice(0, 10);

    const rPhone = (phoneNumber || '') // Si phoneNumber es undefined, usa una cadena vacía
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '') // Eliminamos puntos y comas también aquí
      .slice(0, 10);

    return `${rName}-${rPhone}`.replace(/^-+|-+$/g, ''); // Elimina guiones al inicio o al final si los hay
  }

  generateServiceSlug(name: string): string {
    const rName = (name || '') // Si profileName es undefined, usa una cadena vacía
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '');

    return `${rName}`.replace(/^-+|-+$/g, '');
  }

  extractMeasurements(measurements: string): {
    breastSize: number;
    waist: number;
    hips: number;
  } {
    if (!measurements.trim()) return { breastSize: 0, waist: 0, hips: 0 }; // Si la cadena está vacía, retorna 0 en todo
    const numbers = measurements.match(/\d+/g)?.map(Number) || [0, 0, 0]; // Extrae los números y los convierte en array

    return {
      breastSize: numbers[0] || 0,
      waist: numbers[1] || 0,
      hips: numbers[2] || 0,
    };
  }

  extractLanguages(languages: string): string[] {
    return languages
      .replace(/.*?:\s*/, '')
      .split(', ')
      .map((language) => language.trim());
  }

  findClosestMatches(
    input: string,
    translations: Record<string, string>,
  ): string[] {
    const inputWords = input
      .toLowerCase()
      .split(/\s+/)
      .filter((word) => !['de', 'a', 'en', 'con', 'y'].includes(word));
    const results: { key: string; value: string; score: number }[] = [];

    for (const [key, value] of Object.entries(translations)) {
      const valueLower = value.toLowerCase();
      const matches = inputWords.filter((word) => valueLower.includes(word));
      const combinedSimilarity = matches.length / inputWords.length;
      if (combinedSimilarity > 0.3) {
        results.push({ key, value, score: combinedSimilarity });
      }
    }

    // Eliminar duplicados basándose en el valor
    const uniqueResults = Array.from(
      new Map(results.map((r) => [r.value, r])).values(),
    );

    uniqueResults.sort((a, b) => b.score - a.score);
    return results.slice(0, 3).map((result) => `${result.key}`);
  }
}
