import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ProductDetailSeedService } from './product-detail-seed.service';
import { ProductDetailEntity } from '../../../../products/infrastructure/persistence/relational/entities/product.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ProductDetailEntity])],
  providers: [ProductDetailSeedService],
  exports: [ProductDetailSeedService],
})
export class ProductDetailSeedModule {}
