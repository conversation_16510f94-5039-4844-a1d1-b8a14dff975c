import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { User } from '../../users/domain/user';
import { Advert } from '../../adverts/domain/advert';

export class CreateBookmarkDto {
  @ApiProperty({ example: '', type: String })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: Advert })
  advert: Advert;

  @ApiProperty({ type: User })
  user: User;
}
