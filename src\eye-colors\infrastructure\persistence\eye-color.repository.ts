import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { EyeColor } from '../../domain/eye-color';

export abstract class EyeColorRepository {
  abstract create(
    data: Omit<EyeColor, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<EyeColor>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<EyeColor[]>;

  abstract findAll(): Promise<EyeColor[]>;

  abstract findById(id: EyeColor['id']): Promise<NullableType<EyeColor>>;

  abstract update(
    id: EyeColor['id'],
    payload: DeepPartial<EyeColor>,
  ): Promise<EyeColor | null>;

  abstract remove(id: EyeColor['id']): Promise<void>;
}
