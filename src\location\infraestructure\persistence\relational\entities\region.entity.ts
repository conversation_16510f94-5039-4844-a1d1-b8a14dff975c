import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { CountryEntity } from './country.entity';
import { CityEntity } from './city.entity';
import { AdvertLocationEntity } from '../../../../../advert-locations/infrastructure/persistence/relational/entities/advert-location.entity';

@Entity({
  name: 'region',
})
export class RegionEntity extends EntityRelationalHelper {
  @ApiResponseProperty({ type: Number })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiResponseProperty({ type: String })
  @Column({ default: null })
  name: string;

  @ApiResponseProperty({ type: String })
  @Column({ default: null })
  code: string;

  @ApiResponseProperty({ type: String })
  @Column({ default: null })
  slug: string;

  @ManyToOne(() => CountryEntity, (country) => country.regions)
  @JoinColumn({ name: 'country_id' })
  country: CountryEntity;

  @Column('int', { name: 'country_id' })
  countryId: number;

  @OneToMany(() => CityEntity, (city) => city.region)
  cities: CityEntity[];

  @OneToMany(
    () => AdvertLocationEntity,
    (advertLocation) => advertLocation.region,
  )
  advertLocations: AdvertLocationEntity[];

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
