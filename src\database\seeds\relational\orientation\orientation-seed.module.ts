import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { OrientationSeedService } from './orientation-seed.service';
import { OrientationEntity } from '../../../../orientations/infrastructure/persistence/relational/entities/orientation.entity';

@Module({
  imports: [TypeOrmModule.forFeature([OrientationEntity])],
  providers: [OrientationSeedService],
  exports: [OrientationSeedService],
})
export class OrientationSeedModule {}
