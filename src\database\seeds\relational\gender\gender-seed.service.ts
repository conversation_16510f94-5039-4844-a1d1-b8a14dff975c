import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GenderEntity } from '../../../../genders/infrastructure/persistence/relational/entities/gender.entity';

@Injectable()
export class GenderSeedService {
  constructor(
    @InjectRepository(GenderEntity)
    private repository: Repository<GenderEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ id: 1, name: 'MALE' }),
        this.repository.create({ id: 2, name: 'FEMALE' }),
        this.repository.create({ id: 3, name: 'TRANS' }),
      ]);
    }
  }
}
