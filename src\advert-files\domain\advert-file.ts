import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { Advert } from '../../adverts/domain/advert';
import { Subscription } from '../../subscriptions/domain/subscription';

export class AdvertFile {
  @ApiProperty({ type: String })
  id: string;

  @ApiResponseProperty({ type: () => Advert })
  advert: Advert;

  @ApiProperty({ type: Number })
  type: number;

  @ApiProperty({ type: String })
  mimeType: string;

  @ApiProperty({ type: String })
  file: string;

  @ApiProperty({ type: Number })
  order: number;

  @ApiProperty({ type: Boolean })
  main: boolean;

  @ApiProperty({ type: Boolean })
  validated: boolean;

  @ApiProperty({ type: Date })
  validatedAt: Date;

  @ApiResponseProperty({ type: () => Subscription })
  subscription: Subscription;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
