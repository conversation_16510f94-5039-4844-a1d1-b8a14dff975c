import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { RegionEntity } from '../infraestructure/persistence/relational/entities/region.entity';

export class CreateCityDto {
  @ApiProperty({ type: String })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  code: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  slug: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  defaultLocation: string;

  @ApiResponseProperty({ type: RegionEntity })
  region: RegionEntity;
}
