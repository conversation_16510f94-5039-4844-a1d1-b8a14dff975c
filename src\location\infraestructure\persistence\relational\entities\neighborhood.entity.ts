import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { CityEntity } from './city.entity';
import { AdvertLocationEntity } from '../../../../../advert-locations/infrastructure/persistence/relational/entities/advert-location.entity';

@Entity({
  name: 'neighborhood',
})
export class NeighborhoodEntity extends EntityRelationalHelper {
  @ApiResponseProperty({
    type: Number,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  name: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  slug: string;

  @ManyToOne(() => CityEntity, (city) => city.neighborhoods)
  @JoinColumn({ name: 'city_id' })
  city: CityEntity;

  @Column('int', { name: 'city_id' })
  cityId: number;

  @OneToMany(
    () => AdvertLocationEntity,
    (advertLocation) => advertLocation.neighborhood,
  )
  advertLocations: AdvertLocationEntity[];

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
