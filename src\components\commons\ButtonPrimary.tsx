import { useTranslations } from 'next-intl';
import { ReactNode, ButtonHTMLAttributes } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  loading?: boolean;
  additionalClass?: string;
  disabled?: boolean;
  small?: boolean;
}

const Button = ({
  children,
  loading = false,
  disabled = false,
  small = false,
  additionalClass,
  ...props
}: ButtonProps) => {
  const tPrimaryButton = useTranslations('primary-button');

  return (
    <button
      {...props}
      disabled={disabled || loading}
      aria-disabled={disabled || loading}
      aria-busy={loading}
      className={`
            ${small ? 'w-[91px]' : 'w-full'}
        min-h-[48px]
        rounded-[8px]
        font-medium
        flex
        items-center
        justify-center
        relative
        transition-colors
        hover:opacity-80
        duration-200
        dark:bg-black
        disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed
        ${loading ? 'cursor-wait' : ''}
        ${additionalClass ?? 'text-white bg-[#F15C5C] hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'}
      `}
    >
      {loading ? (
        <>
          <svg
            className="w-6 h-6 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
            ></path>
          </svg>
          <span className={`sr-only`}>{tPrimaryButton('loading')}...</span>
        </>
      ) : (
        children
      )}
    </button>
  );
};

export default Button;
