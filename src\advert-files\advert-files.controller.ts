import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AdvertFilesService } from './advert-files.service';
import { CreateAdvertFileDto } from './dto/create-advert-file.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { AdvertFile } from './domain/advert-file';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import {
  FindAllAdvertFilesDto,
  FindAllAdvertFilesVideoDto,
} from './dto/find-all-advert-files.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiTags('Advertfiles')
@Controller({
  path: 'advert-files',
  version: '1',
})
export class AdvertFilesController {
  constructor(private readonly advert_filesService: AdvertFilesService) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiCreatedResponse({
    type: AdvertFile,
  })
  create(@Body() createAdvertFileDto: CreateAdvertFileDto) {
    return this.advert_filesService.create(createAdvertFileDto);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(AdvertFile),
  })
  async findAll(
    @Query() query: FindAllAdvertFilesDto,
  ): Promise<InfinityPaginationResponseDto<AdvertFile>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.advert_filesService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @Get('videos/allpaginated')
  @ApiOkResponse({
    type: InfinityPaginationResponse(AdvertFile),
  })
  async findAllVideos(
    @Query() query: FindAllAdvertFilesVideoDto,
  ): Promise<InfinityPaginationResponseDto<AdvertFile>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    const result = await this.advert_filesService.findAllVideosWithPagination(
      {
        paginationOptions: {
          page,
          limit,
        },
      },
      query,
    );

    return infinityPagination(result.data, { page, limit }, result.totalCount);
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: string) {
    return this.advert_filesService.findOne(id);
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('advert/:advertId')
  @ApiParam({
    name: 'advertId',
    type: String,
    required: true,
  })
  async findAdvertFilesByAdvert(
    @Param('advertId') advertId: string,
    @Body() updateAdvertFileDto: any,
  ) {
    return await this.advert_filesService.findMany(
      advertId,
      updateAdvertFileDto,
    );
  }

  @Get('advert/main-file/:advertId')
  @ApiParam({
    name: 'advertId',
    type: String,
    required: true,
  })
  findAdvertFilesByAdvertMain(@Param('advertId') advertId: string) {
    return this.advert_filesService.findManyMain(advertId);
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('reels/all')
  async findReelFiles() {
    return await this.advert_filesService.findReels();
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('videos/all')
  async findVideoFiles(@Query() query: FindAllAdvertFilesVideoDto) {
    return await this.advert_filesService.findVideos(query);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: AdvertFile,
  })
  update(@Param('id') id: string, @Body() updateAdvertFileDto: any) {
    return this.advert_filesService.update(id, updateAdvertFileDto);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.advert_filesService.remove(id);
  }

  /*
  @Post('upload/video/:advertId')
  @ApiParam({
    name: 'advertId',
    type: String,
    required: true,
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadVideo(
    @UploadedFile() file: Express.Multer.File,
    @Param('advertId') advertId: string,
  ): Promise<string> {
    return await this.mediaUploadService.uploadVideo(file);
  }
  */

  /*
  @Post('upload/image/:advertId')
  @ApiParam({
    name: 'advertId',
    type: String,
    required: true,
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(
    @UploadedFile() file: Express.Multer.File,
    @Param('advertId') advertId: string,
  ) {
    if (!file) {
      return { message: 'No se recibió archivo' };
    }

    // Paso 1: Obtener direct upload URL de Cloudflare
    const { uploadURL } = await this.mediaUploadService.getDirectUploadUrl();

    // Paso 2: Subir la imagen usando el direct upload URL
    const result = await this.mediaUploadService.uploadImageToDirectUrl(
      uploadURL,
      file.buffer,
      file.originalname,
    );

    return { message: 'Imagen subida exitosamente', result };
  }
 */

  @Post('upload/:advertId')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'advertId',
    type: String,
    required: true,
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Param('advertId') advertId: string,
    @Body() uploadAdvertFileDto: any,
  ): Promise<AdvertFile | null> {
    return await this.advert_filesService.upload(
      advertId,
      file,
      uploadAdvertFileDto,
    );
  }
}
