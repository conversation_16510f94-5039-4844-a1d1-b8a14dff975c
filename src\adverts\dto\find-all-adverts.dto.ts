import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindAllAdvertsDto {
  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 1))
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 50))
  @IsNumber()
  @IsOptional()
  limit?: number;

  @IsOptional()
  profileName?: string;

  @IsOptional()
  slug?: string;

  @IsOptional()
  phoneNumber: string;

  @IsOptional()
  whatsapp: boolean;

  @IsOptional()
  introduction: string;

  @IsOptional()
  gender: object;

  @IsOptional()
  advertType: number;

  @IsOptional()
  birthDate: number;

  @IsOptional()
  height: number;

  @IsOptional()
  breastSize: number;

  @IsOptional()
  chestSize: number;

  @IsOptional()
  waist: number; /* cintura */

  @IsOptional()
  hips: number;

  @IsOptional()
  orientation: number;

  @IsOptional()
  eyeColor: number;

  @IsOptional()
  hairColor: number;

  @IsOptional()
  race: number;

  @IsOptional()
  smoke: boolean;

  @IsOptional()
  location: string;

  @IsOptional()
  email: string;

  @IsOptional()
  www: string;

  @IsOptional()
  timetable: string;

  @IsOptional()
  nationality: object;

  @IsOptional()
  services: number;

  @IsOptional()
  languages: number;

  @IsOptional()
  status: number;

  @IsOptional()
  featured: boolean;

  @IsOptional()
  rating: number;

  @IsOptional()
  user: number;

  @IsOptional()
  advertFiles: number;

  @IsOptional()
  advertLinks: number;

  @IsOptional()
  advertRates: number;

  @IsOptional()
  advertLocations: number;

  @IsOptional()
  subscriptions: number;

  @IsOptional()
  oTopAdvert: boolean;
  @IsOptional()
  oDoubleAdvert: boolean;
  @IsOptional()
  oAvailableNow: boolean;
  @IsOptional()
  oReactivate: boolean;
  @IsOptional()
  oTopStories: boolean;
}

export const RelationalFields = [
  'gender',
  'advertType',
  'chestSize',
  'orientation',
  'eyeColor',
  'hairColor',
  'race',
  'nationality',
  'services',
  'languages',
  'status',
  'user',
  'advertFiles',
  'advertLinks',
  'advertRates',
  'advertLocations',
];

export const DateFields = [''];

export const StringFields = [
  'profileName',
  'phoneNumber',
  'introduction',
  'location',
  'email',
  'www',
  'timetable',
];

export const NumberFields = [
  'height',
  'breastSize',
  'waist',
  'hips',
  'rating',
  'birthDate',
];

export const BooleanFields = [
  'smoke',
  'featured',
  'oTopAdvert',
  'oTopDoubleAdvert',
  'oDoubleAdvert',
  'oAvailableNow',
  'oReactivate',
];
