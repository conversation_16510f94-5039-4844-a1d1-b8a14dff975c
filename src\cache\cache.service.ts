import { Injectable } from '@nestjs/common';
import { CreateCacheDataDto } from './dto/create-cache.dto';
import { UpdateCacheDataDto } from './dto/update-cache.dto';
import { CacheDataRepository } from './infrastructure/persistence/cache.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { CacheData } from './domain/cache';
import { FindAllCacheDataDto } from './dto/find-all-caches.dto';

@Injectable()
export class CacheDataService {
  constructor(private readonly cacheDataRepository: CacheDataRepository) {}

  create(createCacheDto: CreateCacheDataDto) {
    return this.cacheDataRepository.create(createCacheDto);
  }

  findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCacheDataDto,
  ) {
    return this.cacheDataRepository.findAllWithPagination(
      {
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      },
      filters,
    );
  }

  findOne(id: CacheData['id']) {
    return this.cacheDataRepository.findById(id);
  }

  findAllByUser(
    userId: string,
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCacheDataDto,
  ) {
    return this.cacheDataRepository.findAllByUser(
      userId,
      {
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      },
      filters,
    );
  }

  update(id: CacheData['id'], updateCacheDto: UpdateCacheDataDto) {
    return this.cacheDataRepository.update(id, updateCacheDto);
  }

  remove(id: CacheData['id']) {
    return this.cacheDataRepository.remove(id);
  }
}
