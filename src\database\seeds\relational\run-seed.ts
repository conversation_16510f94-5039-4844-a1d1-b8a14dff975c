import { NestFactory } from '@nestjs/core';
import { OfferPackSeedService } from './offer-pack/offer-pack-seed.service';
import { SeedModule } from './seed.module';

import { AdvertStatusSeedService } from './advert-status/advert-status-seed.service';
import { AdvertTypeSeedService } from './advert-type/advert-type-seed.service';
import { ChestSizeSeedService } from './chest-size/chest-size-seed.service';
import { EyeColorSeedService } from './eye-color/eye-color-seed.service';
import { GenderSeedService } from './gender/gender-seed.service';
import { HairColorSeedService } from './hair-color/hair-color-seed.service';
import { LanguageSeedService } from './language/language-seed.service';
import { NationalitySeedService } from './nationality/nationality-seed.service';
import { OrientationSeedService } from './orientation/orientation-seed.service';
import { ProductSeedService } from './product/product-seed.service';
import { ProductDetailSeedService } from './product-detail/product-detail-seed.service';
import { RaceSeedService } from './race/race-seed.service';
import { RateTypeSeedService } from './rate-type/rate-type-seed.service';
import { RoleSeedService } from './role/role-seed.service';
import { rrssTypeSeedService } from './rrss-type/rrss-type-seed.service';
import { ServiceSeedService } from './service/service-seed.service';
import { ServiceTypeSeedService } from './service-type/service-type-seed.service';
import { StatusSeedService } from './status/status-seed.service';
import { TransactionStatusSeedService } from './transaction-status/transaction-status-seed.service';
import { TranslationSeedService } from './translation/translation-seed.service';
import { UserSeedService } from './user/user-seed.service';
import { CountrySeedService } from './country/country-seed.service';
import { RegionSeedService } from './region/region-seed.service';
import { CitySeedService } from './city/city-seed.service';

const runSeed = async () => {
  const app = await NestFactory.create(SeedModule);
  await app.get(StatusSeedService).run();
  await app.get(RoleSeedService).run();
  await app.get(UserSeedService).run();
  await app.get(ServiceTypeSeedService).run();
  await app.get(TransactionStatusSeedService).run();
  await app.get(AdvertStatusSeedService).run();
  await app.get(AdvertTypeSeedService).run();
  await app.get(ChestSizeSeedService).run();
  await app.get(EyeColorSeedService).run();
  await app.get(GenderSeedService).run();
  await app.get(HairColorSeedService).run();
  await app.get(LanguageSeedService).run();
  await app.get(NationalitySeedService).run();
  await app.get(OrientationSeedService).run();
  await app.get(RaceSeedService).run();
  await app.get(RateTypeSeedService).run();
  await app.get(rrssTypeSeedService).run();
  await app.get(ServiceSeedService).run();
  await app.get(ProductSeedService).run();
  await app.get(ProductDetailSeedService).run();
  await app.get(TranslationSeedService).run();
  await app.get(CountrySeedService).run();
  await app.get(RegionSeedService).run();
  await app.get(CitySeedService).run();
  await app.get(OfferPackSeedService).run();

  await app.close();
};

void runSeed();
