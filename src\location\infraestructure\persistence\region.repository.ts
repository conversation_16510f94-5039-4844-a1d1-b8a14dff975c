import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { Region } from '../../domain/region';
import { FindAllRegionsDto } from '../../dto/find-all-regions.dto';

export abstract class RegionRepository {
  abstract create(
    data: Omit<Region, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Region>;

  abstract findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllRegionsDto,
  ): Promise<Region[]>;

  abstract findAll(): Promise<Region[]>;

  abstract findAllAndCount(): Promise<any>;

  abstract findById(id: Region['id']): Promise<NullableType<Region>>;

  abstract update(
    id: Region['id'],
    payload: DeepPartial<Region>,
  ): Promise<Region | null>;

  abstract remove(id: Region['id']): Promise<void>;
}
