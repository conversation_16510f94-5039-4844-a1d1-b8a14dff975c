---
to: src/<%= h.inflection.transform(name, ['pluralize', 'underscore', 'dasherize']) %>/dto/create-<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.dto.ts
---
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class Create<%= name %>Dto {
  @ApiProperty({ type: String })
  @IsNotEmpty()
  name: string;

  // ----- MORE FIELDS HERE -----

  @ApiProperty({ type: String })
  description: string;

  // ----------------------------
}
