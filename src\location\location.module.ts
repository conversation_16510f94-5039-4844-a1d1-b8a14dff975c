import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RelationalLocationPersistenceModule } from './infraestructure/persistence/relational/relational-persistence.module';
import { CountriesService } from './service/countries.service';
import { CountriesController } from './controller/countries.controller';
import { RegionsService } from './service/regions.service';
import { RegionsController } from './controller/regions.controller';
import { CitiesService } from './service/cities.service';
import { CitiesController } from './controller/ciites.controller';
import { NeighborhoodsService } from './service/neighborhoods.service';
import { NeighborhoodsController } from './controller/neighborhoods.controller';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Module({
  imports: [ConfigModule, RelationalLocationPersistenceModule],
  controllers: [
    CountriesController,
    RegionsController,
    CitiesController,
    NeighborhoodsController,
  ],
  providers: [
    CountriesService,
    RegionsService,
    CitiesService,
    NeighborhoodsService,
    CacheServiceHelper,
  ],
  exports: [
    CountriesService,
    RegionsService,
    CitiesService,
    NeighborhoodsService,
    RelationalLocationPersistenceModule,
    CacheServiceHelper,
  ],
})
export class LocationModule {}
