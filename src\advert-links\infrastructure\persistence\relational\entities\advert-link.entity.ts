import {
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { AdvertEntity } from '../../../../../adverts/infrastructure/persistence/relational/entities/advert.entity';
import { Advert } from '../../../../../adverts/domain/advert';
import { rrssTypeEntity } from '../../../../../rrss-types/infrastructure/persistence/relational/entities/rrss-type.entity';
import { rrssType } from '../../../../../rrss-types/domain/rrss-type';

@Entity({
  name: 'advert_link',
})
export class AdvertLinkEntity extends EntityRelationalHelper {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiResponseProperty({
    type: () => AdvertEntity,
  })
  @ManyToOne(() => AdvertEntity, (advert) => advert.advertLinks)
  @ApiProperty({ type: () => Advert })
  advert: Advert;

  @ApiResponseProperty({
    type: () => rrssTypeEntity,
  })
  @ManyToOne(() => rrssTypeEntity, {
    eager: true,
  })
  rrssType: rrssType;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  name: string;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
