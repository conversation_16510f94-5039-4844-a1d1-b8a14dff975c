import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RegionEntity } from '../../../../location/infraestructure/persistence/relational/entities/region.entity';

@Injectable()
export class RegionSeedService {
  constructor(
    @InjectRepository(RegionEntity)
    private repository: Repository<RegionEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({
          id: 1,
          name: 'Cataluña',
          country: { id: 1 },
          slug: `cataluna`,
          code: `CT`,
        }),
        this.repository.create({
          id: 2,
          name: `Comunidad de Madrid`,
          country: { id: 1 },
          slug: `madrid`,
          code: `MD`,
        }),
        this.repository.create({
          id: 3,
          name: `Andalucía`,
          country: { id: 1 },
          slug: `andalucia`,
          code: `AN`,
        }),
        this.repository.create({
          id: 4,
          name: `<PERSON><PERSON><PERSON>`,
          country: { id: 1 },
          slug: `aragon`,
          code: `AR`,
        }),
        this.repository.create({
          id: 5,
          name: `Asturias`,
          country: { id: 1 },
          slug: `asturias`,
          code: `O`,
        }),
        this.repository.create({
          id: 6,
          name: `Cantabria`,
          country: { id: 1 },
          slug: `cantabria`,
          code: `CB`,
        }),
        this.repository.create({
          id: 7,
          name: `Castilla-La Mancha`,
          country: { id: 1 },
          slug: `castillalamancha`,
          code: `CM`,
        }),
        this.repository.create({
          id: 8,
          name: `Castilla y León`,
          country: { id: 1 },
          slug: `castillaleon`,
          code: `CL`,
        }),
        this.repository.create({
          id: 9,
          name: `Canarias`,
          country: { id: 1 },
          slug: `canarias`,
          code: `CN`,
        }),
        this.repository.create({
          id: 10,
          name: `Ceuta`,
          country: { id: 1 },
          slug: `ceuta`,
          code: `CE`,
        }),
        this.repository.create({
          id: 11,
          name: `Comunidad Valenciana`,
          country: { id: 1 },
          slug: `valencia`,
          code: `VC`,
        }),
        this.repository.create({
          id: 12,
          name: `Extremadura`,
          country: { id: 1 },
          slug: `extremadura`,
          code: `EX`,
        }),
        this.repository.create({
          id: 13,
          name: `Galicia`,
          country: { id: 1 },
          slug: `galicia`,
          code: `GA`,
        }),
        this.repository.create({
          id: 14,
          name: `La Rioja`,
          country: { id: 1 },
          slug: `rioja`,
          code: `RI`,
        }),
        this.repository.create({
          id: 15,
          name: `Melilla`,
          country: { id: 1 },
          slug: `melilla`,
          code: `ML`,
        }),
        this.repository.create({
          id: 16,
          name: `Región de Murcia`,
          country: { id: 1 },
          slug: `murcia`,
          code: `MC`,
        }),
        this.repository.create({
          id: 17,
          name: `Navarra`,
          country: { id: 1 },
          slug: `navarra`,
          code: `NC`,
        }),
        this.repository.create({
          id: 18,
          name: `País Vasco`,
          country: { id: 1 },
          slug: `paisvasco`,
          code: `PV`,
        }),
        this.repository.create({
          id: 19,
          name: `Islas Baleares`,
          country: { id: 1 },
          slug: `islasbaleares`,
          code: `PM`,
        }),
        this.repository.create({
          id: 20,
          name: `Catalunya`,
          country: { id: 1 },
          slug: `catalunya`,
          code: ``,
        }),
      ]);
    }
  }
}
