---
to: src/<%= h.inflection.transform(name, ['pluralize', 'underscore', 'dasherize']) %>/infrastructure/persistence/relational/repositories/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.repository.ts
---
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <%= name %>Entity } from '../entities/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { <%= name %> } from '../../../../domain/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>';
import { <%= name %>Repository } from '../../<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.repository';
import { <%= name %>Mapper } from '../mappers/<%= h.inflection.transform(name, ['underscore', 'dasherize']) %>.mapper';
import {
  FindAllDto,
  BooleanFields,
  DateFields,
  EvaluateDateFields,
  NumberFields,
  RelationalFields,
  StringFields,
  InFields,
} from '../../../../dto/find-all-<%= h.inflection.transform(name, ['underscore', 'dasherize', 'pluralize']) %>.dto';
import { applyFilters } from '../../../../../utils/query-builder/query-builder-filters';

@Injectable()
export class <%= name %>RelationalRepository implements <%= name %>Repository {
  constructor(
    @InjectRepository(<%= name %>Entity)
    private readonly <%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository: Repository<<%= name %>Entity>,
  ) {}

  async create(data: <%= name %>): Promise<<%= name %>> {
    const persistenceModel = <%= name %>Mapper.toPersistence(data);
    const newEntity = await this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.save(
      this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.create(persistenceModel),
    );
    return <%= name %>Mapper.toDomain(newEntity);
  }

  async findAll(
    queryFilters: FindAllDto,
    pagination: boolean = true,
  ): Promise<any> {
    const { page = 1, limit = 50, ...filters } = queryFilters;

    const sanitizedLimit = Math.min(Number(limit) || 50, 50);
    const sanitizedPage = Number(page) || 1;

    const query = this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.createQueryBuilder('<%= name %>');

    applyFilters(query, '<%= name %>', filters, {
      inFields: InFields,
      relationalFields: RelationalFields,
      stringFields: StringFields,
      numberFields: NumberFields,
      booleanFields: BooleanFields,
      dateFields: DateFields,
      evaluateDateFields: EvaluateDateFields,
    });

    if (pagination) {
      const [, totalCount] = await query.getManyAndCount();

      query.skip((sanitizedPage - 1) * sanitizedLimit).take(sanitizedLimit);

      const results = await query.getMany();

      return {
        totalCount: totalCount,
        data: results.map((r) => <%= name %>Mapper.toDomain(r)),
      };
    } else {
      const [results, totalCount] = await query.getManyAndCount();

      return {
        totalCount: totalCount,
        data: results.map((r) => <%= name %>Mapper.toDomain(r)),
      };
    }
  }

  async findById(id: <%= name %>['id']): Promise<NullableType<<%= name %>>> {
    const entity = await this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.findOne({
      where: { id },
    });

    return entity ? <%= name %>Mapper.toDomain(entity) : null;
  }

  async update(id: <%= name %>['id'], payload: Partial<<%= name %>>): Promise<<%= name %>> {
    const entity = await this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.save(
      this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.create(
        <%= name %>Mapper.toPersistence({
          ...<%= name %>Mapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return <%= name %>Mapper.toDomain(updatedEntity);
  }

  async remove(id: <%= name %>['id']): Promise<void> {
    await this.<%= h.inflection.transform(name, ['camelize', 'underscore']) %>Repository.softDelete(id);
  }
}
