import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AdvertTypeSeedService } from './advert-type-seed.service';
import { AdvertTypeEntity } from '../../../../advert-types/infrastructure/persistence/relational/entities/advert-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertTypeEntity])],
  providers: [AdvertTypeSeedService],
  exports: [AdvertTypeSeedService],
})
export class AdvertTypeSeedModule {}
