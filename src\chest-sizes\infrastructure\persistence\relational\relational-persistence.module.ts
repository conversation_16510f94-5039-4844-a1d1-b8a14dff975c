import { Module } from '@nestjs/common';
import { ChestSizeRepository } from '../chest-size.repository';
import { ChestSizeRelationalRepository } from './repositories/chest-size.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChestSizeEntity } from './entities/chest-size.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ChestSizeEntity])],
  providers: [
    {
      provide: ChestSizeRepository,
      useClass: ChestSizeRelationalRepository,
    },
  ],
  exports: [ChestSizeRepository],
})
export class RelationalChestSizePersistenceModule {}
