import Link from 'next/link';
import { getTranslations } from 'next-intl/server';
import LoginForm from '@/components/forms/auth/Login';

export async function generateMetadata({
  params,
}: {
  params: { locale: string };
}) {
  const t = await getTranslations({ locale: params.locale, namespace: 'auth' });
  return {
    title: t('login-title'),
    description: t('no-account'),
    robots: {
      index: false,
      follow: true,
    },
    alternates: {
      canonical: '/sign-in',
    },
  };
}

export default async function SignInPage() {
  const t = await getTranslations('auth');

  return (
    <div className="w-full max-w-md">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900">{t('login-title')}</h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('no-account')}{' '}
          <Link
            href="/sign-up"
            className="font-medium text-[#F15C5C] hover:text-red-500"
          >
            {t('register-now')}
          </Link>
        </p>
      </div>
      <LoginForm />
    </div>
  );
}
