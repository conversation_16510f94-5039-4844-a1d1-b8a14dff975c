import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OfferPackEntity } from '../../../../offer-packs/infrastructure/persistence/relational/entities/offer-pack.entity';
import { OfferPackDetailEntity } from '../../../../offer-pack-details/infrastructure/persistence/relational/entities/offer-pack-detail.entity';
import { OfferPackSeedService } from './offer-pack-seed.service';

@Module({
  imports: [TypeOrmModule.forFeature([OfferPackEntity, OfferPackDetailEntity])],
  providers: [OfferPackSeedService],
  exports: [OfferPackSeedService],
})
export class OfferPackSeedModule {}
