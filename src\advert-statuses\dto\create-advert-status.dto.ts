import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateAdvertStatusDto {
  @ApiProperty({ type: Number })
  @IsNotEmpty()
  id: number;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  bcolor: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  fcolor: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  icolor: string;
}
