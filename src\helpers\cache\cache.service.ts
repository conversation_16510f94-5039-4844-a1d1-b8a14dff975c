import { Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CacheServiceHelper {
  constructor(
    private readonly datasource: DataSource,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  addUsedCacheKey(description: string, cacheKey: string, userId: string) {
    const queryCache = `INSERT INTO cache ("cacheDate", "description", "cacheKey", "userId") VALUES (
      NOW(),
      '${description}',
      '${cacheKey}',
      '${userId}'
    )`;
    void this.datasource.query(queryCache);
  }

  async resetCacheByAdvertId(_id: string) {
    console.error('ADVERTS-RESET-CACHE-BY-ADVERT-ID', _id);
    const itemQuery = `
        SELECT advert."id" as advertId, 
               advert."slug" as advertSlug, 
               advert."userId" as userId, 
               city."slug" as citySlug 
          FROM advert INNER JOIN "user" ON (advert."userId" = "user"."id")
                      INNER JOIN advert_location ON (advert.id = advert_location."advertId")
                      INNER JOIN city ON (advert_location."cityId" = "city"."id")
         WHERE advert."id" = ${_id}`;
    const itemAdvert = await this.datasource.query(itemQuery);

    const allKeys = [
      'CACHE-ADVERT-ALL-SITE-MAP',
      'CACHE-ADVERT-FILES-REELS-ALL',
      'CACHE-ADVERT-FILES-VIDEOS-ALL',
      'CACHE-CITIES-ALLCOUNT',
      'CACHE-REGIONS-ALLCOUNT',
      'CACHE-ADVERT-FILES-VIDEOS-ALL-PAGE-1-LIMIT-8',
    ];
    for (const key of allKeys) {
      await this.cacheService.del(key);
    }

    const allKeysWithPage = [
      'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20',
      'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-ORDERBY-updatedAt-DESC',
      'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-ORDERBY-oTopAdvert-DESC-oTopDoubleAdvert-DESC-oDoubleAdvert-DESC-oAvailableNow-DESC-oReactivate-DESC-updatedAt-DESC',
      'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-ADVERTSTATUS-1',
      'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-ADVERTSTATUS-1-ORDERBY-updatedAt-DESC',
      'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-ADVERTSTATUS-1-ORDERBY-oTopAdvert-DESC-oTopDoubleAdvert-DESC-oDoubleAdvert-DESC-oAvailableNow-DESC-oReactivate-DESC-updatedAt-DESC',
    ];
    for (const key of allKeysWithPage) {
      for (let i = 1; i <= 100; i++) {
        await this.cacheService.del(key.replace('###PAGE###', i.toString()));
      }
    }
    if (itemAdvert) {
      const _advertId = itemAdvert[0]?.advertid;
      const _slug = itemAdvert[0]?.advertslug || '';
      const _userId = itemAdvert[0]?.userid || '';
      const _citySlug = itemAdvert[0]?.cityslug.toLowerCase() || '';
      if (_advertId !== '') {
        const idKeys = ['CACHE-ADVERT-BY-ID-###ID###'];
        for (const key of idKeys) {
          const _tmpKey = key.replace('###ID###', _advertId);
          await this.cacheService.del(_tmpKey);
        }
      }
      if (_slug !== '') {
        const slugKeys = ['CACHE-ADVERT-BY-SLUG-###SLUG###'];
        for (const key of slugKeys) {
          const _tmpKey = key.replace('###SLUG###', _slug);
          await this.cacheService.del(_tmpKey);
        }
      }
      if (_userId !== '') {
        const userIdKeys = [
          'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-USER-###USER###-ORDERBY-updatedAt-DESC',
          'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-USER-###USER###-ORDERBY-oTopAdvert-DESC-oTopDoubleAdvert-DESC-oDoubleAdvert-DESC-oAvailableNow-DESC-oReactivate-DESC-updatedAt-DESC',
        ];
        for (const key of userIdKeys) {
          const _tmpKey = key.replace('###USER###', _userId);
          for (let i = 1; i <= 100; i++) {
            await this.cacheService.del(
              _tmpKey.replace('###PAGE###', i.toString()),
            );
          }
        }
      }
      if (_citySlug !== '') {
        const citySlugKeys = [
          'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-ADVERTSTATUS-1-CITYSLUG-###CITYSLUG###',
          'CACHE-LOCATION-HOME-PAGE-###PAGE###-LIMIT-20-ADVERTSTATUS-1-CITYSLUG-###CITYSLUG###-ORDERBY-oTopAdvert-DESC-oTopDoubleAdvert-DESC-oDoubleAdvert-DESC-oAvailableNow-DESC-oReactivate-DESC-updatedAt-DESC',
        ];
        for (const key of citySlugKeys) {
          const _tmpKey = key.replace('###CITYSLUG###', _citySlug);
          for (let i = 1; i <= 100; i++) {
            await this.cacheService.del(
              _tmpKey.replace('###PAGE###', i.toString()),
            );
          }
        }
      }
    }
  }
}
