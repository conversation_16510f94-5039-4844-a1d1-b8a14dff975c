import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { City } from '../../domain/city';
import { FindAllCitiesDto } from '../../dto/find-all-cities.dto';

export abstract class CityRepository {
  abstract create(
    data: Omit<City, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<City>;

  abstract findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCitiesDto,
  ): Promise<City[]>;

  abstract findAll(): Promise<City[]>;

  abstract findAllAndCount(): Promise<any>;

  abstract findAllAndCountQuery(queryParams: any): Promise<any>;

  abstract findById(id: City['id']): Promise<NullableType<City>>;

  abstract findBySlug(slug: City['slug']): Promise<NullableType<City>>;

  abstract update(
    id: City['id'],
    payload: DeepPartial<City>,
  ): Promise<City | null>;

  abstract remove(id: City['id']): Promise<void>;
}
