import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindAllAdvertLocationsDto {
  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 1))
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 50))
  @IsNumber()
  @IsOptional()
  limit?: number;

  @IsOptional()
  name?: string;

  @IsOptional()
  url?: string;

  @IsOptional()
  neighborhood: number;

  @IsOptional()
  neighborhoodSlug: string;

  @IsOptional()
  city: number;

  @IsOptional()
  citySlug: string;

  @IsOptional()
  region: number;

  @IsOptional()
  regionSlug: string;

  @IsOptional()
  country: number;

  @IsOptional()
  countrySlug: string;

  @IsOptional()
  advert: number;

  @IsOptional()
  advertType: number;

  @IsOptional()
  status: number;

  @IsOptional()
  user: string;

  @IsOptional()
  featured: number;

  @IsOptional()
  profileName: string;

  @IsOptional()
  advertSlug: string;

  @IsOptional()
  service: number;

  @IsOptional()
  serviceSlug: string;

  @IsOptional()
  orders: Array<{ field: string; direction: 'ASC' | 'DESC' }>;

  @IsOptional()
  oTopAdvert: boolean;
  @IsOptional()
  oTopDoubleAdvert: boolean;
  @IsOptional()
  oDoubleAdvert: boolean;
  @IsOptional()
  oAvailableNow: boolean;
  @IsOptional()
  oReactivate: boolean;
  @IsOptional()
  oTopStories: boolean;
}

export const BooleanFields = [
  'oTopAdvert',
  'oTopDoubleAdvert',
  'oDoubleAdvert',
  'oAvailableNow',
  'oReactivate',
  'oTopStories',
];
export const StringFields = ['name', 'url', 'profileName', 'advertSlug'];
export const RelationalFields = [
  'neighborhood',
  'city',
  'region',
  'country',
  'advert',
  'advertType',
  'status',
  'service',
];
export const NumberFields = ['featured'];
export const DateFields = [''];
