import { AdvertStatus } from '../../../../domain/advert-status';
import { AdvertStatusEntity } from '../entities/advert-status.entity';

export class AdvertStatusMapper {
  static toDomain(raw: AdvertStatusEntity): AdvertStatus {
    const domainEntity = new AdvertStatus();
    domainEntity.id = raw.id;
    domainEntity.name = raw.name;
    domainEntity.bcolor = raw.bcolor;
    domainEntity.fcolor = raw.fcolor;
    domainEntity.icolor = raw.icolor;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: AdvertStatus): AdvertStatusEntity {
    const persistenceEntity = new AdvertStatusEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.name = domainEntity.name;
    persistenceEntity.bcolor = domainEntity.bcolor;
    persistenceEntity.fcolor = domainEntity.fcolor;
    persistenceEntity.icolor = domainEntity.icolor;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
