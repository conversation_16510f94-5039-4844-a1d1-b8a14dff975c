import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { Neighborhood } from '../../domain/neighborhood';
import { FindAllNeighborhoodsDto } from '../../dto/find-all-neighborhoods.dto';

export abstract class NeighborhoodRepository {
  abstract create(
    data: Omit<Neighborhood, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Neighborhood>;

  abstract findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllNeighborhoodsDto,
  ): Promise<Neighborhood[]>;

  abstract findAll(): Promise<Neighborhood[]>;

  abstract findAllAndCount(): Promise<any>;

  abstract findAllAndCountByCity(cityId: string): Promise<any>;

  abstract findById(
    id: Neighborhood['id'],
  ): Promise<NullableType<Neighborhood>>;

  abstract update(
    id: Neighborhood['id'],
    payload: DeepPartial<Neighborhood>,
  ): Promise<Neighborhood | null>;

  abstract remove(id: Neighborhood['id']): Promise<void>;
}
