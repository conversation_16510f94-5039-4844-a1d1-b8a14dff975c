import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';

export class Country {
  @ApiResponseProperty({
    type: Number,
  })
  id: number;

  @ApiResponseProperty({
    type: String,
    example: 'Spain',
  })
  name: string;

  @ApiResponseProperty({
    type: String,
    example: 'ES',
  })
  code: string;

  @ApiResponseProperty({
    type: String,
  })
  slug: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
