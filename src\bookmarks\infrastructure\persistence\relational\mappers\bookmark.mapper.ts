import { Bookmark } from '../../../../domain/bookmark';
import { BookmarkEntity } from '../entities/bookmark.entity';

export class BookmarkMapper {
  static toDomain(raw: BookmarkEntity): Bookmark {
    const domainEntity = new Bookmark();
    domainEntity.id = raw.id;
    domainEntity.name = raw.name;
    domainEntity.user = raw.user;
    domainEntity.advert = raw.advert;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: Bookmark): BookmarkEntity {
    const persistenceEntity = new BookmarkEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.name = domainEntity.name;
    persistenceEntity.user = domainEntity.user;
    persistenceEntity.advert = domainEntity.advert;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
