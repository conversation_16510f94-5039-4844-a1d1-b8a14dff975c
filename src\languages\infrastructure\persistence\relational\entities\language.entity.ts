import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { AdvertEntity } from '../../../../../adverts/infrastructure/persistence/relational/entities/advert.entity';

@Entity({
  name: 'language',
})
export class LanguageEntity extends EntityRelationalHelper {
  @ApiResponseProperty({
    type: Number,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ type: String, nullable: true })
  code: string;

  @ApiResponseProperty({
    type: String,
  })
  @Index()
  @Column({ type: String, nullable: true })
  name: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ type: String, nullable: true })
  cwCode: string;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;

  @ManyToMany(() => AdvertEntity, (advert) => advert.languages)
  adverts: AdvertEntity[];
}
