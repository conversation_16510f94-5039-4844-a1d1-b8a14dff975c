import { Injectable } from '@nestjs/common';
import { CreateAdvertStatusDto } from './dto/create-advert-status.dto';
import { UpdateAdvertStatusDto } from './dto/update-advert-status.dto';
import { AdvertStatusRepository } from './infrastructure/persistence/advert-status.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { AdvertStatus } from './domain/advert-status';

@Injectable()
export class AdvertStatusesService {
  constructor(
    private readonly advert_statusRepository: AdvertStatusRepository,
  ) {}

  create(createAdvertStatusDto: CreateAdvertStatusDto) {
    return this.advert_statusRepository.create(createAdvertStatusDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.advert_statusRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  findOne(id: AdvertStatus['id']) {
    return this.advert_statusRepository.findById(id);
  }

  update(id: AdvertStatus['id'], updateAdvertStatusDto: UpdateAdvertStatusDto) {
    return this.advert_statusRepository.update(id, updateAdvertStatusDto);
  }

  remove(id: AdvertStatus['id']) {
    return this.advert_statusRepository.remove(id);
  }
}
