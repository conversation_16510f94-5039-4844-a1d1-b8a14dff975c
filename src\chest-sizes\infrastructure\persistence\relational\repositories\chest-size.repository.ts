import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChestSizeEntity } from '../entities/chest-size.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { ChestSize } from '../../../../domain/chest-size';
import { ChestSizeRepository } from '../../chest-size.repository';
import { ChestSizeMapper } from '../mappers/chest-size.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class ChestSizeRelationalRepository implements ChestSizeRepository {
  constructor(
    @InjectRepository(ChestSizeEntity)
    private readonly chest_sizeRepository: Repository<ChestSizeEntity>,
  ) {}

  async create(data: ChestSize): Promise<ChestSize> {
    const persistenceModel = ChestSizeMapper.toPersistence(data);
    const newEntity = await this.chest_sizeRepository.save(
      this.chest_sizeRepository.create(persistenceModel),
    );
    return ChestSizeMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<ChestSize[]> {
    const entities = await this.chest_sizeRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((user) => ChestSizeMapper.toDomain(user));
  }

  async findAll(): Promise<ChestSize[]> {
    const entities = await this.chest_sizeRepository.find();

    return entities.map((user) => ChestSizeMapper.toDomain(user));
  }

  async findById(id: ChestSize['id']): Promise<NullableType<ChestSize>> {
    const entity = await this.chest_sizeRepository.findOne({
      where: { id },
    });

    return entity ? ChestSizeMapper.toDomain(entity) : null;
  }

  async update(
    id: ChestSize['id'],
    payload: Partial<ChestSize>,
  ): Promise<ChestSize> {
    const entity = await this.chest_sizeRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.chest_sizeRepository.save(
      this.chest_sizeRepository.create(
        ChestSizeMapper.toPersistence({
          ...ChestSizeMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return ChestSizeMapper.toDomain(updatedEntity);
  }

  async remove(id: ChestSize['id']): Promise<void> {
    await this.chest_sizeRepository.softDelete(id);
  }
}
