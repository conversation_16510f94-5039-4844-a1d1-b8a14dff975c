import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HairColorEntity } from '../../../../hair-colors/infrastructure/persistence/relational/entities/hair-color.entity';

@Injectable()
export class HairColorSeedService {
  constructor(
    @InjectRepository(HairColorEntity)
    private repository: Repository<HairColorEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON> Brown' }),
        this.repository.create({ name: '<PERSON>lon<PERSON>' }),
        this.repository.create({ name: 'Platinum Blonde' }),
        this.repository.create({ name: 'Strawberry Blonde' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: '<PERSON>' }),
        this.repository.create({ name: 'Dyed' }),
        this.repository.create({ name: 'Other' }),
      ]);
    }
  }
}
