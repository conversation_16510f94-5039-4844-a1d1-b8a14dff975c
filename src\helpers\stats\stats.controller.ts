import { Controller, Get, Query } from '@nestjs/common';
import { StatsService } from './stats.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Stats')
@Controller({
  path: 'stats',
  version: '1',
})
export class StatsController {
  constructor(private readonly statsService: StatsService) {}

  @Get()
  addAdvertVisit(@Query() query: any) {
    if (query?.type && query?.slug) {
      this.statsService.addAdvertVisit(query?.type, query?.slug);
    }
  }
}
