import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LanguageEntity } from '../../../../languages/infrastructure/persistence/relational/entities/language.entity';

@Injectable()
export class LanguageSeedService {
  constructor(
    @InjectRepository(LanguageEntity)
    private repository: Repository<LanguageEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ code: 'Catalan', name: 'Catalan' }),
        this.repository.create({ code: 'Chinese', name: 'Chinese' }),
        this.repository.create({ code: 'Croatian', name: 'Croatian' }),
        this.repository.create({ code: 'English', name: 'English' }),
        this.repository.create({ code: 'French', name: 'French' }),
        this.repository.create({ code: 'German', name: 'German' }),
        this.repository.create({ code: 'Guarani', name: '<PERSON><PERSON>rani' }),
        this.repository.create({ code: 'Italian', name: 'Italian' }),
        this.repository.create({ code: 'Japanese', name: 'Japanese' }),
        this.repository.create({ code: 'Nederland', name: 'Nederland' }),
        this.repository.create({ code: 'Portuguese', name: 'Portuguese' }),
        this.repository.create({ code: 'Russian', name: 'Russian' }),
        this.repository.create({ code: 'Spanish', name: 'Spanish' }),
        this.repository.create({ code: 'Turkish', name: 'Turkish' }),
        this.repository.create({ code: 'Ucranian', name: 'Ucranian' }),
        this.repository.create({ code: 'Arab', name: 'Arab' }),
      ]);
    }
  }
}
