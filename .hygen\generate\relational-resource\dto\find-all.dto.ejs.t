---
to: src/<%= h.inflection.transform(name, ['pluralize', 'underscore', 'dasherize']) %>/dto/find-all-<%= h.inflection.transform(name, ['pluralize', 'underscore', 'dasherize']) %>.dto.ts
---
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindAllDto {
  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 1))
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 10))
  @IsNumber()
  @IsOptional()
  limit?: number;

  @IsOptional()
  name?: string;

  // ----- MORE FIELDS HERE -----

  @IsOptional()
  description?: string;

  // ----------------------------
}

export const InFields = [''];

export const RelationalFields = [''];

export const DateFields = [''];

// Evaluated Date Fields EXAMPLE
export const EvaluateDateFields = [
  {
    name: 'startDate',
    evaluationType: '>=',
  },
  {
    name: 'endDate',
    evaluationType: '<=',
  },
];

export const StringFields = ['name', 'description'];

export const NumberFields = [''];

export const BooleanFields = [''];
