import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { AdvertRate } from '../../domain/advert-rate';

export abstract class AdvertRateRepository {
  abstract create(
    data: Omit<AdvertRate, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<AdvertRate>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertRate[]>;

  abstract findAll(): Promise<AdvertRate[]>;

  abstract findById(id: AdvertRate['id']): Promise<NullableType<AdvertRate>>;

  abstract findMany(advertId: string): Promise<NullableType<AdvertRate[]>>;

  abstract update(
    id: AdvertRate['id'],
    payload: DeepPartial<AdvertRate>,
  ): Promise<AdvertRate | null>;

  abstract remove(id: AdvertRate['id']): Promise<void>;
}
