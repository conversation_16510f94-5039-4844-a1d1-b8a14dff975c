import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CountryRepository } from '../country.repository';
import { CountryRelationalRepository } from './repositories/country.repository';
import { CountryEntity } from './entities/country.entity';
import { RegionRepository } from '../region.repository';
import { RegionRelationalRepository } from './repositories/region.repository';
import { RegionEntity } from './entities/region.entity';
import { CityRepository } from '../city.repository';
import { CityRelationalRepository } from './repositories/city.repository';
import { CityEntity } from './entities/city.entity';
import { NeighborhoodRepository } from '../neighborhood.repository';
import { NeighborhoodRelationalRepository } from './repositories/neighborhood.repository';
import { NeighborhoodEntity } from './entities/neighborhood.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CountryEntity,
      RegionEntity,
      CityEntity,
      NeighborhoodEntity,
    ]),
  ],
  providers: [
    {
      provide: CountryRepository,
      useClass: CountryRelationalRepository,
    },
    {
      provide: RegionRepository,
      useClass: RegionRelationalRepository,
    },
    {
      provide: CityRepository,
      useClass: CityRelationalRepository,
    },
    {
      provide: NeighborhoodRepository,
      useClass: NeighborhoodRelationalRepository,
    },
  ],
  exports: [
    CountryRepository,
    RegionRepository,
    CityRepository,
    NeighborhoodRepository,
  ],
})
export class RelationalLocationPersistenceModule {}
