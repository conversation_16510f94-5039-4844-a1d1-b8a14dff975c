import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { AdvertFileEntity } from '../entities/advert-file.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { AdvertFile } from '../../../../domain/advert-file';
import { AdvertFileRepository } from '../../advert-file.repository';
import { AdvertFileMapper } from '../mappers/advert-file.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';
import { put, PutBlobResult } from '@vercel/blob';
import { FindAllAdvertFilesVideoDto } from '../../../../dto/find-all-advert-files.dto';
import { fileType } from '../../../../dto/advert-file-type.dto';
import moment from 'moment';

@Injectable()
export class AdvertFileRelationalRepository implements AdvertFileRepository {
  constructor(
    @InjectRepository(AdvertFileEntity)
    private readonly advert_fileRepository: Repository<AdvertFileEntity>,
  ) {}

  async create(data: AdvertFile): Promise<AdvertFile> {
    const persistenceModel = AdvertFileMapper.toPersistence(data);
    const newEntity = await this.advert_fileRepository.save(
      this.advert_fileRepository.create(persistenceModel),
    );
    return AdvertFileMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertFile[]> {
    const entities = await this.advert_fileRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((user) => AdvertFileMapper.toDomain(user));
  }

  async findAllVideosWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllAdvertFilesVideoDto,
  ) {
    const query = this.advert_fileRepository
      .createQueryBuilder('advertFile')
      .innerJoinAndSelect('advertFile.advert', 'advert')
      .leftJoinAndSelect('advert.advertLocations', 'advertLocations')
      .leftJoinAndSelect('advertLocations.city', 'city');
    query.where('(advertFile.type = :type2 OR (advertFile.type = :type3))', {
      type2: fileType.VIDEO,
      type3: fileType.REEL_VIDEO,
    });
    if (filters?.citySlug) {
      query.andWhere(`LOWER(city.slug) LIKE LOWER('%${filters.citySlug}%')`);
    }
    query
      .orderBy('city.slug', 'ASC')
      .addOrderBy('advert.updatedAt', 'DESC')
      .addOrderBy('advert.id', 'ASC')
      .addOrderBy('advertFile.updatedAt', 'DESC');

    const totalCount = await query.getCount();

    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);

    const videos = await query.getMany();

    return {
      data: videos.map((advertFile) => AdvertFileMapper.toDomain(advertFile)),
      totalCount: totalCount,
    };
  }

  async findById(id: AdvertFile['id']): Promise<NullableType<AdvertFile>> {
    const entity = await this.advert_fileRepository.findOne({
      where: { id },
    });

    return entity ? AdvertFileMapper.toDomain(entity) : null;
  }

  async findMany(
    advertId: string,
    updateAdvertFileDto: any,
  ): Promise<NullableType<AdvertFile[]>> {
    if (updateAdvertFileDto?.type) {
      return await this.advert_fileRepository.find({
        where: {
          advert: { id: advertId },
          type: updateAdvertFileDto?.type ?? 1,
        },
        order: { type: 1, main: -1, order: 1 },
      });
    } else {
      return await this.advert_fileRepository.find({
        where: { advert: { id: advertId } },
        order: { type: 1, main: -1, order: 1 },
      });
    }
  }

  async findManyMain(advertId: string): Promise<NullableType<AdvertFile[]>> {
    return await this.advert_fileRepository.find({
      where: {
        advert: { id: advertId },
        type: 1,
        main: true,
      },
      order: { order: 1 },
    });
  }

  async findManyReels(): Promise<
    (
      | undefined
      | {
          profileName: string;
          profileImage: AdvertFileEntity | null;
          hour: string;
          files: AdvertFile[];
          advertId: string;
        }
    )[]
  > {
    const nowDate = moment.utc().format('YYYY-MM-DD HH:mm:ss');
    const futureDate = moment
      .utc()
      .add(24, 'hours')
      .format('YYYY-MM-DD HH:mm:ss');

    const data = await this.advert_fileRepository
      .createQueryBuilder('advert_file')
      .leftJoinAndSelect('advert_file.advert', 'advert')
      .leftJoinAndSelect('advert_file.subscription', 'subscription')
      .where(
        new Brackets((qb) => {
          qb.where(
            `    advert_file.type IN (:...types) 
                    AND advert_file.validatedAt IS NOT NULL
                    AND subscription.status = 'active'
                    AND subscription.startDate <= :futureLimit
                    AND subscription.endDate >= :now`,
            {
              types: [fileType.REEL_IMAGE, fileType.REEL_VIDEO],
              futureLimit: futureDate,
              now: nowDate,
            },
          ).orWhere('advert_file.type = :type1 AND advert_file.main = :main', {
            type1: fileType.IMAGE,
            main: true,
          });
        }),
      )
      .andWhere('advert.status = 1')
      .orderBy('advert.oTopStories', 'DESC')
      .addOrderBy("DATE_TRUNC('hour', advert_file.validatedAt)", 'DESC')
      .addOrderBy('advert_file.order', 'ASC')
      .getMany();

    const groupedByAdvert = data.reduce(
      (acc, file) => {
        const advertId = file?.advert?.id;
        const advertName = file?.advert?.profileName;
        if (!advertId) return acc;

        const validationHour = new Date(file.validatedAt);
        validationHour.setMinutes(0, 0, 0); // Truncar minutos y segundos
        const hourKey = validationHour.toISOString(); // Usamos un ISO string para evitar problemas de formato

        if (!acc[advertId]) {
          acc[advertId] = { profileName: advertName, hours: {} }; // Almacenamos profileName y un objeto para horas
        }

        if (!acc[advertId].hours[hourKey]) {
          acc[advertId].hours[hourKey] = []; // Inicializamos el array de archivos por hora si no existe
        }

        acc[advertId].hours[hourKey].push(file); // Agregamos el archivo en el array correspondiente

        return acc;
      },
      {} as Record<
        string,
        { profileName: string; hours: Record<string, AdvertFile[]> }
      >,
    );

    return Object.entries(groupedByAdvert)
      .map(([advertId, { profileName, hours }]) => {
        // Obtener el archivo de tipo 1 y main = true (sin importar la hora)
        const fileType1 = data.find(
          (file) =>
            file.advert &&
            file.advert.id === advertId &&
            file.type === fileType.IMAGE &&
            file.main,
        );

        const mostRecentHour = Object.keys(hours)
          .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
          .shift();

        const filesOfType34 = mostRecentHour
          ? hours[mostRecentHour]
              .filter(
                (file) =>
                  file.type === fileType.REEL_IMAGE ||
                  file.type === fileType.REEL_VIDEO,
              ) // Filtrar tipo 3
              .sort((a, b) => a.order - b.order) // Ordenar por `order`
          : [];

        if (filesOfType34.length === 0) {
          return undefined;
        }

        return {
          advertId,
          profileName,
          hour: mostRecentHour || 'unknown',
          profileImage: fileType1 || null,
          files: filesOfType34,
        };
      })
      .filter((item) => item !== undefined);
  }

  async findManyVideos(filters: FindAllAdvertFilesVideoDto): Promise<
    (
      | undefined
      | {
          profileName: string;
          profileImage: AdvertFile | null;
          files: AdvertFile[];
          advertId: string;
        }
    )[]
  > {
    const page = filters?.page ?? 1;
    let limit = filters?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }
    const query = this.advert_fileRepository
      .createQueryBuilder('advert_file')
      .leftJoinAndSelect('advert_file.advert', 'advert');
    if (filters?.citySlug) {
      query.leftJoinAndSelect('advert.advertLocations', 'advertLocations');
      query.leftJoinAndSelect('advertLocations.city', 'city');
      query.andWhere(`LOWER(city.slug) LIKE LOWER('%${filters.citySlug}%')`);
    }
    query
      .andWhere('advert.status = 1')
      .andWhere('advert_file.type IN (:...types)', {
        types: [fileType.VIDEO, fileType.REEL_VIDEO],
      })
      .orWhere('advert_file.type = :type1 AND advert_file.main = :main', {
        type1: fileType.IMAGE,
        main: true,
      })
      .orderBy("DATE_TRUNC('hour', advert_file.createdAt)", 'DESC')
      .addOrderBy('advert_file.order', 'ASC');
    const data = await query.getMany();

    const groupedByAdvert = data.reduce(
      (acc, file) => {
        const advertId = file?.advert?.id;
        const advertName = file?.advert?.profileName;

        if (!advertId) return acc;

        if (!acc[advertId]) {
          acc[advertId] = {
            profileName: advertName,
            files: [],
            profileImage: null,
          };
        }

        // Verifica si el archivo es de tipo 1 y main, para definir `profileImage`
        if (file.type === fileType.IMAGE && file.main) {
          acc[advertId].profileImage = file;
        }

        // Filtrar archivos según la condición de tipo
        if (file.type === fileType.VIDEO || file.type === fileType.REEL_VIDEO) {
          acc[advertId].files.push(file);
        }

        return acc;
      },
      {} as Record<
        string,
        {
          profileName: string;
          files: AdvertFile[];
          profileImage: AdvertFile | null;
        }
      >,
    );

    const paginateResults = (data, page = 1, limit = 50) => {
      if (page === 0 && limit === 0) {
        return data;
      }
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      return data.slice(startIndex, endIndex);
    };

    return paginateResults(
      Object.entries(groupedByAdvert)
        .map(([advertId, { profileName, files, profileImage }]) => {
          if (files.length === 0) {
            return undefined;
          }

          return {
            advertId,
            profileName,
            profileImage: profileImage || null,
            files,
          };
        })
        .filter((item) => item !== undefined), // Eliminamos los undefined
      page,
      limit,
    );

    /*
    return Object.entries(groupedByAdvert)
      .map(([advertId, { profileName, files, profileImage }]) => {
        if (files.length === 0) {
          return undefined;
        }

        return {
          advertId,
          profileName,
          profileImage: profileImage || null,
          files,
        };
      })
      .filter((item) => item !== undefined);
     */
  }

  async update(
    id: AdvertFile['id'],
    payload: Partial<AdvertFile>,
  ): Promise<AdvertFile> {
    const entity = await this.advert_fileRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.advert_fileRepository.save(
      this.advert_fileRepository.create(
        AdvertFileMapper.toPersistence({
          ...AdvertFileMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return AdvertFileMapper.toDomain(updatedEntity);
  }

  async remove(id: AdvertFile['id']): Promise<void> {
    await this.advert_fileRepository.delete(id);
  }

  async upload(advertId: string, dataFile: File): Promise<PutBlobResult> {
    // TODO ORTEGA
    return await put(dataFile.name, dataFile, { access: 'public' });
  }
}
