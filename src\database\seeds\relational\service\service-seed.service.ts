import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceEntity } from '../../../../services/infrastructure/persistence/relational/entities/service.entity';

@Injectable()
export class ServiceSeedService {
  constructor(
    @InjectRepository(ServiceEntity)
    private repository: Repository<ServiceEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      // SERVICIOS
      await this.repository.save([
        this.repository.create({
          //id: 1,
          name: 'EscortsHub',
          serviceType: { id: 1 },
          slug: 'escortshub',
          en: 'EscortsHub',
          es: 'Escorts Hub',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          //id: 2,
          name: 'EscortsHub',
          serviceType: { id: 2 },
          slug: 'escortshub',
          en: 'EscortsHub',
          es: 'Escorts Hub',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          //id: 3,
          name: 'EscortsHub',
          serviceType: { id: 1 },
          slug: 'escortshub',
          en: 'EscortsHub',
          es: 'Escorts Hub',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          //id: 4,
          name: 'EscortFans',
          serviceType: { id: 1 },
          slug: 'escortfans',
          en: 'EscortFans',
          es: 'EscortFans',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          //id: 5,
          name: 'EscortFans',
          serviceType: { id: 2 },
          slug: 'escortfans',
          en: 'EscortFans',
          es: 'EscortFans',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          //id: 6,
          name: 'EscortFans',
          serviceType: { id: 3 },
          slug: 'escortfans',
          en: 'EscortFans',
          es: 'EscortFans',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: '69',
          serviceType: { id: 1 },
          slug: '69',
          en: '69',
          es: '69',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'PSE (Pornstar experience)',
          serviceType: { id: 1 },
          slug: 'psepornstarexperience',
          en: 'PSE (Pornstar experience)',
          es: 'Experiencia de pornstar (PSE)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Phone sex',
          serviceType: { id: 1 },
          slug: 'phonesex',
          en: 'Phone sex',
          es: 'Sexo telefónico',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Reverse oral',
          serviceType: { id: 1 },
          slug: 'reverseoral',
          en: 'Reverse oral',
          es: 'Oral invertido',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Rimming',
          serviceType: { id: 1 },
          slug: 'rimming',
          en: 'Rimming',
          es: 'Beso negro',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Rimming giving',
          serviceType: { id: 1 },
          slug: 'rimminggiving',
          en: 'Rimming giving',
          es: 'Dar beso negro',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Rimming receiving',
          serviceType: { id: 1 },
          slug: 'rimmingreceiving',
          en: 'Rimming receiving',
          es: 'Recibir beso negro',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Role play',
          serviceType: { id: 1 },
          slug: 'roleplay',
          en: 'Role play',
          es: 'Juego de roles',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Russian',
          serviceType: { id: 1 },
          slug: 'russian',
          en: 'Russian',
          es: 'Rusa',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Sex toys',
          serviceType: { id: 1 },
          slug: 'sextoys',
          en: 'Sex toys',
          es: 'Juguetes sexuales',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Sexting',
          serviceType: { id: 1 },
          slug: 'sexting',
          en: 'Sexting',
          es: 'Sexting',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Shared showers',
          serviceType: { id: 1 },
          slug: 'sharedshowers',
          en: 'Shared showers',
          es: 'Duchas compartidas',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Showers and bathtub games',
          serviceType: { id: 1 },
          slug: 'showersandbathtubgames',
          en: 'Showers and bathtub games',
          es: 'Juegos en la ducha y la bañera',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Squirting',
          serviceType: { id: 1 },
          slug: 'squirting',
          en: 'Squirting',
          es: 'Eyaculación femenina',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Strap on',
          serviceType: { id: 1 },
          slug: 'strapon',
          en: 'Strap on',
          es: 'Strap-on',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Submissive',
          serviceType: { id: 1 },
          slug: 'submissive',
          en: 'Submissive',
          es: 'Sumisa',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Tea bagging',
          serviceType: { id: 1 },
          slug: 'teabagging',
          en: 'Tea bagging',
          es: 'Cajitas chinas',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Tie and tease',
          serviceType: { id: 1 },
          slug: 'tieandtease',
          en: 'Tie and tease',
          es: 'Atar y provocar',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Uniforms',
          serviceType: { id: 1 },
          slug: 'uniforms',
          en: 'Uniforms',
          es: 'Uniformes',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Watersports',
          serviceType: { id: 1 },
          slug: 'watersports',
          en: 'Watersports',
          es: 'Juegos de orina',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Domination',
          serviceType: { id: 3 },
          slug: 'domination',
          en: 'Domination',
          es: 'Dominación',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Double domme',
          serviceType: { id: 3 },
          slug: 'doubledomme',
          en: 'Double domme',
          es: 'Doble dominadora',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Electrics',
          serviceType: { id: 3 },
          slug: 'electrics',
          en: 'Electrics',
          es: 'Electroestimulación',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Face sitting',
          serviceType: { id: 3 },
          slug: 'facesitting',
          en: 'Face sitting',
          es: 'Sentarse en la cara',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Degradation',
          serviceType: { id: 3 },
          slug: 'degradation',
          en: 'Degradation',
          es: 'Degradación',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Dog training',
          serviceType: { id: 3 },
          slug: 'dogtraining',
          en: 'Dog training',
          es: 'Entrenamiento de perro',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Caricias',
          serviceType: { id: 99 },
          slug: 'caricias',
          en: 'Petting',
          es: 'Caricias',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Masajes Relajantes',
          serviceType: { id: 99 },
          slug: 'masajesrelajantes',
          en: 'Relaxing Massages',
          es: 'Masajes Relajantes',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Literatura Erótica',
          serviceType: { id: 99 },
          slug: 'literaturaerotica',
          en: 'Erotic Literature',
          es: 'Literatura Erótica',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Locales Liberales',
          serviceType: { id: 99 },
          slug: 'localesliberales',
          en: 'Liberal Locals',
          es: 'Locales Liberales',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Salir de Fiesta',
          serviceType: { id: 99 },
          slug: 'salirdefiesta',
          en: 'Partying',
          es: 'Salir de Fiesta',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Crossdressing',
          serviceType: { id: 3 },
          slug: 'crossdressing',
          en: 'Crossdressing',
          es: 'Transvestismo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Foot Fetish',
          serviceType: { id: 1 },
          slug: 'footfetish',
          en: 'Foot Fetish',
          es: 'Fetiche de pies',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Phone Sex',
          serviceType: { id: 2 },
          slug: 'phonesex',
          en: 'Phone Sex',
          es: 'Sexo telefónico',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Sexting',
          serviceType: { id: 2 },
          slug: 'sexting',
          en: 'Sexting',
          es: 'Sexting',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Strap On',
          serviceType: { id: 3 },
          slug: 'strapon',
          en: 'Strap On',
          es: 'Strap-on',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Watersports',
          serviceType: { id: 3 },
          slug: 'watersports',
          en: 'Watersports',
          es: 'Juegos de orina',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Naked massage',
          serviceType: { id: 2 },
          slug: 'nakedmassage',
          en: 'Naked massage',
          es: 'Masaje desnudo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Nuru massage',
          serviceType: { id: 2 },
          slug: 'nurumassage',
          en: 'Nuru massage',
          es: 'Masaje Nuru',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Prostate massage',
          serviceType: { id: 2 },
          slug: 'prostatemassage',
          en: 'Prostate massage',
          es: 'Masaje prostático',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Reflexology',
          serviceType: { id: 2 },
          slug: 'reflexology',
          en: 'Reflexology',
          es: 'Reflexología',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Soapy massage',
          serviceType: { id: 2 },
          slug: 'soapymassage',
          en: 'Soapy massage',
          es: 'Masaje con jabón',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Sports massage',
          serviceType: { id: 2 },
          slug: 'sportsmassage',
          en: 'Sports massage',
          es: 'Masaje deportivo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Swedish massage',
          serviceType: { id: 2 },
          slug: 'swedishmassage',
          en: 'Swedish massage',
          es: 'Masaje sueco',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Fetishes',
          serviceType: { id: 3 },
          slug: 'fetishes',
          en: 'Fetishes',
          es: 'Fetiches',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Training',
          serviceType: { id: 3 },
          slug: 'training',
          en: 'Training',
          es: 'Entrenamiento',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Trampling',
          serviceType: { id: 3 },
          slug: 'trampling',
          en: 'Trampling',
          es: 'Pisoteo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Wrestling',
          serviceType: { id: 3 },
          slug: 'wrestling',
          en: 'Wrestling',
          es: 'Lucha',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Body worship',
          serviceType: { id: 3 },
          slug: 'bodyworship',
          en: 'Body worship',
          es: 'Adoración corporal',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Viajes',
          serviceType: { id: 99 },
          slug: 'viajes',
          en: 'Trips',
          es: 'Viajes',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Salidas a Eventos',
          serviceType: { id: 99 },
          slug: 'salidasaeventos',
          en: 'Outings to Events',
          es: 'Salidas a Eventos',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Masajes a Domicilio',
          serviceType: { id: 99 },
          slug: 'masajesadomicilio',
          en: 'Home Massages',
          es: 'Masajes a Domicilio',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Cenas Románticas',
          serviceType: { id: 99 },
          slug: 'cenasromanticas',
          en: 'Romantic Dinners',
          es: 'Cenas Románticas',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Bondage',
          serviceType: { id: 3 },
          slug: 'bondage',
          en: 'Bondage',
          es: 'Bondage',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Shiatsu',
          serviceType: { id: 2 },
          slug: 'shiatsu',
          en: 'Shiatsu',
          es: 'Shiatsu',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Foot Fetish',
          serviceType: { id: 3 },
          slug: 'footfetish',
          en: 'Foot Fetish',
          es: 'Fetiche de pies',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Tantra massage',
          serviceType: { id: 2 },
          slug: 'tantramassage',
          en: 'Tantra massage',
          es: 'Masaje tantra',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Tantric Lingam Massage',
          serviceType: { id: 2 },
          slug: 'tantriclingammassage',
          en: 'Tantric Lingam Massage',
          es: 'Masaje Lingam tántrico',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Thai massage',
          serviceType: { id: 2 },
          slug: 'thaimassage',
          en: 'Thai massage',
          es: 'Masaje tailandés',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Ultimate Yoni Massage',
          serviceType: { id: 2 },
          slug: 'ultimateyonimassage',
          en: 'Ultimate Yoni Massage',
          es: 'Masaje Yoni definitivo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Financial domination',
          serviceType: { id: 3 },
          slug: 'financialdomination',
          en: 'Financial domination',
          es: 'Dominación financiera',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Flogging',
          serviceType: { id: 3 },
          slug: 'flogging',
          en: 'Flogging',
          es: 'Flagelación',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Gags & hoods',
          serviceType: { id: 3 },
          slug: 'gagshoods',
          en: 'Gags & hoods',
          es: 'Gag y capuchas',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'CBT (Cock and ball torture)',
          serviceType: { id: 3 },
          slug: 'cbtcockandballtorture',
          en: 'CBT (Cock and ball torture)',
          es: 'CBT (Tortura de pene y testículos)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Burn',
          serviceType: { id: 3 },
          slug: 'burn',
          en: 'Burn',
          es: 'Quemadura',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Cane / Whipping',
          serviceType: { id: 3 },
          slug: 'canewhipping',
          en: 'Cane / Whipping',
          es: 'Caño / Latigazo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Chastity control',
          serviceType: { id: 3 },
          slug: 'chastitycontrol',
          en: 'Chastity control',
          es: 'Control de castidad',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Costumes',
          serviceType: { id: 3 },
          slug: 'costumes',
          en: 'Costumes',
          es: 'Disfraces',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Turkish Bath Massage',
          serviceType: { id: 2 },
          slug: 'turkishbathmassage',
          en: 'Turkish Bath Massage',
          es: 'Masaje de baño turco',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Fisting',
          serviceType: { id: 3 },
          slug: 'fisting',
          en: 'Fisting',
          es: 'Fisting',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Anal giving',
          serviceType: { id: 1 },
          slug: 'analgiving',
          en: 'Anal giving',
          es: 'Dar sexo anal',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Anal receiving',
          serviceType: { id: 1 },
          slug: 'analreceiving',
          en: 'Anal receiving',
          es: 'Recibir sexo anal',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Latex PVC',
          serviceType: { id: 3 },
          slug: 'latexpvc',
          en: 'Latex PVC',
          es: 'Latex PVC',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Aromatherapy',
          serviceType: { id: 2 },
          slug: 'aromatherapy',
          en: 'Aromatherapy',
          es: 'Aromaterapia',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Sissyfication',
          serviceType: { id: 3 },
          slug: 'sissyfication',
          en: 'Sissyfication',
          es: 'Sissyfication',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Smothering',
          serviceType: { id: 3 },
          slug: 'smothering',
          en: 'Smothering',
          es: 'Sofocación',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Genital massage',
          serviceType: { id: 2 },
          slug: 'genitalmassage',
          en: 'Genital massage',
          es: 'Masaje genital',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Face slapping',
          serviceType: { id: 3 },
          slug: 'faceslapping',
          en: 'Face slapping',
          es: 'Bofetada en la cara',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Feminisation',
          serviceType: { id: 3 },
          slug: 'feminisation',
          en: 'Feminisation',
          es: 'Feminización',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Cranio-Sacral Therapy',
          serviceType: { id: 2 },
          slug: 'craniosacraltherapy',
          en: 'Cranio-Sacral Therapy',
          es: 'Terapia craneosacral',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Handcuffs',
          serviceType: { id: 3 },
          slug: 'handcuffs',
          en: 'Handcuffs',
          es: 'Esposas',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Humiliation',
          serviceType: { id: 3 },
          slug: 'humiliation',
          en: 'Humiliation',
          es: 'Humillación',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'CIM (Cum in mouth)',
          serviceType: { id: 1 },
          slug: 'cimcuminmouth',
          en: 'CIM (Cum in mouth)',
          es: 'CIM (Eyacular en la boca)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'CIM (with condom)',
          serviceType: { id: 1 },
          slug: 'cimwithcondom',
          en: 'CIM (with condom)',
          es: 'CIM (con condón)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'COB (Cum on body)',
          serviceType: { id: 1 },
          slug: 'cobcumonbody',
          en: 'COB (Cum on body)',
          es: 'COB (Eyacular en el cuerpo)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Couples',
          serviceType: { id: 1 },
          slug: 'couples',
          en: 'Couples',
          es: 'Parejas',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Crossdressing',
          serviceType: { id: 1 },
          slug: 'crossdressing',
          en: 'Crossdressing',
          es: 'Transvestismo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Deep Throat',
          serviceType: { id: 1 },
          slug: 'deepthroat',
          en: 'Deep Throat',
          es: 'Boca profunda',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Facials',
          serviceType: { id: 1 },
          slug: 'facials',
          en: 'Facials',
          es: 'Faciales',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Fingering',
          serviceType: { id: 1 },
          slug: 'fingering',
          en: 'Fingering',
          es: 'Dedos',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'French kissing',
          serviceType: { id: 1 },
          slug: 'frenchkissing',
          en: 'French kissing',
          es: 'Beso francés',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'GFE (Girlfriend experience)',
          serviceType: { id: 1 },
          slug: 'gfegirlfriendexperience',
          en: 'GFE (Girlfriend experience)',
          es: 'Experiencia de novia (GFE)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Kissing (no tongue)',
          serviceType: { id: 1 },
          slug: 'kissingnotongue',
          en: 'Kissing (no tongue)',
          es: 'Besar (sin lengua)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Lap dancing',
          serviceType: { id: 1 },
          slug: 'lapdancing',
          en: 'Lap dancing',
          es: 'Bailes en el regazo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Nipple Play',
          serviceType: { id: 3 },
          slug: 'nippleplay',
          en: 'Nipple Play',
          es: 'Juegos con pezones',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Nipple play',
          serviceType: { id: 1 },
          slug: 'nippleplay',
          en: 'Nipple play',
          es: 'Juegos con pezones',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'OWO (Oral without condom)',
          serviceType: { id: 1 },
          slug: 'owooralwithoutcondom',
          en: 'OWO (Oral without condom)',
          es: 'OWO (Oral sin condón)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Oral (with condom)',
          serviceType: { id: 1 },
          slug: 'oralwithcondom',
          en: 'Oral (with condom)',
          es: 'Oral (con condón)',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Pegging',
          serviceType: { id: 1 },
          slug: 'pegging',
          en: 'Pegging',
          es: 'Pegging',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Phone Sex',
          serviceType: { id: 3 },
          slug: 'phonesex',
          en: 'Phone Sex',
          es: 'Sexo telefónico',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Role Play',
          serviceType: { id: 3 },
          slug: 'roleplay',
          en: 'Role Play',
          es: 'Juego de roles',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Sexting',
          serviceType: { id: 3 },
          slug: 'sexting',
          en: 'Sexting',
          es: 'Sexting',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Anti-Stress Massage',
          serviceType: { id: 2 },
          slug: 'antistressmassage',
          en: 'Anti-Stress Massage',
          es: 'Masaje anti-estrés',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Body to body massage',
          serviceType: { id: 2 },
          slug: 'bodytobodymassage',
          en: 'Body to body massage',
          es: 'Masaje corporal',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Clothed massage',
          serviceType: { id: 2 },
          slug: 'clothedmassage',
          en: 'Clothed massage',
          es: 'Masaje con ropa',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Hot-stone massage',
          serviceType: { id: 2 },
          slug: 'hotstonemassage',
          en: 'Hot-stone massage',
          es: 'Masaje con piedras calientes',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Deep tissue massage',
          serviceType: { id: 2 },
          slug: 'deeptissuemassage',
          en: 'Deep tissue massage',
          es: 'Masaje de tejido profundo',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Foot Massage',
          serviceType: { id: 2 },
          slug: 'footmassage',
          en: 'Foot Massage',
          es: 'Masaje de pies',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Four hands massage',
          serviceType: { id: 2 },
          slug: 'fourhandsmassage',
          en: 'Four hands massage',
          es: 'Masaje de cuatro manos',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Happy ending',
          serviceType: { id: 2 },
          slug: 'happyending',
          en: 'Happy ending',
          es: 'Final feliz',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Lomi Hawaiian Massage',
          serviceType: { id: 2 },
          slug: 'lomihawaiianmassage',
          en: 'Lomi Hawaiian Massage',
          es: 'Masaje Lomi hawaiano',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Lymphatic Massage',
          serviceType: { id: 2 },
          slug: 'lymphaticmassage',
          en: 'Lymphatic Massage',
          es: 'Masaje linfático',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Inflicting pain',
          serviceType: { id: 3 },
          slug: 'inflictingpain',
          en: 'Inflicting pain',
          es: 'Infligir dolor',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Interrogations',
          serviceType: { id: 3 },
          slug: 'interrogations',
          en: 'Interrogations',
          es: 'Interrogatorios',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Mummification',
          serviceType: { id: 3 },
          slug: 'mummification',
          en: 'Mummification',
          es: 'Momificación',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Pegging Domination',
          serviceType: { id: 3 },
          slug: 'peggingdomination',
          en: 'Pegging Domination',
          es: 'Dominación de Pegging',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Period play',
          serviceType: { id: 3 },
          slug: 'periodplay',
          en: 'Period play',
          es: 'Juego de la menstruación',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Sensory Deprivation',
          serviceType: { id: 3 },
          slug: 'sensorydeprivation',
          en: 'Sensory Deprivation',
          es: 'Privación sensorial',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Slaves accepted',
          serviceType: { id: 3 },
          slug: 'slavesaccepted',
          en: 'Slaves accepted',
          es: 'Esclavos aceptados',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Spanking',
          serviceType: { id: 3 },
          slug: 'spanking',
          en: 'Spanking',
          es: 'Azotes',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Spitting',
          serviceType: { id: 3 },
          slug: 'spitting',
          en: 'Spitting',
          es: 'Escupir',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Hand job',
          serviceType: { id: 1 },
          slug: 'handjob',
          en: 'Hand job',
          es: 'Masturbación manual',
          wiki_en: 'Masturbation',
          wiki_es: 'Masturbación',
        }),
        this.repository.create({
          name: 'Male grooming',
          serviceType: { id: 2 },
          slug: 'malegrooming',
          en: 'Male grooming',
          es: 'Aseo masculino',
          wiki_en: '',
          wiki_es: '',
        }),
        this.repository.create({
          name: 'Massage',
          serviceType: { id: 2 },
          slug: 'massage',
          en: 'Massage',
          es: 'Masaje',
          wiki_en: '',
          wiki_es: '',
        }),
      ]);
    }
  }
}
