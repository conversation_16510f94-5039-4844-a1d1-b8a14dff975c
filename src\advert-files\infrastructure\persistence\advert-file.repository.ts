import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { AdvertFile } from '../../domain/advert-file';
import { PutBlobResult } from '@vercel/blob';
import { AdvertFileEntity } from './relational/entities/advert-file.entity';
import { FindAllAdvertFilesVideoDto } from '../../dto/find-all-advert-files.dto';

export abstract class AdvertFileRepository {
  abstract create(
    data: Omit<AdvertFile, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<AdvertFile>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertFile[]>;

  abstract findAllVideosWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllAdvertFilesVideoDto,
  );

  abstract findById(id: AdvertFile['id']): Promise<NullableType<AdvertFile>>;

  abstract findMany(
    advertId: string,
    updateAdvertFileDto: any,
  ): Promise<NullableType<AdvertFile[]>>;

  abstract findManyMain(advertId: string): Promise<NullableType<AdvertFile[]>>;

  abstract findManyReels(): Promise<
    (
      | undefined
      | {
          profileName: string;
          profileImage: AdvertFileEntity | null;
          hour: string;
          files: AdvertFile[];
          advertId: string;
        }
    )[]
  >;

  abstract findManyVideos(filters: FindAllAdvertFilesVideoDto): Promise<
    (
      | undefined
      | {
          profileName: string;
          profileImage: AdvertFile | null;
          files: AdvertFile[];
          advertId: string;
        }
    )[]
  >;

  abstract update(
    id: AdvertFile['id'],
    payload: DeepPartial<AdvertFile>,
  ): Promise<AdvertFile | null>;

  abstract remove(id: AdvertFile['id']): Promise<void>;

  abstract upload(advertId: string, dataFile: File): Promise<PutBlobResult>;
}
