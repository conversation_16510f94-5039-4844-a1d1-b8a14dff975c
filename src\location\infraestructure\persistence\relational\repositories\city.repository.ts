import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CityEntity } from '../entities/city.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { City } from '../../../../domain/city';
import { CityRepository } from '../../city.repository';
import { CityMapper } from '../mappers/city.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';
import {
  DateFields,
  FindAllCitiesDto,
  NumberFields,
  RelationalFields,
  StringFields,
} from '../../../../dto/find-all-cities.dto';

@Injectable()
export class CityRelationalRepository implements CityRepository {
  constructor(
    @InjectRepository(CityEntity)
    private readonly cityRepository: Repository<CityEntity>,
  ) {}

  async create(data: City): Promise<City> {
    const persistenceModel = CityMapper.toPersistence(data);
    const newEntity = await this.cityRepository.save(
      this.cityRepository.create(persistenceModel),
    );
    return CityMapper.toDomain(newEntity);
  }

  async findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCitiesDto,
  ): Promise<City[]> {
    const query = this.cityRepository.createQueryBuilder('city');
    query.leftJoinAndSelect('city.region', 'region');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page, limit, ...updatedDto } = filters;
    if (updatedDto) {
      Object.entries(updatedDto).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (RelationalFields.includes(key)) {
            query.andWhere(`${key}.id = :${key}`, { [key]: value });
          } else if (StringFields.includes(key)) {
            query.andWhere(`LOWER(city.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          } else if (NumberFields.includes(key)) {
            query.andWhere(`city.${key} = :${key}`, {
              [key]: Number(value),
            });
          } else if (DateFields.includes(key)) {
            query.andWhere(`city.${key} = :${key}`, {
              [key]: new Date(value),
            });
          }
        }
      });
    }
    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);

    const entities = await query.getMany();

    return entities.map((entity) => CityMapper.toDomain(entity));
  }

  async findAll(): Promise<City[]> {
    const entities = await this.cityRepository.find({
      relations: ['region'],
    });

    return entities.map((element) => CityMapper.toDomain(element));
  }

  async findAllAndCount(): Promise<any> {
    return await this.cityRepository
      .createQueryBuilder('city')
      .innerJoinAndSelect('city.advertLocations', 'advert_location')
      .innerJoinAndSelect('advert_location.advert', 'advert')
      .select('city.id', 'cityId')
      .addSelect('city.name', 'cityName')
      .addSelect('city.slug', 'citySlug')
      .addSelect('COUNT(advert_location.id)', 'locationCount')
      .addSelect("CONCAT(city.name, ' (', COUNT(*), ')')", 'title')
      .where('advert.status = :status', { status: 1 })
      .groupBy('city.id')
      .addGroupBy('city.name')
      .orderBy('COUNT(advert_location.id)', 'DESC')
      .getRawMany();
  }

  async findAllAndCountQuery(queryParams: any): Promise<any> {
    const query = this.cityRepository
      .createQueryBuilder('city')
      .innerJoin('city.region', 'region')
      .innerJoinAndSelect('city.advertLocations', 'advert_location')
      .innerJoinAndSelect('advert_location.advert', 'advert')
      .select('city.id', 'cityId')
      .addSelect('city.name', 'cityName')
      .addSelect('city.slug', 'citySlug')
      .addSelect('COUNT(advert_location.id)', 'locationCount')
      .addSelect("CONCAT(city.name, ' (', COUNT(*), ')')", 'title');
    // .where('advert.status = :status', { status: 1 });
    if (queryParams !== '') {
      if (queryParams?.regionSlug) {
        query.andWhere('region.slug = :regionSlug', {
          regionSlug: queryParams?.regionSlug,
        });
      }
      if (queryParams?.userId) {
        query.andWhere('advert.userId = :userId', {
          userId: queryParams?.userId,
        });
      }
    }
    query
      .groupBy('city.id')
      .addGroupBy('city.name')
      .orderBy('COUNT(advert_location.id)', 'DESC');

    return await query.getRawMany();
  }

  async findById(id: City['id']): Promise<NullableType<City>> {
    const entity = await this.cityRepository.findOne({
      where: { id },
      relations: ['region'],
    });

    return entity ? CityMapper.toDomain(entity) : null;
  }

  async findBySlug(slug: City['slug']): Promise<NullableType<City>> {
    const entity = await this.cityRepository.findOne({
      where: { slug },
      relations: ['region'],
    });

    return entity ? CityMapper.toDomain(entity) : null;
  }

  async update(id: City['id'], payload: Partial<City>): Promise<City> {
    const entity = await this.cityRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.cityRepository.save(
      this.cityRepository.create(
        CityMapper.toPersistence({
          ...CityMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return CityMapper.toDomain(updatedEntity);
  }

  async remove(id: City['id']): Promise<void> {
    await this.cityRepository.softDelete(id);
  }
}
