import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { Advert } from '../../domain/advert';
import { FindAllAdvertLocationsDto } from '../../dto/find-all-advert-locations.dto';
import { GoogleApiLocationDto } from '../../../helpers/google-api/google-api.dto';
import { AdvertFile } from '../../../advert-files/domain/advert-file';

type ReelType = {
  hour: string;
  files: AdvertFile[];
};

export abstract class AdvertRepository {
  abstract create(
    data: Omit<
      Advert,
      | 'id'
      | 'createdAt'
      | 'updatedAt'
      | 'deletedAt'
      | 'advertLinks'
      | 'advertRates'
      | 'advertFiles'
      | 'advertLocations'
    >,
    googleData: GoogleApiLocationDto,
  ): Promise<NullableType<Advert>>;

  abstract findAllWithPaginationByLocationHome(
    query: FindAllAdvertLocationsDto,
  );

  abstract findAllByUser(userId: string);

  abstract findAllSiteMap();

  abstract findBySlug(id: Advert['slug']): Promise<NullableType<Advert>>;

  abstract findById(id: Advert['id']): Promise<NullableType<Advert>>;

  abstract findManyReels(advertId: string): Promise<ReelType[]>;

  abstract update(
    id: Advert['id'],
    payload: DeepPartial<Advert>,
    googleData: GoogleApiLocationDto,
  ): Promise<Advert | null>;

  abstract updateStatus(
    id: Advert['id'],
    payload: DeepPartial<Advert>,
  ): Promise<Advert | null>;

  abstract remove(id: Advert['id']): Promise<void>;
}
