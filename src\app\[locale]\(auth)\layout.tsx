import Image from 'next/image';
import Link from 'next/link';
import { ReactNode } from 'react';
import { useTranslations } from 'next-intl';

// Icono de check para la lista de características
const CheckIcon = () => (
  <svg
    className="h-6 w-6 text-[#F15C5C]"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 13l4 4L19 7"
    />
  </svg>
);

export default function AuthLayout({ children }: { children: ReactNode }) {
  const t = useTranslations('authLayout');
  const year = new Date().getFullYear();

  return (
    <div className="min-h-screen bg-white text-gray-800 font-sans">
      <header className="absolute top-0 left-0 w-full p-4 sm:p-6 z-10">
        <div className="mx-auto max-w-screen-xl px-4">
          <Link href="/" className="inline-block">
            <Image
              src="/svg/logoEscortsHub.svg"
              alt="ModelHub Logo"
              width={180}
              height={40}
              priority
            />
          </Link>
        </div>
      </header>
      <main className="flex min-h-screen">
        {/* Columna Izquierda: Contenido SEO */}
        <div className="hidden lg:flex lg:w-1/2 xl:w-[45%] flex-col justify-center items-start p-12 xl:p-24 bg-gray-50 relative">
          <div className="space-y-6 max-w-md">
            <h1 className="text-4xl xl:text-5xl font-bold tracking-tight text-gray-900">
              {t('title')}
            </h1>
            <p className="text-lg text-gray-600">{t('subtitle')}</p>
            <ul className="space-y-4 pt-4">
              <li className="flex items-start space-x-3">
                <CheckIcon />
                <span className="text-gray-700">{t('feature1')}</span>
              </li>
              <li className="flex items-start space-x-3">
                <CheckIcon />
                <span className="text-gray-700">{t('feature2')}</span>
              </li>
              <li className="flex items-start space-x-3">
                <CheckIcon />
                <span className="text-gray-700">{t('feature3')}</span>
              </li>
              <li className="flex items-start space-x-3">
                <CheckIcon />
                <span className="text-gray-700">{t('feature4')}</span>
              </li>
            </ul>
          </div>
          <div className="absolute bottom-6 left-12 text-xs text-gray-400">
            {t('copyright', { year })}
          </div>
        </div>

        {/* Columna Derecha: Formulario (Children) */}
        <div className="w-full lg:w-1/2 xl:w-[55%] flex flex-col justify-center items-center p-6 sm:p-12 bg-white">
          {/* Contenido para móviles (se muestra encima del formulario) */}
          <div className="lg:hidden text-center mb-5 pt-16">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              {t('mobileTitle')}
            </h1>
            <p className="mt-2 text-md text-gray-600">{t('mobileSubtitle')}</p>
          </div>
          {children}
        </div>
      </main>
    </div>
  );
}
