import { Injectable } from '@nestjs/common';
import { IPaginationOptions } from '../../utils/types/pagination-options';
import { Neighborhood } from '../domain/neighborhood';
import { NeighborhoodRepository } from '../infraestructure/persistence/neighborhood.repository';
import { CreateNeighborhoodDto } from '../dto/create-neighborhood.dto';
import { UpdateNeighborhoodDto } from '../dto/update-neighborhood.dto';
import { FindAllNeighborhoodsDto } from '../dto/find-all-neighborhoods.dto';

@Injectable()
export class NeighborhoodsService {
  constructor(
    private readonly neighborhoodRepository: NeighborhoodRepository,
  ) {}

  create(createNeighborhoodDto: CreateNeighborhoodDto) {
    return this.neighborhoodRepository.create(createNeighborhoodDto);
  }

  findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllNeighborhoodsDto,
  ) {
    return this.neighborhoodRepository.findAllWithPagination(
      {
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      },
      filters,
    );
  }

  findAll() {
    return this.neighborhoodRepository.findAll();
  }

  findAllAndCount() {
    return this.neighborhoodRepository.findAllAndCount();
  }

  findAllAndCountByCity(cityId: string) {
    return this.neighborhoodRepository.findAllAndCountByCity(cityId);
  }

  findOne(id: Neighborhood['id']) {
    return this.neighborhoodRepository.findById(id);
  }

  update(id: Neighborhood['id'], updateNeighborhoodDto: UpdateNeighborhoodDto) {
    return this.neighborhoodRepository.update(id, updateNeighborhoodDto);
  }

  remove(id: Neighborhood['id']) {
    return this.neighborhoodRepository.remove(id);
  }
}
