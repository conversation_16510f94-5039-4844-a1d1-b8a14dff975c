import { Inject, Injectable } from '@nestjs/common';
import { CreateBookmarkDto } from './dto/create-bookmark.dto';
import { UpdateBookmarkDto } from './dto/update-bookmark.dto';
import { BookmarkRepository } from './infrastructure/persistence/bookmark.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { Bookmark } from './domain/bookmark';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Injectable()
export class BookmarksService {
  constructor(
    private readonly bookmarkRepository: BookmarkRepository,
    private readonly cacheServiceHelper: CacheServiceHelper,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createBookmarkDto: CreateBookmarkDto) {
    return this.bookmarkRepository.create(createBookmarkDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.bookmarkRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  findAll() {
    return this.bookmarkRepository.findAll();
  }

  findOne(id: Bookmark['id']) {
    return this.bookmarkRepository.findById(id);
  }

  async findMany(userId: string) {
    const _cacheKey = `CACHE-BOOKMARKS-ID-${userId}`;
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'bookmarksService.findMany',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.bookmarkRepository.findMany(userId);
    if (data) {
      return await this.cacheService.set(_cacheKey, data);
    }
  }

  update(id: Bookmark['id'], updateBookmarkDto: UpdateBookmarkDto) {
    return this.bookmarkRepository.update(id, updateBookmarkDto);
  }

  remove(id: Bookmark['id']) {
    return this.bookmarkRepository.remove(id);
  }
}
