import { Module } from '@nestjs/common';
import { ChestSizesService } from './chest-sizes.service';
import { ChestSizesController } from './chest-sizes.controller';
import { RelationalChestSizePersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Module({
  imports: [RelationalChestSizePersistenceModule],
  controllers: [ChestSizesController],
  providers: [ChestSizesService, CacheServiceHelper],
  exports: [
    ChestSizesService,
    RelationalChestSizePersistenceModule,
    CacheServiceHelper,
  ],
})
export class ChestSizesModule {}
