import { Module } from '@nestjs/common';
import { AdvertFileRepository } from '../advert-file.repository';
import { AdvertFileRelationalRepository } from './repositories/advert-file.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertFileEntity } from './entities/advert-file.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertFileEntity])],
  providers: [
    {
      provide: AdvertFileRepository,
      useClass: AdvertFileRelationalRepository,
    },
  ],
  exports: [AdvertFileRepository],
})
export class RelationalAdvertFilePersistenceModule {}
