import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { Advert } from '../../adverts/domain/advert';
import { rrssType } from '../../rrss-types/domain/rrss-type';

export class AdvertLink {
  @ApiProperty({
    type: String,
  })
  id: string;

  @ApiResponseProperty({
    type: () => Advert,
  })
  advert: Advert;

  @ApiResponseProperty({
    type: () => rrssType,
  })
  rrssType: rrssType;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  name: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
