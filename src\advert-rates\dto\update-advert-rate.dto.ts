import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateAdvertRateDto } from './create-advert-rate.dto';
import { Advert } from '../../adverts/domain/advert';
import { RateType } from '../../rate-types/domain/rate-type';

export class UpdateAdvertRateDto extends PartialType(CreateAdvertRateDto) {
  @ApiProperty({
    type: Advert,
  })
  advert: Advert;

  @ApiProperty({
    type: RateType,
  })
  rateType: RateType;

  @ApiProperty({
    type: Number,
  })
  amount: number;
}
