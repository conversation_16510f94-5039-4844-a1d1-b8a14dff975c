import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { CountryEntity } from '../infraestructure/persistence/relational/entities/country.entity';

export class CreateRegionDto {
  @ApiProperty({ type: String })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  code: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  slug: string;

  @ApiProperty({ type: CountryEntity })
  country: CountryEntity;
}
