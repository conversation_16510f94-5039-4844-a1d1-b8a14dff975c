import { AdvertRate } from '../../../../domain/advert-rate';
import { AdvertRateEntity } from '../entities/advert-rate.entity';

export class AdvertRateMapper {
  static toDomain(raw: AdvertRateEntity): AdvertRate {
    const domainEntity = new AdvertRate();
    domainEntity.id = raw.id;
    domainEntity.advert = raw.advert;
    domainEntity.rateType = raw.rateType;
    domainEntity.amount = raw.amount;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: AdvertRate): AdvertRateEntity {
    const persistenceEntity = new AdvertRateEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.advert = domainEntity.advert;
    persistenceEntity.rateType = domainEntity.rateType;
    persistenceEntity.amount = domainEntity.amount;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
