import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';
import { CountryRepository } from '../../country.repository';
import { CountryEntity } from '../entities/country.entity';
import { Country } from '../../../../domain/country';
import { CountryMapper } from '../mappers/country.mapper';
import {
  StringFields,
  NumberFields,
  DateFields,
  RelationalFields,
} from '../../../../dto/find-all-countries.dto';
import { FindAllCountriesDto } from '../../../../dto/find-all-countries.dto';

@Injectable()
export class CountryRelationalRepository implements CountryRepository {
  constructor(
    @InjectRepository(CountryEntity)
    private readonly countryRepository: Repository<CountryEntity>,
  ) {}

  async create(data: Country): Promise<Country> {
    const persistenceModel = CountryMapper.toPersistence(data);
    const newEntity = await this.countryRepository.save(
      this.countryRepository.create(persistenceModel),
    );
    return CountryMapper.toDomain(newEntity);
  }

  async findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCountriesDto,
  ): Promise<Country[]> {
    const query = this.countryRepository.createQueryBuilder('countries');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page, limit, ...updatedDto } = filters;
    if (updatedDto) {
      Object.entries(updatedDto).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (RelationalFields.includes(key)) {
            query.andWhere(`${key}.id = :${key}`, { [key]: value });
          } else if (StringFields.includes(key)) {
            query.andWhere(`LOWER(countries.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          } else if (NumberFields.includes(key)) {
            query.andWhere(`countries.${key} = :${key}`, {
              [key]: Number(value),
            });
          } else if (DateFields.includes(key)) {
            query.andWhere(`countries.${key} = :${key}`, {
              [key]: new Date(value),
            });
          }
        }
      });
    }
    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);
    const entities = await query.getMany();
    return entities.map((data) => CountryMapper.toDomain(data));
  }

  async findAll(): Promise<Country[]> {
    const entities = await this.countryRepository.find();

    return entities.map((user) => CountryMapper.toDomain(user));
  }

  async findAllAndCount(): Promise<any> {
    const countryCounts = await this.countryRepository
      .createQueryBuilder('countries')
      .innerJoinAndSelect('countries.advertLocations', 'advert_location')
      .innerJoinAndSelect('advert_location.advert', 'advert')
      .select('countries.id', 'countryId')
      .addSelect('countries.name', 'countryName')
      .addSelect('countries.slug', 'countrySlug')
      .addSelect('COUNT(advert_location.id)', 'locationCount')
      .addSelect("CONCAT(countries.name, ' (', COUNT(*), ')')", 'title')
      .where('advert.status = :status', { status: 1 })
      .groupBy('countries.id')
      .addGroupBy('countries.name')
      .getRawMany();

    // Añadir manualmente el registro con "id = 0"
    countryCounts.unshift({
      countryId: 99,
      countryName: 'All',
      locationCount: '0',
      title: 'All',
    });

    return countryCounts;
  }

  async findById(id: Country['id']): Promise<NullableType<Country>> {
    const entity = await this.countryRepository.findOne({
      where: { id },
    });

    return entity ? CountryMapper.toDomain(entity) : null;
  }

  async update(id: Country['id'], payload: Partial<Country>): Promise<Country> {
    const entity = await this.countryRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.countryRepository.save(
      this.countryRepository.create(
        CountryMapper.toPersistence({
          ...CountryMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return CountryMapper.toDomain(updatedEntity);
  }

  async remove(id: Country['id']): Promise<void> {
    await this.countryRepository.softDelete(id);
  }
}
