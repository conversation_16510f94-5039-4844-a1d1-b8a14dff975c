import { Inject, Injectable } from '@nestjs/common';
import { CreateAdvertDto } from './dto/create-advert.dto';
import { UpdateAdvertDto } from './dto/update-advert.dto';
import { AdvertRepository } from './infrastructure/persistence/advert.repository';
import { Advert } from './domain/advert';
import { FindAllAdvertLocationsDto } from './dto/find-all-advert-locations.dto';
import { GoogleApiService } from '../helpers/google-api/google-api.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Injectable()
export class AdvertsService {
  constructor(
    private readonly advertRepository: AdvertRepository,
    private readonly googleApiService: GoogleApiService,
    private readonly cacheServiceHelper: CacheServiceHelper,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  async create(createAdvertDto: CreateAdvertDto) {
    const googleData = createAdvertDto.location
      ? await this.googleApiService.getGeocodeByPlaceId(
          'es',
          createAdvertDto.location,
        )
      : null;
    const locationData =
      await this.googleApiService.transformToLocation(googleData);

    const createData = await this.advertRepository.create(
      createAdvertDto,
      locationData,
    );
    if (createData) {
      await this.cacheServiceHelper.resetCacheByAdvertId(`'${createData.id}'`);
    }

    return createData;
  }

  async findAllWithPaginationByLocationHome(query: FindAllAdvertLocationsDto) {
    return await this.advertRepository.findAllWithPaginationByLocationHome(
      query,
    );
  }

  async findAllByUser(userId: string) {
    const _cacheKey = `CACHE-ADVERT-ALL-BY-USER-${userId}`;
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertService.findAllByUser',
      _cacheKey,
      userId,
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.advertRepository.findAllByUser(userId);
    if (data) {
      await this.cacheService.set(_cacheKey, data);
      return data;
    }
  }

  async findAllSiteMap() {
    const _cacheKey = `CACHE-ADVERT-ALL-SITE-MAP`;
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertService.findAllSiteMap',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.advertRepository.findAllSiteMap();
    if (data) {
      await this.cacheService.set(_cacheKey, data);
      return data;
    }
  }

  async findOneBySlug(slug: Advert['slug']) {
    const _cacheKey = `CACHE-ADVERT-BY-SLUG-${slug}`;
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertService.findOneBySlug',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.advertRepository.findBySlug(slug);
    if (data) {
      await this.cacheService.set(_cacheKey, data);
    }
    return data;
  }

  async findOne(id: Advert['id']) {
    const _cacheKey = `CACHE-ADVERT-BY-ID-${id}`;
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertService.findOne',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.advertRepository.findById(id);
    if (data) {
      await this.cacheService.set(_cacheKey, data);
    }
    return data;
  }

  async update(id: Advert['id'], updateAdvertDto: UpdateAdvertDto) {
    const googleData = updateAdvertDto.location
      ? await this.googleApiService.getGeocodeByPlaceId(
          'es',
          updateAdvertDto.location,
        )
      : null;
    const locationData =
      await this.googleApiService.transformToLocation(googleData);

    const updateData = await this.advertRepository.update(
      id,
      updateAdvertDto,
      locationData,
    );
    await this.cacheServiceHelper.resetCacheByAdvertId(`'${id}'`);

    return updateData;
  }

  updateStatus(id: Advert['id'], updateAdvertDto: UpdateAdvertDto) {
    return this.advertRepository.updateStatus(id, updateAdvertDto);
  }

  async remove(id: Advert['id']) {
    await this.cacheServiceHelper.resetCacheByAdvertId(`'${id}'`);
    return await this.advertRepository.remove(id);
  }
}
