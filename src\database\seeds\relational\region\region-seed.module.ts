import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RegionSeedService } from './region-seed.service';
import { RegionEntity } from '../../../../location/infraestructure/persistence/relational/entities/region.entity';

@Module({
  imports: [TypeOrmModule.forFeature([RegionEntity])],
  providers: [RegionSeedService],
  exports: [RegionSeedService],
})
export class RegionSeedModule {}
