import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { AdvertLinksService } from './advert-links.service';
import { CreateAdvertLinkDto } from './dto/create-advert-link.dto';
import { UpdateAdvertLinkDto } from './dto/update-advert-link.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { AdvertLink } from './domain/advert-link';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllAdvertLinksDto } from './dto/find-all-advert-links.dto';
import { plainToInstance } from 'class-transformer';

@ApiTags('AdvertLinks')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'advert-links',
  version: '1',
})
export class AdvertLinksController {
  constructor(private readonly advert_linksService: AdvertLinksService) {}

  @Post()
  @ApiCreatedResponse({
    type: AdvertLink,
  })
  create(@Body() createAdvertLinkDto: any) {
    const data = plainToInstance(CreateAdvertLinkDto, createAdvertLinkDto);
    return this.advert_linksService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(AdvertLink),
  })
  async findAll(
    @Query() query: FindAllAdvertLinksDto,
  ): Promise<InfinityPaginationResponseDto<AdvertLink>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.advert_linksService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: AdvertLink,
  })
  async findAllWithoutPagination(): Promise<AdvertLink[]> {
    return await this.advert_linksService.findAll();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: string) {
    return this.advert_linksService.findOne(id);
  }

  @Get('advert/:advertId')
  @ApiParam({
    name: 'advertId',
    type: String,
    required: true,
  })
  findAdvertLinksByAdvert(@Param('advertId') advertId: string) {
    return this.advert_linksService.findMany(advertId);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: AdvertLink,
  })
  update(
    @Param('id') id: string,
    @Body() updateAdvertLinkDto: UpdateAdvertLinkDto,
  ) {
    return this.advert_linksService.update(id, updateAdvertLinkDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.advert_linksService.remove(id);
  }
}
