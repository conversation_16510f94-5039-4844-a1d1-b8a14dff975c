import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductEntity } from '../../../../products/infrastructure/persistence/relational/entities/product.entity';

@Injectable()
export class ProductSeedService {
  constructor(
    @InjectRepository(ProductEntity)
    private repository: Repository<ProductEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({
          id: 1,
          name: 'PROFILE ADVERT',
          description:
            'Your Profile Advert includes:\n' +
            'Optimal exposure for your services, on the industry leader’s website.\n' +
            'Multilingual customer service team for support.\n' +
            'Active and moderated forums where you can meet with clients and fellow workers.\n' +
            'Access to all upgrades for your advert.\n' +
            'Bonus - Start at no1! Your advert starts from the top left corner on the front page.',
          active: true,
          code: 'PROFILE-ADVERT',
          type: 'ADVERT',
          order: 1,
        }),
        this.repository.create({
          id: 2,
          name: 'DOUBLE ADVERT',
          description:
            'What is it ?\n' +
            'Double the size of your Profile Advert.\n' +
            '\n' +
            'Where does it appear?\n' +
            'Wherever your advert appears, it will be XL size!\n' +
            '\n' +
            'For how long ?\n' +
            '30 days for every paid period.',
          active: true,
          code: 'DOUBLE-AD',
          type: 'DAYS',
          order: 2,
        }),
        this.repository.create({
          id: 11,
          name: 'TOP ADVERT',
          description:
            'What is it?\n' +
            'Better visibility and bigger format than the Profile Advert, with a personalized design. Get more calls now!\n' +
            '\n' +
            'Where does it appear?\n' +
            'Always at the top of the page, where the punters look first.\n' +
            '\n' +
            'For how long?\n' +
            '30 days for every paid period.\n' +
            '\n' +
            'What is the position?\n' +
            'Guaranteed position in the TOP section. TOP Adverts rotate randomly within the section.',
          active: true,
          code: 'TOP-ADVERT',
          type: 'DAYS',
          order: 3,
        }),
        this.repository.create({
          id: 12,
          name: 'DOUBLE TOP ADVERT',
          description:
            'What is it?\n' +
            'The best visibility of all upgrades, and the best value for your money! Double the size of a TOP Advert with a personalized design.\n' +
            '\n' +
            'Where does it appear?\n' +
            'Always at the top of the page, where the punters look first.\n' +
            '\n' +
            'For how long?\n' +
            '30 days for every paid period.\n' +
            '\n' +
            'What is the position?\n' +
            'Guaranteed position in the TOP section. TOP Adverts rotate randomly within the section.',
          active: true,
          code: 'DOUBLE-TOP-ADVERT',
          type: 'DAYS',
          order: 4,
        }),
        this.repository.create({
          id: 50,
          name: 'AVAILABLE NOW',
          description:
            'What is it?\n' +
            'A special frame for your thumbnail with a flashing icon, with the starting time of when you are available.\n' +
            '\n' +
            'Where does it appear?\n' +
            'Wherever your advert appears, you will be tagged as Available Now.\n' +
            'Plus, you appear in the Available Now page filter.\n' +
            '\n' +
            'For how long?\n' +
            'Active for 12 hours, starting from the time that you choose.',
          active: true,
          code: 'AVAILABLE-NOW',
          type: 'ITEMS',
          order: 5,
        }),
        this.repository.create({
          id: 60,
          name: 'STORIES',
          description:
            'What is it?\n' +
            'The best alternative to gain extra visibility, quickly get the attention of potential clients with your content at the TOP of the site!\n' +
            '\n' +
            'Where does it appear?\n' +
            'Always at the top of the page, where the punters look first.\n' +
            '\n' +
            'For how long?\n' +
            'Active for 24 hours from the time that you post your stories.\n' +
            '\n' +
            'What is the position?\n' +
            'Stories change position according to how many of them are published on the site but are always at the TOP of the page.\n' +
            'Newest stories are placed first!',
          active: true,
          code: 'STORIES',
          type: 'ITEMS',
          order: 6,
        }),
        this.repository.create({
          id: 98,
          name: 'REACTIVATION',
          description:
            'What is it?\n' +
            'A reactivation is a premium product which gets you seen by potential clients by putting your advert in the prime position on the site.\n' +
            '\n' +
            'How does it work?\n' +
            'A reactivation slot moves your advert to the first position of the homepage and location pages. You choose when this takes place, giving you the chance to maximise exposure at prime times.\n' +
            '\n' +
            'For how long?\n' +
            'Adverts change position according to how many adverts we have on the page.\n' +
            'Newly activated adverts are placed first.',
          active: true,
          code: 'REACTIVATION',
          type: 'ITEMS',
          order: 7,
        }),
        this.repository.create({
          id: 99,
          name: 'WALLET FOUNDS',
          description: 'Add founds to my wallet.',
          active: false,
          code: 'WALLET-FOUNDS',
          type: 'FOUNDS',
          order: 100,
        }),
      ]);
    }
  }
}
