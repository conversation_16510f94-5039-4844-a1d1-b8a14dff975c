import { Controller, Get, Query } from '@nestjs/common';
import { CacheServiceHelper } from './cache.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Cache')
@Controller({
  path: 'cache',
  version: '1',
})
export class CacheControllerHelper {
  constructor(private readonly cacheService: CacheServiceHelper) {}

  @Get()
  addUsedCacheKey(@Query() query: any) {
    this.cacheService.addUsedCacheKey(
      query?.description,
      query?.cacheKey,
      query?.userId,
    );
  }
}
