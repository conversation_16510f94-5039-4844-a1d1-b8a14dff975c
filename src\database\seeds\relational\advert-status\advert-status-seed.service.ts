import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertStatusEntity } from '../../../../advert-statuses/infrastructure/persistence/relational/entities/advert-status.entity';
import { AdvertStatusEnum } from '../../../../advert-statuses/dto/advertStatuses.enum';

@Injectable()
export class AdvertStatusSeedService {
  constructor(
    @InjectRepository(AdvertStatusEntity)
    private repository: Repository<AdvertStatusEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({
          id: AdvertStatusEnum.active,
          name: 'Active',
          bcolor: '#22c55e',
        }),
        this.repository.create({
          id: AdvertStatusEnum.inactive,
          name: 'Inactive',
          bcolor: '#ef4444',
        }),
        this.repository.create({
          id: AdvertStatusEnum.review,
          name: 'Review',
          bcolor: '#eab308',
        }),
        this.repository.create({
          id: AdvertStatusEnum.reviewActivation,
          name: 'ReviewActivation',
          bcolor: '#78350f',
        }),
        this.repository.create({
          id: AdvertStatusEnum.canActivated,
          name: 'CanActivated',
          bcolor: '#FFFFFF',
        }),
        this.repository.create({
          id: AdvertStatusEnum.expired,
          name: 'Expired',
          bcolor: '#ef4444',
        }),
      ]);
    }
  }
}
