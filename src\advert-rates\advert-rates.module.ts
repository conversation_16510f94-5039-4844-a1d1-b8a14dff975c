import { Module } from '@nestjs/common';
import { AdvertRatesService } from './advert-rates.service';
import { AdvertRatesController } from './advert-rates.controller';
import { RelationalAdvertRatePersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [RelationalAdvertRatePersistenceModule],
  controllers: [AdvertRatesController],
  providers: [AdvertRatesService],
  exports: [AdvertRatesService, RelationalAdvertRatePersistenceModule],
})
export class AdvertRatesModule {}
