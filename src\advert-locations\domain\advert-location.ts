import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { RegionEntity } from '../../location/infraestructure/persistence/relational/entities/region.entity';
import { NeighborhoodEntity } from '../../location/infraestructure/persistence/relational/entities/neighborhood.entity';
import { CityEntity } from '../../location/infraestructure/persistence/relational/entities/city.entity';
import { CountryEntity } from '../../location/infraestructure/persistence/relational/entities/country.entity';
import { Advert } from '../../adverts/domain/advert';

const idType = Number;

export class AdvertLocation {
  @ApiResponseProperty({
    type: idType,
  })
  id: number;

  @ApiResponseProperty({
    type: () => Advert,
  })
  advert: Advert;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  name: string;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  url: string;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  googleApiResponse: string;

  @ApiResponseProperty({
    type: () => NeighborhoodEntity,
  })
  neighborhood: NeighborhoodEntity;

  @ApiResponseProperty({
    type: () => CityEntity,
  })
  city: CityEntity;

  @ApiResponseProperty({
    type: () => RegionEntity,
  })
  region: RegionEntity;

  @ApiResponseProperty({
    type: () => CountryEntity,
  })
  country: CountryEntity;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
