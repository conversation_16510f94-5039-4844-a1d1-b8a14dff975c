import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RaceSeedService } from './race-seed.service';
import { RaceEntity } from '../../../../races/infrastructure/persistence/relational/entities/race.entity';

@Module({
  imports: [TypeOrmModule.forFeature([RaceEntity])],
  providers: [RaceSeedService],
  exports: [RaceSeedService],
})
export class RaceSeedModule {}
