import { Module } from '@nestjs/common';
import { BookmarksService } from './bookmarks.service';
import { BookmarksController } from './bookmarks.controller';
import { RelationalBookmarkPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Module({
  imports: [RelationalBookmarkPersistenceModule],
  controllers: [BookmarksController],
  providers: [BookmarksService, CacheServiceHelper],
  exports: [
    BookmarksService,
    RelationalBookmarkPersistenceModule,
    CacheServiceHelper,
  ],
})
export class BookmarksModule {}
