import { Inject, Injectable } from '@nestjs/common';
import { CityRepository } from '../infraestructure/persistence/city.repository';
import { City } from '../domain/city';
import { CreateCityDto } from '../dto/create-city.dto';
import { UpdateCityDto } from '../dto/update-city.dto';
import { FindAllCitiesDto } from '../dto/find-all-cities.dto';
import { IPaginationOptions } from '../../utils/types/pagination-options';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheServiceHelper } from '../../helpers/cache/cache.service';

@Injectable()
export class CitiesService {
  constructor(
    private readonly cityRepository: CityRepository,
    private readonly cacheServiceHelper: CacheServiceHelper,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createCityDto: CreateCityDto) {
    return this.cityRepository.create(createCityDto);
  }

  findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllCitiesDto,
  ) {
    return this.cityRepository.findAllWithPagination(
      {
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      },
      filters,
    );
  }

  findAll() {
    return this.cityRepository.findAll();
  }

  async findAllAndCount() {
    const _cacheKey = 'CACHE-CITIES-ALLCOUNT';
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'citiesService.findAllAndCount',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.cityRepository.findAllAndCount();
    if (data) {
      await this.cacheService.set(_cacheKey, data);
      return data;
    }
  }

  findAllAndCountQuery(query: any) {
    return this.cityRepository.findAllAndCountQuery(query);
  }

  findOne(id: City['id']) {
    return this.cityRepository.findById(id);
  }

  async findOneBySlug(slug: City['slug']) {
    return await this.cityRepository.findBySlug(slug);
  }

  update(id: City['id'], updateCityDto: UpdateCityDto) {
    return this.cityRepository.update(id, updateCityDto);
  }

  remove(id: City['id']) {
    return this.cityRepository.remove(id);
  }
}
