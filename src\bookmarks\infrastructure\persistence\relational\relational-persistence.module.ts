import { Module } from '@nestjs/common';
import { BookmarkRepository } from '../bookmark.repository';
import { BookmarkRelationalRepository } from './repositories/bookmark.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BookmarkEntity } from './entities/bookmark.entity';

@Module({
  imports: [TypeOrmModule.forFeature([BookmarkEntity])],
  providers: [
    {
      provide: BookmarkRepository,
      useClass: BookmarkRelationalRepository,
    },
  ],
  exports: [BookmarkRepository],
})
export class RelationalBookmarkPersistenceModule {}
