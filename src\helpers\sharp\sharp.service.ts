import { Injectable } from '@nestjs/common';
import sharp from 'sharp';
import axios from 'axios';
import * as process from 'node:process';

@Injectable()
export class ImageService {
  async addWatermark(imagePath: Buffer): Promise<Buffer> {
    const frontendDomain =
      process.env.FRONTEND_DOMAIN || 'http://localhost:3001';
    const allowedDomains = frontendDomain
      .split(',')
      .map((domain) => domain.trim());
    const watermarkUrl = allowedDomains[0] + '/watermark.png';
    const watermarkBuffer = await this.downloadImage(watermarkUrl);
    const image = sharp(imagePath);
    const [imageMetadata] = await Promise.all([image.metadata()]);
    const { width, height } = imageMetadata;
    if (!width || !height) {
      throw new Error('No se pudo obtener las dimensiones de la imagen');
    }
    return await image
      .composite([
        {
          input: watermarkBuffer,
          gravity: 'north', // Posición de la marca de agua
        },
      ])
      .toBuffer();
  }

  async downloadImage(url: string): Promise<Buffer> {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    return Buffer.from(response.data);
  }
}
