import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OfferPackEntity } from '../../../../offer-packs/infrastructure/persistence/relational/entities/offer-pack.entity';
import { Repository } from 'typeorm';
import { OfferPackDetailEntity } from '../../../../offer-pack-details/infrastructure/persistence/relational/entities/offer-pack-detail.entity';

@Injectable()
export class OfferPackSeedService {
  constructor(
    @InjectRepository(OfferPackEntity)
    private repository: Repository<OfferPackEntity>,
    @InjectRepository(OfferPackDetailEntity)
    private detailRepository: Repository<OfferPackDetailEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();
    const startDate = new Date('2025-06-01 00:00:00').toISOString();
    const endDate = new Date('2026-01-01 00:00:00').toISOString();

    if (count === 0) {
      const item1 = this.repository.create({
        name: '🔥 OFERTA LANZAMIENTO ESCORTSHUB',
        description:
          'OFERTA LANZAMIENTO ESCORTSHUB 🔥\n' +
          '🚩 Anuncio básico: 25€/mes (durante 2 meses, normal 50€/mes)\n' +
          '⭐ Primer mes reactivaciones ilimitadas GRATIS\n' +
          '📸 3 historias GRATIS por semana (primer mes)\n' +
          '✅ Garantía: Si no recibes contactos en 30 días, próximo mes GRATIS.\n' +
          '¡Solo para las 200 primeras modelos!\n' +
          '📲 Apúntate AHORA y asegura tu plaza',
        startDate: startDate,
        endDate: endDate,
        qty: 1,
        qtyType: 'PACKS',
        price: 100,
        amount: 50,
        currency: 'EUR',
        activationType: 'REGISTER',
        available: 200,
        balance: 0,
        active: true,
      });

      const saved1 = await this.repository.save(item1);

      await this.detailRepository.save(
        this.detailRepository.create({
          name: 'Anuncio Básico',
          description:
            '🚩 Anuncio básico: 25€/mes\n' +
            '(durante 2 meses, normal 50€/mes)',
          offerPack: {
            id: saved1.id.toString(),
          },
          product: {
            id: 1,
          },
          qty: 2,
          qtyType: 'ITEMS',
          duration: 30,
          durationType: 'DAYS',
        }),
      );

      await this.detailRepository.save(
        this.detailRepository.create({
          name: 'Reactivaciones',
          description: '⭐ Primer mes reactivaciones ilimitadas GRATIS',
          offerPack: {
            id: saved1.id.toString(),
          },
          product: {
            id: 98,
          },
          qty: 9999,
          qtyType: 'ITEMS',
          duration: 0,
          durationType: 'DAYS',
        }),
      );

      await this.detailRepository.save(
        this.detailRepository.create({
          name: 'Stories',
          description: '📸 3 historias GRATIS por semana (primer mes)',
          offerPack: {
            id: saved1.id.toString(),
          },
          product: {
            id: 60,
          },
          qty: 12,
          qtyType: 'ITEMS',
          duration: 24,
          durationType: 'HOURS',
        }),
      );

      const item2 = this.repository.create({
        name: '🚀 MÁXIMA VISIBILIDAD',
        description:
          '🚀 Pack Máxima Visibilidad opcional:\n' +
          '(+40€/mes) Anuncio TOP + Disponible Ahora ilimitado',
        startDate: startDate,
        endDate: endDate,
        qty: 1,
        qtyType: 'PACKS',
        price: 40,
        amount: 40,
        currency: 'EUR',
        activationType: 'REGISTER-OPTIONAL',
        available: 200,
        balance: 0,
        active: true,
      });

      const saved2 = await this.repository.save(item2);

      await this.detailRepository.save(
        this.detailRepository.create({
          name: 'Anuncios TOP ilimitados',
          description: 'Anuncios TOP ilimitados durante un mes',
          offerPack: {
            id: saved2.id.toString(),
          },
          product: {
            id: 11,
          },
          qty: 9999,
          qtyType: 'ITEMS',
          duration: 30,
          durationType: 'DAYS',
        }),
      );

      await this.detailRepository.save(
        this.detailRepository.create({
          name: 'Disponible Ahora Ilimitado',
          description: 'Disponible Ahora Ilimitado durante un mes',
          offerPack: {
            id: saved2.id.toString(),
          },
          product: {
            id: 50,
          },
          qty: 9999,
          qtyType: 'ITEMS',
          duration: 12,
          durationType: 'HOURS',
        }),
      );
    }
  }
}
