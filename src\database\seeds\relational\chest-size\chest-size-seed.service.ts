import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChestSizeEntity } from '../../../../chest-sizes/infrastructure/persistence/relational/entities/chest-size.entity';

@Injectable()
export class ChestSizeSeedService {
  constructor(
    @InjectRepository(ChestSizeEntity)
    private repository: Repository<ChestSizeEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ name: 'Big' }),
        this.repository.create({ name: 'Giant' }),
        this.repository.create({ name: 'Medium' }),
        this.repository.create({ name: 'Normal' }),
        this.repository.create({ name: 'Perfect Size' }),
        this.repository.create({ name: 'Silicone' }),
        this.repository.create({ name: 'Small' }),
        this.repository.create({ name: 'Voluptuous' }),
        this.repository.create({ name: 'Other' }),
      ]);
    }
  }
}
