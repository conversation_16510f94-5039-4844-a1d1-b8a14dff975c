import { Inject, Injectable } from '@nestjs/common';
import { CreateChestSizeDto } from './dto/create-chest-size.dto';
import { UpdateChestSizeDto } from './dto/update-chest-size.dto';
import { ChestSizeRepository } from './infrastructure/persistence/chest-size.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { ChestSize } from './domain/chest-size';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Injectable()
export class ChestSizesService {
  constructor(
    private readonly chest_sizeRepository: ChestSizeRepository,
    private readonly cacheServiceHelper: CacheServiceHelper,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createChestSizeDto: CreateChestSizeDto) {
    return this.chest_sizeRepository.create(createChestSizeDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.chest_sizeRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  async findAll() {
    const _cacheKey = 'CACHE-CHEST-SIZES-ALL';
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'chestSizesService.findAll',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.chest_sizeRepository.findAll();
    if (data) {
      return await this.cacheService.set(_cacheKey, data);
    }
  }

  findOne(id: ChestSize['id']) {
    return this.chest_sizeRepository.findById(id);
  }

  update(id: ChestSize['id'], updateChestSizeDto: UpdateChestSizeDto) {
    return this.chest_sizeRepository.update(id, updateChestSizeDto);
  }

  remove(id: ChestSize['id']) {
    return this.chest_sizeRepository.remove(id);
  }
}
