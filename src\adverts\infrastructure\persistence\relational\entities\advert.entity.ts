import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { GenderEntity } from '../../../../../genders/infrastructure/persistence/relational/entities/gender.entity';
import { UserEntity } from '../../../../../users/infrastructure/persistence/relational/entities/user.entity';
import { Gender } from '../../../../../genders/domain/gender';
import { User } from '../../../../../users/domain/user';
import { AdvertTypeEntity } from '../../../../../advert-types/infrastructure/persistence/relational/entities/advert-type.entity';
import { AdvertType } from '../../../../../advert-types/domain/advert-type';
import { NationalityEntity } from '../../../../../nationalities/infrastructure/persistence/relational/entities/nationality.entity';
import { Nationality } from '../../../../../nationalities/domain/nationality';
import { ChestSizeEntity } from '../../../../../chest-sizes/infrastructure/persistence/relational/entities/chest-size.entity';
import { ChestSize } from '../../../../../chest-sizes/domain/chest-size';
import { ServiceEntity } from '../../../../../services/infrastructure/persistence/relational/entities/service.entity';
import { LanguageEntity } from '../../../../../languages/infrastructure/persistence/relational/entities/language.entity';
import { Language } from '../../../../../languages/domain/language';
import { AdvertStatusEntity } from '../../../../../advert-statuses/infrastructure/persistence/relational/entities/advert-status.entity';
import { AdvertStatus } from '../../../../../advert-statuses/domain/advert-status';
import { Service } from '../../../../../services/domain/service';
import { OrientationEntity } from '../../../../../orientations/infrastructure/persistence/relational/entities/orientation.entity';
import { Orientation } from '../../../../../orientations/domain/orientation';
import { EyeColorEntity } from '../../../../../eye-colors/infrastructure/persistence/relational/entities/eye-color.entity';
import { EyeColor } from '../../../../../eye-colors/domain/eye-color';
import { HairColorEntity } from '../../../../../hair-colors/infrastructure/persistence/relational/entities/hair-color.entity';
import { HairColor } from '../../../../../hair-colors/domain/hair-color';
import { RaceEntity } from '../../../../../races/infrastructure/persistence/relational/entities/race.entity';
import { Race } from '../../../../../races/domain/race';
import { AdvertLink } from '../../../../../advert-links/domain/advert-link';
import { AdvertLinkEntity } from '../../../../../advert-links/infrastructure/persistence/relational/entities/advert-link.entity';
import { AdvertRateEntity } from '../../../../../advert-rates/infrastructure/persistence/relational/entities/advert-rate.entity';
import { AdvertRate } from '../../../../../advert-rates/domain/advert-rate';
import { AdvertFileEntity } from '../../../../../advert-files/infrastructure/persistence/relational/entities/advert-file.entity';
import { AdvertFile } from '../../../../../advert-files/domain/advert-file';
import { AdvertLocationEntity } from '../../../../../advert-locations/infrastructure/persistence/relational/entities/advert-location.entity';
import { AdvertLocation } from '../../../../../advert-locations/domain/advert-location';
import { SubscriptionEntity } from '../../../../../subscriptions/infrastructure/persistence/relational/entities/subscription.entity';
import { Subscription } from '../../../../../subscriptions/domain/subscription';

@Entity({
  name: 'advert',
})
export class AdvertEntity extends EntityRelationalHelper {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  @Column({ default: null })
  @Expose()
  profileName: string;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  @Column({ default: null })
  @Expose()
  slug: string;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  @Column({ default: null })
  @Expose({ groups: ['me', 'admin'] })
  phoneNumber: string;

  @ApiResponseProperty({
    type: Boolean,
  })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  whatsapp: boolean;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  @Column({ default: null })
  @Expose({ groups: ['me', 'admin'] })
  introduction: string;

  @ApiResponseProperty({
    type: () => GenderEntity,
  })
  @ManyToOne(() => GenderEntity, {
    eager: true,
  })
  gender: Gender;

  @ApiResponseProperty({
    type: () => AdvertTypeEntity,
  })
  @ManyToOne(() => AdvertTypeEntity, {
    eager: true,
  })
  advertType: AdvertType;

  @ApiResponseProperty({
    type: Number,
  })
  @Column({ default: 18 })
  @Expose({ groups: ['me', 'admin'] })
  birthDate: number;

  @ApiResponseProperty({
    type: Number,
  })
  @Column({ default: 0 })
  @Expose({ groups: ['me', 'admin'] })
  height: number;

  @ApiResponseProperty({
    type: Number,
  })
  @Column({ default: 0 })
  @Expose({ groups: ['me', 'admin'] })
  breastSize: number;

  @ApiResponseProperty({
    type: () => ChestSizeEntity,
  })
  @ManyToOne(() => ChestSizeEntity, {
    eager: true,
  })
  chestSize: ChestSize;

  @ApiResponseProperty({
    type: Number,
  })
  @Column({ default: 0 })
  @Expose({ groups: ['me', 'admin'] })
  waist: number;

  @ApiResponseProperty({
    type: Number,
  })
  @Column({ default: 0 })
  @Expose({ groups: ['me', 'admin'] })
  hips: number;

  @ApiResponseProperty({
    type: () => OrientationEntity,
  })
  @ManyToOne(() => OrientationEntity, {
    eager: true,
  })
  orientation: Orientation;

  @ApiResponseProperty({
    type: () => EyeColorEntity,
  })
  @ManyToOne(() => EyeColorEntity, {
    eager: true,
  })
  eyeColor: EyeColor;

  @ApiResponseProperty({
    type: () => HairColorEntity,
  })
  @ManyToOne(() => HairColorEntity, {
    eager: true,
  })
  hairColor: HairColor;

  @ApiResponseProperty({
    type: () => RaceEntity,
  })
  @ManyToOne(() => RaceEntity, {
    eager: true,
  })
  race: Race;

  @ApiResponseProperty({
    type: Boolean,
  })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  smoke: boolean;

  @ApiResponseProperty({ type: String })
  @Column({ default: '' })
  @Expose({ groups: ['me', 'admin'] })
  location: string;

  @ApiResponseProperty({ type: String })
  @Column({ default: '' })
  @Expose({ groups: ['me', 'admin'] })
  email: string;

  @ApiResponseProperty({ type: String })
  @Column({ default: '' })
  @Expose({ groups: ['me', 'admin'] })
  www: string;

  @ApiResponseProperty({ type: String })
  @Column({ default: '' })
  @Expose({ groups: ['me', 'admin'] })
  timetable: string;

  @ApiResponseProperty({
    type: () => NationalityEntity,
  })
  @ManyToOne(() => NationalityEntity, {
    eager: true,
  })
  nationality: Nationality;

  @ApiResponseProperty({
    type: () => LanguageEntity,
  })
  @ManyToMany(() => LanguageEntity, (language) => language.adverts)
  @JoinTable({
    name: 'advert_languages',
  })
  languages: Language[];

  @ApiResponseProperty({
    type: () => ServiceEntity,
  })
  @ManyToMany(() => ServiceEntity, (service) => service.adverts)
  @JoinTable({
    name: 'advert_services',
  })
  services: Service[];

  @ApiResponseProperty({
    type: () => AdvertStatusEntity,
  })
  @ManyToOne(() => AdvertStatusEntity, {
    eager: true,
  })
  status: AdvertStatus;

  @ApiResponseProperty({
    type: Boolean,
  })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  featured: boolean;

  @ApiResponseProperty({
    type: Number,
  })
  @Column({ default: 0 })
  @Expose({ groups: ['me', 'admin'] })
  @Column('decimal', { precision: 20, scale: 2, default: 0.0 })
  rating: number;

  @ApiResponseProperty({
    type: () => UserEntity,
  })
  @ManyToOne(() => UserEntity, {
    eager: true,
  })
  user: User;

  @ApiResponseProperty({
    type: () => AdvertLinkEntity,
  })
  @OneToMany(() => AdvertLinkEntity, (advertLink) => advertLink.advert)
  @ApiProperty({ type: () => [AdvertLink] })
  advertLinks: AdvertLink[];

  @ApiResponseProperty({
    type: () => AdvertRateEntity,
  })
  @OneToMany(() => AdvertRateEntity, (advertRate) => advertRate.advert)
  @ApiProperty({ type: () => [AdvertRate] })
  advertRates: AdvertRate[];

  @ApiResponseProperty({
    type: () => AdvertFileEntity,
  })
  @OneToMany(() => AdvertFileEntity, (advertFile) => advertFile.advert)
  @ApiProperty({ type: () => [AdvertFile] })
  advertFiles: AdvertFile[];

  @ApiResponseProperty({
    type: () => AdvertLocationEntity,
  })
  @OneToMany(
    () => AdvertLocationEntity,
    (advertLocation) => advertLocation.advert,
  )
  @ApiProperty({ type: () => [AdvertLocation] })
  advertLocations: AdvertLocation[];

  @ApiResponseProperty({
    type: () => SubscriptionEntity,
  })
  @OneToMany(() => SubscriptionEntity, (subscription) => subscription.advert)
  @ApiProperty({ type: () => [Subscription] })
  subscriptions: Subscription[];

  @ApiResponseProperty({ type: Boolean })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  oTopAdvert: boolean;

  @ApiResponseProperty({ type: Boolean })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  oTopDoubleAdvert: boolean;

  @ApiResponseProperty({ type: Boolean })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  oDoubleAdvert: boolean;

  @ApiResponseProperty({ type: Boolean })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  oAvailableNow: boolean;

  @ApiResponseProperty({ type: Boolean })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  oReactivate: boolean;

  @ApiResponseProperty({ type: Boolean })
  @Column({ default: false })
  @Expose({ groups: ['me', 'admin'] })
  oTopStories: boolean;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
