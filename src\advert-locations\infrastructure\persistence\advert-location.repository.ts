import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { AdvertLocation } from '../../domain/advert-location';
import { FindAllAdvertLocationsDto } from '../../dto/find-all-advert-locations.dto';

export abstract class AdvertLocationRepository {
  abstract create(
    data: Omit<AdvertLocation, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<AdvertLocation>;

  abstract findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllAdvertLocationsDto,
  ): Promise<AdvertLocation[]>;

  abstract findAll(): Promise<AdvertLocation[]>;

  abstract findById(
    id: AdvertLocation['id'],
  ): Promise<NullableType<AdvertLocation>>;

  abstract update(
    id: AdvertLocation['id'],
    payload: DeepPartial<AdvertLocation>,
  ): Promise<AdvertLocation | null>;

  abstract remove(id: AdvertLocation['id']): Promise<void>;
}
