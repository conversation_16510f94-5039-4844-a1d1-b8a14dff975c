'use client';

import useSubmit from '@/hooks/useSubmit';
import { SignUp } from '@/models/auth';
import { User } from '@/models/user';
import { registerUser } from '@/services/AuthService';
import { regexEmail, regexPassword } from '@/utils/regex';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React, { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import {
  ClientIcon,
  ProfessionalIcon,
  RoleCard,
} from '@/components/auth/RoleCard';
import ButtonPrimary from '@/components/commons/ButtonPrimary';
import FieldError from '@/components/commons/FieldError';
import FieldInput from '@/components/commons/FieldInput';

const ROLES = {
  PROFESSIONAL: 2,
  CLIENT: 3,
};

const RegisterForm: React.FC = () => {
  const [selectedRole, setSelectedRole] = useState(ROLES.PROFESSIONAL);
  const [isFinish, setIsFinish] = useState(false);
  const tValidates = useTranslations('validates');
  const tAuth = useTranslations('auth');
  const tInput = useTranslations('input');
  const tUserDetail = useTranslations('userDetail');
  const {
    register,
    formState: { errors },
    handleSubmit,
    getValues,
  } = useForm<SignUp>();
  const { error, setError, isLoading, doSubmit } = useSubmit<SignUp, void>();

  const onSubmit: SubmitHandler<SignUp> = async data => {
    try {
      if (!data) return setError(tValidates('empty'));
      const user: User = {
        ...data,
        role: { id: selectedRole },
      };
      await doSubmit({
        data: user,
        callback: async userData => {
          await registerUser(userData, selectedRole);
        },
      });
    } catch (error) {
      /* empty */
    } finally {
      setIsFinish(true);
    }
  };

  return (
    <>
      {isFinish ? (
        <div className="w-full flex flex-col justify-center items-center animate-fade mt-2">
          <p className="form-subtitle mb-6 dark:text-white">
            {tAuth('confirm.description-register')}
          </p>
          <Image
            src="/svg/product-receive-a-notification.svg"
            alt="product-receive-a-notification"
            width={150}
            height={150}
            className="bg-cover animate-bounce transition-all"
          />
        </div>
      ) : (
        <>
          <div>
            <p className="text-sm font-semibold text-gray-700 mb-4">
              {tAuth('iam')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <RoleCard
                icon={
                  <ProfessionalIcon
                    selected={selectedRole === ROLES.PROFESSIONAL}
                  />
                }
                title={tAuth('roles.professional')}
                description={tAuth('roles.professional-desc')}
                selected={selectedRole === ROLES.PROFESSIONAL}
                onClick={() => setSelectedRole(ROLES.PROFESSIONAL)}
              />
              <RoleCard
                icon={<ClientIcon selected={selectedRole === ROLES.CLIENT} />}
                title={tAuth('roles.client')}
                description={tAuth('roles.client-desc')}
                selected={selectedRole === ROLES.CLIENT}
                onClick={() => setSelectedRole(ROLES.CLIENT)}
              />
            </div>
          </div>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="form-initial mt-8"
            suppressHydrationWarning
          >
            <div>
              <FieldInput
                label={tInput('name')}
                id="name"
                name="firstName"
                placeholder="Pepe"
                error={errors.firstName?.message}
                register={register}
                rules={{
                  required: { value: true, message: tValidates('name') },
                }}
                isRequired={true}
              />
            </div>
            <div>
              <FieldInput
                label={tUserDetail('lastName')}
                id="lastName"
                name="lastName"
                placeholder="Pepe"
                error={errors.lastName?.message}
                register={register}
                rules={{
                  required: { value: true, message: tValidates('name') },
                }}
                isRequired={true}
              />
            </div>
            <div>
              <FieldInput
                label={tInput('email')}
                id="email"
                name="email"
                placeholder="<EMAIL>"
                error={errors.email?.message}
                register={register}
                rules={{
                  required: {
                    value: true,
                    message: tValidates('email'),
                  },
                  pattern: {
                    value: regexEmail,
                    message: tValidates('invalidEmail'),
                  },
                }}
                isRequired={true}
              />
            </div>
            <div>
              <FieldInput
                label={tInput('password')}
                type="password"
                name="password"
                id="password"
                error={errors.password?.message}
                register={register}
                rules={{
                  required: {
                    value: true,
                    message: tValidates('password.required'),
                  },
                  minLength: {
                    value: 8,
                    message: tValidates('password.eight'),
                  },
                  pattern: {
                    value: regexPassword,
                    message: tValidates('password.regex'),
                  },
                }}
                isRequired={true}
                placeholder="••••••••••"
              />
            </div>
            <div>
              <FieldInput
                label={tInput('confirmPassword')}
                id="confirmPassword"
                name="confirmPassword"
                error={errors.confirmPassword?.message as string}
                register={register}
                rules={{
                  required: {
                    value: true,
                    message: tValidates('confirmPassword.required'),
                  },
                  minLength: {
                    value: 8,
                    message: tValidates('confirmPassword.eight'),
                  },
                  validate: value => {
                    const { password } = getValues();
                    if (password !== value)
                      return tValidates('confirmPassword.equal');
                    return true;
                  },
                  pattern: {
                    value: regexPassword,
                    message: tValidates('confirmPassword.regex'),
                  },
                }}
                type="password"
                isRequired={true}
                placeholder="••••••••••"
              />
            </div>
            <div className="w-full">
              <ButtonPrimary loading={isLoading} type="submit">
                {tAuth('register')}
              </ButtonPrimary>
            </div>
            <div className="w-full text-center">
              <FieldError error={error} />
            </div>
          </form>
        </>
      )}
    </>
  );
};

export default RegisterForm;
