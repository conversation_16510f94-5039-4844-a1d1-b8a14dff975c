import { Module } from '@nestjs/common';
import { HairColorRepository } from '../hair-color.repository';
import { HairColorRelationalRepository } from './repositories/hair-color.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HairColorEntity } from './entities/hair-color.entity';

@Module({
  imports: [TypeOrmModule.forFeature([HairColorEntity])],
  providers: [
    {
      provide: HairColorRepository,
      useClass: HairColorRelationalRepository,
    },
  ],
  exports: [HairColorRepository],
})
export class RelationalHairColorPersistenceModule {}
