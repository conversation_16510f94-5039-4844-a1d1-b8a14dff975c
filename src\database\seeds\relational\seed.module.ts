import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DataSource, DataSourceOptions } from 'typeorm';
import { TypeOrmConfigService } from '../../typeorm-config.service';

import { StatusSeedModule } from './status/status-seed.module';
import { RoleSeedModule } from './role/role-seed.module';
import { UserSeedModule } from './user/user-seed.module';
import { AdvertStatusSeedModule } from './advert-status/advert-status-seed.module';
import { AdvertTypeSeedModule } from './advert-type/advert-type-seed.module';
import { ChestSizeSeedModule } from './chest-size/chest-size-seed.module';
import { EyeColorSeedModule } from './eye-color/eye-color-seed.module';
import { GenderSeedModule } from './gender/gender-seed.module';
import { HairColorSeedModule } from './hair-color/hair-color-seed.module';
import { LanguageSeedModule } from './language/language-seed.module';
import { NationalitySeedModule } from './nationality/nationality-seed.module';
import { OrientationSeedModule } from './orientation/orientation-seed.module';
import { RaceSeedModule } from './race/race-seed.module';
import { ServiceTypeSeedModule } from './service-type/service-type-seed.module';
import { ServiceSeedModule } from './service/service-seed.module';
import { rrssTypeSeedModule } from './rrss-type/rrss-type-seed.module';
import { rateTypeSeedModule } from './rate-type/rate-type-seed.module';
import { CountrySeedModule } from './country/country-seed.module';
import { RegionSeedModule } from './region/region-seed.module';
import { CitySeedModule } from './city/city-seed.module';
import { ProductSeedModule } from './product/product-seed.module';
import { ProductDetailSeedModule } from './product-detail/product-detail-seed.module';
import { TransactionStatusSeedModule } from './transaction-status/transaction-status-seed.module';
import { TranslationSeedModule } from './translation/translation-seed.module';

import databaseConfig from '../../config/database.config';
import appConfig from '../../../config/app.config';

import { OfferPackSeedModule } from './offer-pack/offer-pack-seed.module';

@Module({
  imports: [
    OfferPackSeedModule,
    StatusSeedModule,
    RoleSeedModule,
    UserSeedModule,
    AdvertStatusSeedModule,
    AdvertTypeSeedModule,
    ChestSizeSeedModule,
    EyeColorSeedModule,
    GenderSeedModule,
    HairColorSeedModule,
    LanguageSeedModule,
    NationalitySeedModule,
    OrientationSeedModule,
    RaceSeedModule,
    ServiceTypeSeedModule,
    ServiceSeedModule,
    rrssTypeSeedModule,
    rateTypeSeedModule,
    ProductSeedModule,
    ProductDetailSeedModule,
    TransactionStatusSeedModule,
    TranslationSeedModule,
    CountrySeedModule,
    RegionSeedModule,
    CitySeedModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, appConfig],
      envFilePath: ['.env'],
    }),
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
      dataSourceFactory: async (options: DataSourceOptions) => {
        return new DataSource(options).initialize();
      },
    }),
  ],
})
export class SeedModule {}
