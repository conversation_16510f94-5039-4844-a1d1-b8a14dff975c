import { Module } from '@nestjs/common';
import { AdvertsService } from './adverts.service';
import { AdvertsController } from './adverts.controller';
import { RelationalAdvertPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';
import { GoogleApiService } from '../helpers/google-api/google-api.service';
import { CacheServiceHelper } from '../helpers/cache/cache.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [RelationalAdvertPersistenceModule, HttpModule],
  controllers: [AdvertsController],
  providers: [AdvertsService, GoogleApiService, CacheServiceHelper],
  exports: [
    AdvertsService,
    GoogleApiService,
    CacheServiceHelper,
    RelationalAdvertPersistenceModule,
  ],
})
export class AdvertsModule {}
