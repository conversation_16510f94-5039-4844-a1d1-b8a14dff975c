import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertStatusSeedService } from './advert-status-seed.service';
import { AdvertStatusEntity } from '../../../../advert-statuses/infrastructure/persistence/relational/entities/advert-status.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertStatusEntity])],
  providers: [AdvertStatusSeedService],
  exports: [AdvertStatusSeedService],
})
export class AdvertStatusSeedModule {}
