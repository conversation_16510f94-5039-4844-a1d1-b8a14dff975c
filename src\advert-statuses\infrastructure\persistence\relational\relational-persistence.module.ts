import { Module } from '@nestjs/common';
import { AdvertStatusRepository } from '../advert-status.repository';
import { AdvertStatusRelationalRepository } from './repositories/advert-status.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertStatusEntity } from './entities/advert-status.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertStatusEntity])],
  providers: [
    {
      provide: AdvertStatusRepository,
      useClass: AdvertStatusRelationalRepository,
    },
  ],
  exports: [AdvertStatusRepository],
})
export class RelationalAdvertStatusPersistenceModule {}
