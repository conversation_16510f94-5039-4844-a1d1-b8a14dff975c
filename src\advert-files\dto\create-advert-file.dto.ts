import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Advert } from '../../adverts/domain/advert';
import { Subscription } from '../../subscriptions/domain/subscription';

export class CreateAdvertFileDto {
  @ApiPropertyOptional({ type: Advert })
  @Type(() => Advert)
  advert: Advert;

  @ApiProperty({ type: Number })
  type: number;

  @ApiProperty({ type: String })
  mimeType: string;

  @ApiProperty({ type: String })
  file: string;

  @ApiProperty({ type: Number })
  order: number;

  @ApiProperty({ type: Boolean })
  main: boolean;

  @ApiProperty({ type: Boolean })
  validated: boolean;

  @ApiProperty({ type: Date })
  validatedAt: Date;

  @ApiPropertyOptional({ type: Subscription })
  @Type(() => Subscription)
  subscription: Subscription;
}
