import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { Advert } from '../../adverts/domain/advert';
import { Type } from 'class-transformer';
import { RateType } from '../../rate-types/domain/rate-type';

export class AdvertRate {
  @ApiResponseProperty({
    type: String,
  })
  id: string;

  @ApiResponseProperty({
    type: () => Advert,
  })
  advert: Advert;

  @ApiResponseProperty({ type: RateType })
  @Type(() => RateType)
  rateType: RateType;

  @ApiResponseProperty({
    type: Number,
    example: 0,
  })
  amount: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
