import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Repository } from 'typeorm';
import bcrypt from 'bcryptjs';
import { RoleEnum } from '../../../../roles/roles.enum';
import { StatusEnum } from '../../../../statuses/dto/statuses.enum';
import { UserEntity } from '../../../../users/infrastructure/persistence/relational/entities/user.entity';
import { DataSource } from 'typeorm';
import { WalletStatus } from '../../../../wallets/domain/wallet';

@Injectable()
export class UserSeedService {
  constructor(
    @InjectRepository(UserEntity)
    private repository: Repository<UserEntity>,
    private readonly dataSource: DataSource,
  ) {}

  async run() {
    const countAdmin = await this.repository.count({
      where: {
        role: {
          id: RoleEnum.admin,
        },
      },
    });

    if (!countAdmin) {
      const salt = await bcrypt.genSalt();
      const password = await bcrypt.hash('secret', salt);

      const user = this.repository.create({
        firstName: 'Super',
        lastName: 'Admin',
        email: '<EMAIL>',
        password,
        role: {
          id: RoleEnum.admin,
          name: 'Admin',
        },
        status: {
          id: StatusEnum.active,
          name: 'Active',
        },
      });

      const saved = await this.repository.save(user);

      await this.dataSource
        .createQueryBuilder()
        .insert()
        .into('wallet')
        .values({
          name: 'Admin Wallet',
          balance: 0,
          currency: 'HUB',
          status: WalletStatus.ACTIVE,
          user: {
            id: saved.id,
          },
        })
        .execute();
    }

    const countUser = await this.repository.count({
      where: {
        role: {
          id: RoleEnum.user,
        },
      },
    });

    if (!countUser) {
      const salt = await bcrypt.genSalt();
      const password = await bcrypt.hash('secret', salt);

      const user = this.repository.create({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password,
        role: {
          id: RoleEnum.user,
          name: 'User',
        },
        status: {
          id: StatusEnum.active,
          name: 'Active',
        },
      });

      const saved = await this.repository.save(user);

      await this.dataSource
        .createQueryBuilder()
        .insert()
        .into('wallet')
        .values({
          name: 'J.Doe Wallet',
          balance: 0,
          currency: 'HUB',
          status: WalletStatus.ACTIVE,
          user: {
            id: saved.id,
          },
        })
        .execute();
    }
  }
}
