import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AllConfigType } from '../config/config.type';

@Injectable()
export class HomeService {
  constructor(private configService: ConfigService<AllConfigType>) {}

  appInfo() {
    const infoApi = {
      apiVersion: '1.0.0',
      baseUrl: 'https://api.example.com',
      documentationUrl: 'https://api.example.com/docs',
      termsOfServiceUrl: 'https://api.example.com/terms',
      contactEmail: '<EMAIL>',
      timestamp: '2024-06-01T12:34:56Z',
      status: 'success',
      message: 'Request completed successfully.',
      pagination: {
        currentPage: 1,
        totalPages: 10,
        pageSize: 20,
        totalItems: 200,
      },
      user: {
        userId: '12345',
        userName: 'john doe',
        userEmail: '<EMAIL>',
        userRoles: ['admin', 'editor'],
      },
      app: {
        appName: 'My Awesome App',
        appVersion: '2.3.1',
        releaseDate: '2024-05-20',
      },
      system: {
        serverTime: '2024-06-01T12:34:56Z',
        uptime: '48 hours',
        serverStatus: 'operational',
      },
      links: {
        previousPageUrl: null,
        nextPageUrl: 'https://api.example.com/resources?page=2',
        relatedResources: [
          'https://api.example.com/related1',
          'https://api.example.com/related2',
        ],
      },
      environment: 'production',
      featureFlags: {
        newFeatureEnabled: true,
        betaFeatureEnabled: false,
      },
      configurationSettings: {
        theme: 'dark',
        notificationsEnabled: true,
      },
      data: {
        // Aquí va la estructura de datos principal que devuelve tu API
      },
    };
    return {
      name: this.configService.get('app.name', { infer: true }),
      test: 'test',
      info: infoApi,
    };
  }
}
