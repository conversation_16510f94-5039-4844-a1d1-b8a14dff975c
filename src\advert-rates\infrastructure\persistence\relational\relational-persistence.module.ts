import { Module } from '@nestjs/common';
import { AdvertRateRepository } from '../advert-rate.repository';
import { AdvertRateRelationalRepository } from './repositories/advert-rate.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertRateEntity } from './entities/advert-rate.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertRateEntity])],
  providers: [
    {
      provide: AdvertRateRepository,
      useClass: AdvertRateRelationalRepository,
    },
  ],
  exports: [AdvertRateRepository],
})
export class RelationalAdvertRatePersistenceModule {}
