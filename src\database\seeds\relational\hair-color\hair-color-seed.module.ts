import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { HairColorSeedService } from './hair-color-seed.service';
import { HairColorEntity } from '../../../../hair-colors/infrastructure/persistence/relational/entities/hair-color.entity';

@Module({
  imports: [TypeOrmModule.forFeature([HairColorEntity])],
  providers: [HairColorSeedService],
  exports: [HairColorSeedService],
})
export class HairColorSeedModule {}
