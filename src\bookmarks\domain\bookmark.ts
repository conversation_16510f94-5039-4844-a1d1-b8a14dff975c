import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { User } from '../../users/domain/user';
import { Advert } from '../../adverts/domain/advert';

export class Bookmark {
  @ApiProperty({
    type: String,
  })
  id: string;

  @ApiResponseProperty({
    type: String,
    example: '',
  })
  name: string;

  @ApiResponseProperty({
    type: () => User,
  })
  user: User;

  @ApiResponseProperty({
    type: () => Advert,
  })
  advert: Advert;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
