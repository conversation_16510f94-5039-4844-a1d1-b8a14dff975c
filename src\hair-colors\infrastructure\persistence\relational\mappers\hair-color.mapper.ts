import { BaseEntityMapper } from '../../../../../utils/mappers/base-entity-mapper';
import { HairColor } from '../../../../domain/hair-color';
import { HairColorEntity } from '../entities/hair-color.entity';

export class HairColorMapper {
  private static baseMapper = new (class extends BaseEntityMapper<
    HairColor,
    HairColorEntity
  > {})();

  static toPersistence(domainEntity: HairColor): HairColorEntity {
    const persistenceEntity = new HairColorEntity();
    return this.baseMapper.mapToPersistenceCommon(
      domainEntity,
      persistenceEntity,
    );
  }

  static toDomain(raw: HairColorEntity): HairColor {
    const domainEntity = new HairColor();
    return this.baseMapper.mapToDomainCommon(raw, domainEntity);
  }
}
