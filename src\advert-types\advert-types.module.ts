import { Module } from '@nestjs/common';
import { AdvertTypesService } from './advert-types.service';
import { AdvertTypesController } from './advert-types.controller';
import { RelationalAdvertTypePersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Module({
  imports: [RelationalAdvertTypePersistenceModule],
  controllers: [AdvertTypesController],
  providers: [AdvertTypesService, CacheServiceHelper],
  exports: [
    AdvertTypesService,
    RelationalAdvertTypePersistenceModule,
    CacheServiceHelper,
  ],
})
export class AdvertTypesModule {}
