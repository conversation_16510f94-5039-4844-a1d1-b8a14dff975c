import { Injectable } from '@nestjs/common';
import { CreateAdvertLinkDto } from './dto/create-advert-link.dto';
import { UpdateAdvertLinkDto } from './dto/update-advert-link.dto';
import { AdvertLinkRepository } from './infrastructure/persistence/advert-link.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { AdvertLink } from './domain/advert-link';

@Injectable()
export class AdvertLinksService {
  constructor(private readonly advert_linkRepository: AdvertLinkRepository) {}

  create(createAdvertLinkDto: CreateAdvertLinkDto) {
    return this.advert_linkRepository.create(createAdvertLinkDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.advert_linkRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  findAll() {
    return this.advert_linkRepository.findAll();
  }

  findOne(id: AdvertLink['id']) {
    return this.advert_linkRepository.findById(id);
  }

  findMany(advertId: string) {
    return this.advert_linkRepository.findMany(advertId);
  }

  update(id: AdvertLink['id'], updateAdvertLinkDto: UpdateAdvertLinkDto) {
    return this.advert_linkRepository.update(id, updateAdvertLinkDto);
  }

  remove(id: AdvertLink['id']) {
    return this.advert_linkRepository.remove(id);
  }
}
