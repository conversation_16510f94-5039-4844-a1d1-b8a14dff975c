import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindAllRegionsDto {
  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 1))
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 50))
  @IsNumber()
  @IsOptional()
  limit?: number;

  @IsOptional()
  name?: string;

  @IsOptional()
  code?: string;

  @IsOptional()
  slug?: string;

  @IsOptional()
  country?: number;
}

export const StringFields = ['name', 'code'];
export const RelationalFields = ['country'];
export const NumberFields = [''];
export const DateFields = [''];
