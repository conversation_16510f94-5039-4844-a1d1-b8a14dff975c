import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RegionEntity } from '../entities/region.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { Region } from '../../../../domain/region';
import { RegionRepository } from '../../region.repository';
import { RegionMapper } from '../mappers/region.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';
import {
  DateFields,
  FindAllRegionsDto,
  NumberFields,
  RelationalFields,
  StringFields,
} from '../../../../dto/find-all-regions.dto';

@Injectable()
export class RegionRelationalRepository implements RegionRepository {
  constructor(
    @InjectRepository(RegionEntity)
    private readonly regionRepository: Repository<RegionEntity>,
  ) {}

  async create(data: Region): Promise<Region> {
    const persistenceModel = RegionMapper.toPersistence(data);
    const newEntity = await this.regionRepository.save(
      this.regionRepository.create(persistenceModel),
    );
    return RegionMapper.toDomain(newEntity);
  }

  async findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllRegionsDto,
  ): Promise<Region[]> {
    const query = this.regionRepository.createQueryBuilder('region');
    query.leftJoinAndSelect('region.country', 'country');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page, limit, ...updatedDto } = filters;
    if (updatedDto) {
      Object.entries(updatedDto).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (RelationalFields.includes(key)) {
            query.andWhere(`${key}.id = :${key}`, { [key]: value });
          } else if (StringFields.includes(key)) {
            query.andWhere(`LOWER(region.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          } else if (NumberFields.includes(key)) {
            query.andWhere(`region.${key} = :${key}`, {
              [key]: Number(value),
            });
          } else if (DateFields.includes(key)) {
            query.andWhere(`region.${key} = :${key}`, {
              [key]: new Date(value),
            });
          }
        }
      });
    }
    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);

    const entities = await query.getMany();

    return entities.map((entity) => RegionMapper.toDomain(entity));
  }

  async findAll(): Promise<Region[]> {
    const entities = await this.regionRepository.find({
      relations: ['country'],
    });

    return entities.map((element) => RegionMapper.toDomain(element));
  }

  async findAllAndCount(): Promise<any> {
    return await this.regionRepository
      .createQueryBuilder('region')
      .innerJoinAndSelect('region.advertLocations', 'advert_location')
      .innerJoinAndSelect('advert_location.advert', 'advert')
      .select('region.id', 'regionId')
      .addSelect('region.name', 'regionName')
      .addSelect('region.slug', 'regionSlug')
      .addSelect('COUNT(advert_location.id)', 'locationCount')
      .addSelect("CONCAT(region.name, ' (', COUNT(*), ')')", 'title')
      .where('advert.status = :status', { status: 1 })
      .groupBy('region.id')
      .addGroupBy('region.name')
      .orderBy('COUNT(advert_location.id)', 'DESC')
      .getRawMany();
  }

  async findById(id: Region['id']): Promise<NullableType<Region>> {
    const entity = await this.regionRepository.findOne({
      where: { id },
      relations: ['country'],
    });

    return entity ? RegionMapper.toDomain(entity) : null;
  }

  async update(id: Region['id'], payload: Partial<Region>): Promise<Region> {
    const entity = await this.regionRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.regionRepository.save(
      this.regionRepository.create(
        RegionMapper.toPersistence({
          ...RegionMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return RegionMapper.toDomain(updatedEntity);
  }

  async remove(id: Region['id']): Promise<void> {
    await this.regionRepository.softDelete(id);
  }
}
