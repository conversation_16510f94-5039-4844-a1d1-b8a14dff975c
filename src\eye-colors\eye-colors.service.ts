import { Inject, Injectable } from '@nestjs/common';
import { CreateEyeColorDto } from './dto/create-eye-color.dto';
import { UpdateEyeColorDto } from './dto/update-eye-color.dto';
import { EyeColorRepository } from './infrastructure/persistence/eye-color.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { EyeColor } from './domain/eye-color';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class EyeColorsService {
  constructor(
    private readonly eye_colorRepository: EyeColorRepository,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createEyeColorDto: CreateEyeColorDto) {
    return this.eye_colorRepository.create(createEyeColorDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.eye_colorRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  async findAll() {
    const _key = 'CACHE-EYE-COLORS-ALL';
    const cachedData = await this.cacheService.get<{ name: string }>(_key);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.eye_colorRepository.findAll();
    if (data) {
      return await this.cacheService.set(_key, data);
    }
  }

  findOne(id: EyeColor['id']) {
    return this.eye_colorRepository.findById(id);
  }

  update(id: EyeColor['id'], updateEyeColorDto: UpdateEyeColorDto) {
    return this.eye_colorRepository.update(id, updateEyeColorDto);
  }

  remove(id: EyeColor['id']) {
    return this.eye_colorRepository.remove(id);
  }
}
