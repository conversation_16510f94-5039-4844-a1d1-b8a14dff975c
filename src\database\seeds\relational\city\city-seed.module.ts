import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CitySeedService } from './city-seed.service';
import { CityEntity } from '../../../../location/infraestructure/persistence/relational/entities/city.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CityEntity])],
  providers: [CitySeedService],
  exports: [CitySeedService],
})
export class CitySeedModule {}
