import { MigrationInterface, QueryRunner } from 'typeorm';

export class AllTablesAndSeeders1748899661304 implements MigrationInterface {
  name = 'AllTablesAndSeeders1748899661304';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "translation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "language" character varying NOT NULL, "ref" character varying NOT NULL, "key" character varying NOT NULL, "field" character varying NOT NULL, "value" text NOT NULL, "status" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_7aef875e43ab80d34a0cdd39c70" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "role" ("id" integer NOT NULL, "name" character varying NOT NULL, CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "status" ("id" integer NOT NULL, "name" character varying NOT NULL, CONSTRAINT "PK_e12743a7086ec826733f54e1d95" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "city" ("id" SERIAL NOT NULL, "name" character varying, "code" character varying, "slug" character varying, "defaultLocation" character varying NOT NULL DEFAULT '', "region_id" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_b222f51ce26f7e5ca86944a6739" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "neighborhood" ("id" SERIAL NOT NULL, "name" character varying, "slug" character varying, "city_id" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_97797961be30242a5170d17caec" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "gender" ("id" SERIAL NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_98a711129bc073e6312d08364e8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_715cef762c43bdc30e83bea161" ON "gender" ("name") `,
    );
    await queryRunner.query(
      `CREATE TABLE "advert_type" ("id" integer NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_0daffa89cb3c4ac83ccb944861c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "nationality" ("id" SERIAL NOT NULL, "code" character varying, "name" character varying, "en" character varying, "es" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_ec4111a2e9f11d6b69312e4a77f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "chest_size" ("id" SERIAL NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_b7a91ed53302cdc9e90b0a70bd5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "service_type" ("id" integer NOT NULL, "name" character varying, "slug" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_0a11a8d444eff1346826caed987" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "service" ("id" SERIAL NOT NULL, "name" character varying, "slug" character varying, "en" character varying, "es" character varying, "wiki_en" character varying, "wiki_es" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "serviceTypeId" integer, CONSTRAINT "PK_85a21558c006647cd76fdce044b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7806a14d42c3244064b4a1706c" ON "service" ("name") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4df47ef659e04d5be78ddb6b59" ON "service" ("slug") `,
    );
    await queryRunner.query(
      `CREATE TABLE "language" ("id" SERIAL NOT NULL, "code" character varying, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_cc0a99e710eb3733f6fb42b1d4c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7df7d1e250ea2a416f078a631f" ON "language" ("name") `,
    );
    await queryRunner.query(
      `CREATE TABLE "advert_status" ("id" integer NOT NULL, "name" character varying, "bcolor" character varying NOT NULL DEFAULT '#FFFFFF', "fcolor" character varying NOT NULL DEFAULT '#000000', "icolor" character varying NOT NULL DEFAULT '#FF0000', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_073fd96f9c86f371733b517b0c7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "orientation" ("id" SERIAL NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_855d7b7a876102fbb47cba97d15" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "eye_color" ("id" SERIAL NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_0fc681c4ccb38b833f6ed6a0e81" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "hair_color" ("id" SERIAL NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_24a88381c64998f4e87dd369c22" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "race" ("id" SERIAL NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_a3068b184130d87a20e516045bb" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "rrss_type" ("id" SERIAL NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_12fd90adf516b12db725d2678a5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "advert_link" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "advertId" uuid, "rrssTypeId" integer, CONSTRAINT "PK_e7f7107e272d19d1a38a8a0b8c1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "rate_type" ("id" SERIAL NOT NULL, "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_80a7a15248ccd54f740097dc28c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "advert_rate" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "amount" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "advertId" uuid, "rateTypeId" integer, CONSTRAINT "PK_e353ddc7afad7c9c30592842b3b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "advert_file" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" integer NOT NULL DEFAULT '1', "mimeType" character varying NOT NULL DEFAULT '', "file" character varying NOT NULL DEFAULT '', "order" integer NOT NULL DEFAULT '0', "main" boolean NOT NULL DEFAULT false, "validated" boolean NOT NULL DEFAULT false, "validatedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "advertId" uuid, CONSTRAINT "PK_c945fa0bdc0df97bd0147f2d03a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "product" ("id" integer NOT NULL, "name" character varying, "code" character varying, "type" character varying NOT NULL DEFAULT 'ADVERT', "description" character varying, "active" boolean NOT NULL DEFAULT false, "order" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_bebc9158e480b949565b4dc7a82" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "product_detail" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying, "qty" numeric(20,2) NOT NULL DEFAULT '0', "qtyType" character varying NOT NULL DEFAULT 'DAYS', "price" numeric(20,2) NOT NULL DEFAULT '0', "amount" numeric(20,2) NOT NULL DEFAULT '0', "currency" character varying NOT NULL DEFAULT 'EUR', "duration" numeric(20,2) NOT NULL DEFAULT '0', "durationType" character varying NOT NULL DEFAULT 'DAYS', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "productId" integer, CONSTRAINT "PK_12ea67a439667df5593ff68fc33" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "subscription" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "startDate" TIMESTAMP, "endDate" TIMESTAMP, "status" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid, "productId" integer, "advertId" uuid, CONSTRAINT "PK_8c3e00ebd02103caa1174cd5d9d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "advert" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "profileName" character varying, "slug" character varying, "phoneNumber" character varying, "whatsapp" boolean NOT NULL DEFAULT false, "introduction" character varying, "birthDate" integer NOT NULL DEFAULT '18', "height" integer NOT NULL DEFAULT '0', "breastSize" integer NOT NULL DEFAULT '0', "waist" integer NOT NULL DEFAULT '0', "hips" integer NOT NULL DEFAULT '0', "smoke" boolean NOT NULL DEFAULT false, "location" character varying NOT NULL DEFAULT '', "email" character varying NOT NULL DEFAULT '', "www" character varying NOT NULL DEFAULT '', "timetable" character varying NOT NULL DEFAULT '', "featured" boolean NOT NULL DEFAULT false, "rating" numeric(20,2) NOT NULL DEFAULT '0', "oTopAdvert" boolean NOT NULL DEFAULT false, "oTopDoubleAdvert" boolean NOT NULL DEFAULT false, "oDoubleAdvert" boolean NOT NULL DEFAULT false, "oAvailableNow" boolean NOT NULL DEFAULT false, "oReactivate" boolean NOT NULL DEFAULT false, "oTopStories" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "genderId" integer, "advertTypeId" integer, "chestSizeId" integer, "orientationId" integer, "eyeColorId" integer, "hairColorId" integer, "raceId" integer, "nationalityId" integer, "statusId" integer, "userId" uuid, CONSTRAINT "PK_4bd8b4cdfb562b02706beece450" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "advert_location" ("id" SERIAL NOT NULL, "name" character varying, "url" character varying, "googleApiResponse" text NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "advertId" uuid, "neighborhoodId" integer, "cityId" integer, "regionId" integer, "countryId" integer, CONSTRAINT "PK_2ba9c48919057dec3e38fbb6891" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "countries" ("id" SERIAL NOT NULL, "name" character varying, "code" character varying, "slug" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_b2d7006793e8697ab3ae2deff18" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "region" ("id" SERIAL NOT NULL, "name" character varying, "code" character varying, "slug" character varying, "country_id" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_5f48ffc3af96bc486f5f3f3a6da" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_phone" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "phoneNumber" character varying NOT NULL DEFAULT '', "validated" boolean NOT NULL DEFAULT false, "validatedAt" TIMESTAMP, "validatedResponse" text NOT NULL, "status" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid, CONSTRAINT "PK_8b544a5b4edf9ab1e479c5466f3" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying, "password" character varying, "provider" character varying NOT NULL DEFAULT 'email', "socialId" character varying, "firstName" character varying, "lastName" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "roleId" integer, "statusId" integer, CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9bd2fe7a8e694dedc4ec2f666f" ON "user" ("socialId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_58e4dbff0e1a32a9bdc861bb29" ON "user" ("firstName") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f0e1b4ecdca13b177e2e3a0613" ON "user" ("lastName") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."wallet_status_enum" AS ENUM('active', 'suspended', 'closed')`,
    );
    await queryRunner.query(
      `CREATE TABLE "wallet" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying, "balance" numeric(10,2) NOT NULL DEFAULT '0', "currency" character varying(10) NOT NULL DEFAULT 'EUR', "status" "public"."wallet_status_enum" NOT NULL DEFAULT 'active', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid, CONSTRAINT "PK_bec464dd8d54c39c54fd32e2334" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "transactionstatus" ("id" SERIAL NOT NULL, "name" character varying, "description" character varying, "state" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_79e4d46f2fc411d59b9ecd31ad4" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."transaction_type_enum" AS ENUM('deposit', 'withdraw', 'payment', 'refund', 'hub')`,
    );
    await queryRunner.query(
      `CREATE TABLE "transaction" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "transactionDate" TIMESTAMP, "description" character varying, "provider" character varying, "providerResponse" character varying, "price" numeric(20,2) NOT NULL DEFAULT '0', "amount" numeric(20,2) NOT NULL DEFAULT '0', "qty" numeric(20,2) NOT NULL DEFAULT '0', "qtyType" character varying NOT NULL DEFAULT 'DAYS', "type" "public"."transaction_type_enum" NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "transactionStatusId" integer, "walletId" uuid, "productId" integer, "productDetailId" uuid, "userId" uuid, CONSTRAINT "PK_89eadb93a89810556e1cbcd6ab9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "session" ("id" SERIAL NOT NULL, "hash" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid, CONSTRAINT "PK_f55da76ac1c3ac420f444d2ff11" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3d2f174ef04fb312fdebd0ddc5" ON "session" ("userId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "statistic" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" character varying NOT NULL, "keyType" character varying NOT NULL, "keyValue" character varying NOT NULL, "counter" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_e3e6fd496e1988019d8a46749ae" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1220344f99fc7855e55ec1e583" ON "statistic" ("type") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_543e58a1fb605838fa15dd763f" ON "statistic" ("keyType") `,
    );
    await queryRunner.query(
      `CREATE TABLE "bookmark" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid, "advertId" uuid, CONSTRAINT "PK_b7fbf4a865ba38a590bb9239814" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "cache" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "cacheDate" TIMESTAMP, "description" character varying, "cacheKey" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid, CONSTRAINT "PK_c1aeeb8cd7a17a7698cf64e1a8d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "advert_languages" ("advertId" uuid NOT NULL, "languageId" integer NOT NULL, CONSTRAINT "PK_01669e7d70165586d03bde988d1" PRIMARY KEY ("advertId", "languageId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_774ee2a5b2c0ab81380098ae26" ON "advert_languages" ("advertId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4c02e748e3490d107de9bd6cb3" ON "advert_languages" ("languageId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "advert_services" ("advertId" uuid NOT NULL, "serviceId" integer NOT NULL, CONSTRAINT "PK_d35060636ede84ab8c1b7e6bd9a" PRIMARY KEY ("advertId", "serviceId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6110262ae9d63b17fcd5cb933d" ON "advert_services" ("advertId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2e747eecaac2a363ce760e078d" ON "advert_services" ("serviceId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "city" ADD CONSTRAINT "FK_0b663dca66456beb75ec93de9fc" FOREIGN KEY ("region_id") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "neighborhood" ADD CONSTRAINT "FK_afa8388e34d9117a5ec01da4764" FOREIGN KEY ("city_id") REFERENCES "city"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "service" ADD CONSTRAINT "FK_72b9571152bcfe5e9c60f95cd6e" FOREIGN KEY ("serviceTypeId") REFERENCES "service_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_link" ADD CONSTRAINT "FK_f2c6101ec767b62898ab6eb29d0" FOREIGN KEY ("advertId") REFERENCES "advert"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_link" ADD CONSTRAINT "FK_5d55da3f0505ae80a6391cebfdf" FOREIGN KEY ("rrssTypeId") REFERENCES "rrss_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_rate" ADD CONSTRAINT "FK_e01a5258f5ef6541d8b01c8417a" FOREIGN KEY ("advertId") REFERENCES "advert"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_rate" ADD CONSTRAINT "FK_c59a5fe36f977c4cb08bdd57fe8" FOREIGN KEY ("rateTypeId") REFERENCES "rate_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_file" ADD CONSTRAINT "FK_79a950621c59a6e20376ca089bb" FOREIGN KEY ("advertId") REFERENCES "advert"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_detail" ADD CONSTRAINT "FK_2c1471f10d59111c8d052b0bdbc" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscription" ADD CONSTRAINT "FK_cc906b4bc892b048f1b654d2aa0" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscription" ADD CONSTRAINT "FK_da4246cc4749275d90f63d76f5a" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscription" ADD CONSTRAINT "FK_326a0c78dfa5b76bab6097aa669" FOREIGN KEY ("advertId") REFERENCES "advert"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_8ccd60261770f617f911c0f92b1" FOREIGN KEY ("genderId") REFERENCES "gender"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_b67710ac6b4104bb5621e95ad12" FOREIGN KEY ("advertTypeId") REFERENCES "advert_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_2b93c3fff337eaa814f643c9ca6" FOREIGN KEY ("chestSizeId") REFERENCES "chest_size"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_8f834523f802fa868c9c522456a" FOREIGN KEY ("orientationId") REFERENCES "orientation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_f075501c15f6fcb47b3034d06de" FOREIGN KEY ("eyeColorId") REFERENCES "eye_color"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_a42d990806226e87134020dd17b" FOREIGN KEY ("hairColorId") REFERENCES "hair_color"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_74a96015353fa8edbecc657d5c2" FOREIGN KEY ("raceId") REFERENCES "race"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_e7dfc912909953413cb154c8e73" FOREIGN KEY ("nationalityId") REFERENCES "nationality"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_08522d756f79b764e190b495e59" FOREIGN KEY ("statusId") REFERENCES "advert_status"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" ADD CONSTRAINT "FK_2a3714047a0c902fd9d5077fdcb" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" ADD CONSTRAINT "FK_c77a6a9506921ebc94c9d3cd4b8" FOREIGN KEY ("advertId") REFERENCES "advert"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" ADD CONSTRAINT "FK_7e63b0b295b81fac7b72a1a25b1" FOREIGN KEY ("neighborhoodId") REFERENCES "neighborhood"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" ADD CONSTRAINT "FK_1b5f186058dbd44951b7528fecd" FOREIGN KEY ("cityId") REFERENCES "city"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" ADD CONSTRAINT "FK_c22b3d8595d6c332826f81fd8c5" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" ADD CONSTRAINT "FK_8fefa28216804babbea6f694bcd" FOREIGN KEY ("countryId") REFERENCES "countries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ADD CONSTRAINT "FK_26b43e0b19bc5dc2c480da151b6" FOREIGN KEY ("country_id") REFERENCES "countries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_phone" ADD CONSTRAINT "FK_1a921efc6d5620e9018fd304825" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_c28e52f758e7bbc53828db92194" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_dc18daa696860586ba4667a9d31" FOREIGN KEY ("statusId") REFERENCES "status"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "wallet" ADD CONSTRAINT "FK_35472b1fe48b6330cd349709564" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" ADD CONSTRAINT "FK_acb226c33f2e9022e76d04ca1dc" FOREIGN KEY ("transactionStatusId") REFERENCES "transactionstatus"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" ADD CONSTRAINT "FK_900eb6b5efaecf57343e4c0e79d" FOREIGN KEY ("walletId") REFERENCES "wallet"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" ADD CONSTRAINT "FK_fd965536176f304a7dd64937165" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" ADD CONSTRAINT "FK_ae5d29ac8e047412ce150380a7b" FOREIGN KEY ("productDetailId") REFERENCES "product_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" ADD CONSTRAINT "FK_605baeb040ff0fae995404cea37" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "session" ADD CONSTRAINT "FK_3d2f174ef04fb312fdebd0ddc53" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bookmark" ADD CONSTRAINT "FK_e389fc192c59bdce0847ef9ef8b" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bookmark" ADD CONSTRAINT "FK_7abeb42356af147840053fcfdde" FOREIGN KEY ("advertId") REFERENCES "advert"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cache" ADD CONSTRAINT "FK_65083d4226a0834a763882e899e" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_languages" ADD CONSTRAINT "FK_774ee2a5b2c0ab81380098ae26f" FOREIGN KEY ("advertId") REFERENCES "advert"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_languages" ADD CONSTRAINT "FK_4c02e748e3490d107de9bd6cb37" FOREIGN KEY ("languageId") REFERENCES "language"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_services" ADD CONSTRAINT "FK_6110262ae9d63b17fcd5cb933dc" FOREIGN KEY ("advertId") REFERENCES "advert"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_services" ADD CONSTRAINT "FK_2e747eecaac2a363ce760e078d9" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "advert_services" DROP CONSTRAINT "FK_2e747eecaac2a363ce760e078d9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_services" DROP CONSTRAINT "FK_6110262ae9d63b17fcd5cb933dc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_languages" DROP CONSTRAINT "FK_4c02e748e3490d107de9bd6cb37"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_languages" DROP CONSTRAINT "FK_774ee2a5b2c0ab81380098ae26f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cache" DROP CONSTRAINT "FK_65083d4226a0834a763882e899e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bookmark" DROP CONSTRAINT "FK_7abeb42356af147840053fcfdde"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bookmark" DROP CONSTRAINT "FK_e389fc192c59bdce0847ef9ef8b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "session" DROP CONSTRAINT "FK_3d2f174ef04fb312fdebd0ddc53"`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" DROP CONSTRAINT "FK_605baeb040ff0fae995404cea37"`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" DROP CONSTRAINT "FK_ae5d29ac8e047412ce150380a7b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" DROP CONSTRAINT "FK_fd965536176f304a7dd64937165"`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" DROP CONSTRAINT "FK_900eb6b5efaecf57343e4c0e79d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "transaction" DROP CONSTRAINT "FK_acb226c33f2e9022e76d04ca1dc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "wallet" DROP CONSTRAINT "FK_35472b1fe48b6330cd349709564"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_dc18daa696860586ba4667a9d31"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_c28e52f758e7bbc53828db92194"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_phone" DROP CONSTRAINT "FK_1a921efc6d5620e9018fd304825"`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" DROP CONSTRAINT "FK_26b43e0b19bc5dc2c480da151b6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" DROP CONSTRAINT "FK_8fefa28216804babbea6f694bcd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" DROP CONSTRAINT "FK_c22b3d8595d6c332826f81fd8c5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" DROP CONSTRAINT "FK_1b5f186058dbd44951b7528fecd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" DROP CONSTRAINT "FK_7e63b0b295b81fac7b72a1a25b1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_location" DROP CONSTRAINT "FK_c77a6a9506921ebc94c9d3cd4b8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_2a3714047a0c902fd9d5077fdcb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_08522d756f79b764e190b495e59"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_e7dfc912909953413cb154c8e73"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_74a96015353fa8edbecc657d5c2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_a42d990806226e87134020dd17b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_f075501c15f6fcb47b3034d06de"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_8f834523f802fa868c9c522456a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_2b93c3fff337eaa814f643c9ca6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_b67710ac6b4104bb5621e95ad12"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert" DROP CONSTRAINT "FK_8ccd60261770f617f911c0f92b1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscription" DROP CONSTRAINT "FK_326a0c78dfa5b76bab6097aa669"`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscription" DROP CONSTRAINT "FK_da4246cc4749275d90f63d76f5a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscription" DROP CONSTRAINT "FK_cc906b4bc892b048f1b654d2aa0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_detail" DROP CONSTRAINT "FK_2c1471f10d59111c8d052b0bdbc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_file" DROP CONSTRAINT "FK_79a950621c59a6e20376ca089bb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_rate" DROP CONSTRAINT "FK_c59a5fe36f977c4cb08bdd57fe8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_rate" DROP CONSTRAINT "FK_e01a5258f5ef6541d8b01c8417a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_link" DROP CONSTRAINT "FK_5d55da3f0505ae80a6391cebfdf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "advert_link" DROP CONSTRAINT "FK_f2c6101ec767b62898ab6eb29d0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "service" DROP CONSTRAINT "FK_72b9571152bcfe5e9c60f95cd6e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "neighborhood" DROP CONSTRAINT "FK_afa8388e34d9117a5ec01da4764"`,
    );
    await queryRunner.query(
      `ALTER TABLE "city" DROP CONSTRAINT "FK_0b663dca66456beb75ec93de9fc"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2e747eecaac2a363ce760e078d"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6110262ae9d63b17fcd5cb933d"`,
    );
    await queryRunner.query(`DROP TABLE "advert_services"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4c02e748e3490d107de9bd6cb3"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_774ee2a5b2c0ab81380098ae26"`,
    );
    await queryRunner.query(`DROP TABLE "advert_languages"`);
    await queryRunner.query(`DROP TABLE "cache"`);
    await queryRunner.query(`DROP TABLE "bookmark"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_543e58a1fb605838fa15dd763f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1220344f99fc7855e55ec1e583"`,
    );
    await queryRunner.query(`DROP TABLE "statistic"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3d2f174ef04fb312fdebd0ddc5"`,
    );
    await queryRunner.query(`DROP TABLE "session"`);
    await queryRunner.query(`DROP TABLE "transaction"`);
    await queryRunner.query(`DROP TYPE "public"."transaction_type_enum"`);
    await queryRunner.query(`DROP TABLE "transactionstatus"`);
    await queryRunner.query(`DROP TABLE "wallet"`);
    await queryRunner.query(`DROP TYPE "public"."wallet_status_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_f0e1b4ecdca13b177e2e3a0613"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_58e4dbff0e1a32a9bdc861bb29"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9bd2fe7a8e694dedc4ec2f666f"`,
    );
    await queryRunner.query(`DROP TABLE "user"`);
    await queryRunner.query(`DROP TABLE "user_phone"`);
    await queryRunner.query(`DROP TABLE "region"`);
    await queryRunner.query(`DROP TABLE "countries"`);
    await queryRunner.query(`DROP TABLE "advert_location"`);
    await queryRunner.query(`DROP TABLE "advert"`);
    await queryRunner.query(`DROP TABLE "subscription"`);
    await queryRunner.query(`DROP TABLE "product_detail"`);
    await queryRunner.query(`DROP TABLE "product"`);
    await queryRunner.query(`DROP TABLE "advert_file"`);
    await queryRunner.query(`DROP TABLE "advert_rate"`);
    await queryRunner.query(`DROP TABLE "rate_type"`);
    await queryRunner.query(`DROP TABLE "advert_link"`);
    await queryRunner.query(`DROP TABLE "rrss_type"`);
    await queryRunner.query(`DROP TABLE "race"`);
    await queryRunner.query(`DROP TABLE "hair_color"`);
    await queryRunner.query(`DROP TABLE "eye_color"`);
    await queryRunner.query(`DROP TABLE "orientation"`);
    await queryRunner.query(`DROP TABLE "advert_status"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7df7d1e250ea2a416f078a631f"`,
    );
    await queryRunner.query(`DROP TABLE "language"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4df47ef659e04d5be78ddb6b59"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7806a14d42c3244064b4a1706c"`,
    );
    await queryRunner.query(`DROP TABLE "service"`);
    await queryRunner.query(`DROP TABLE "service_type"`);
    await queryRunner.query(`DROP TABLE "chest_size"`);
    await queryRunner.query(`DROP TABLE "nationality"`);
    await queryRunner.query(`DROP TABLE "advert_type"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_715cef762c43bdc30e83bea161"`,
    );
    await queryRunner.query(`DROP TABLE "gender"`);
    await queryRunner.query(`DROP TABLE "neighborhood"`);
    await queryRunner.query(`DROP TABLE "city"`);
    await queryRunner.query(`DROP TABLE "status"`);
    await queryRunner.query(`DROP TABLE "role"`);
    await queryRunner.query(`DROP TABLE "translation"`);
  }
}
