import { Advert } from '../../../../domain/advert';
import { AdvertEntity } from '../entities/advert.entity';

export class AdvertMapper {
  static toDomain(raw: AdvertEntity): Advert {
    const domainEntity = new Advert();
    domainEntity.id = raw.id;
    domainEntity.profileName = raw.profileName;
    domainEntity.slug = raw.slug;
    domainEntity.phoneNumber = raw.phoneNumber;
    domainEntity.whatsapp = raw.whatsapp;
    domainEntity.introduction = raw.introduction;
    domainEntity.gender = raw.gender;
    domainEntity.advertType = raw.advertType;
    domainEntity.birthDate = raw.birthDate;
    domainEntity.height = raw.height;
    domainEntity.breastSize = raw.breastSize;
    domainEntity.chestSize = raw.chestSize;
    domainEntity.waist = raw.waist;
    domainEntity.hips = raw.hips;
    domainEntity.orientation = raw.orientation;
    domainEntity.eyeColor = raw.eyeColor;
    domainEntity.hairColor = raw.hairColor;
    domainEntity.race = raw.race;
    domainEntity.smoke = raw.smoke;
    domainEntity.location = raw.location;
    domainEntity.email = raw.email;
    domainEntity.www = raw.www;
    domainEntity.timetable = raw.timetable;
    domainEntity.nationality = raw.nationality;
    domainEntity.languages = raw.languages;
    domainEntity.services = raw.services;
    domainEntity.status = raw.status;
    domainEntity.featured = raw.featured;
    domainEntity.rating = raw.rating;
    domainEntity.user = raw.user;
    domainEntity.advertLinks = raw.advertLinks;
    domainEntity.advertRates = raw.advertRates;
    domainEntity.subscriptions = raw.subscriptions;
    domainEntity.advertFiles = raw.advertFiles;
    domainEntity.advertLocations = raw.advertLocations;
    domainEntity.oTopAdvert = raw.oTopAdvert;
    domainEntity.oTopDoubleAdvert = raw.oTopDoubleAdvert;
    domainEntity.oDoubleAdvert = raw.oDoubleAdvert;
    domainEntity.oAvailableNow = raw.oAvailableNow;
    domainEntity.oReactivate = raw.oReactivate;
    domainEntity.oTopStories = raw.oTopStories;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: Advert): AdvertEntity {
    const persistenceEntity = new AdvertEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.profileName = domainEntity.profileName;
    persistenceEntity.slug = domainEntity.slug;
    persistenceEntity.phoneNumber = domainEntity.phoneNumber;
    persistenceEntity.whatsapp = domainEntity.whatsapp;
    persistenceEntity.introduction = domainEntity.introduction;
    persistenceEntity.gender = domainEntity.gender;
    persistenceEntity.advertType = domainEntity.advertType;
    persistenceEntity.birthDate = domainEntity.birthDate;
    persistenceEntity.height = domainEntity.height;
    persistenceEntity.breastSize = domainEntity.breastSize;
    persistenceEntity.chestSize = domainEntity.chestSize;
    persistenceEntity.waist = domainEntity.waist;
    persistenceEntity.hips = domainEntity.hips;
    persistenceEntity.orientation = domainEntity.orientation;
    persistenceEntity.eyeColor = domainEntity.eyeColor;
    persistenceEntity.hairColor = domainEntity.hairColor;
    persistenceEntity.race = domainEntity.race;
    persistenceEntity.smoke = domainEntity.smoke;
    persistenceEntity.location = domainEntity.location;
    persistenceEntity.email = domainEntity.email;
    persistenceEntity.www = domainEntity.www;
    persistenceEntity.timetable = domainEntity.timetable;
    persistenceEntity.nationality = domainEntity.nationality;
    persistenceEntity.languages = domainEntity.languages;
    persistenceEntity.services = domainEntity.services;
    persistenceEntity.status = domainEntity.status;
    persistenceEntity.featured = domainEntity.featured;
    persistenceEntity.rating = domainEntity.rating;
    persistenceEntity.user = domainEntity.user;
    persistenceEntity.advertLinks = domainEntity.advertLinks;
    persistenceEntity.advertRates = domainEntity.advertRates;
    persistenceEntity.subscriptions = domainEntity.subscriptions;
    persistenceEntity.advertFiles = domainEntity.advertFiles;
    persistenceEntity.advertLocations = domainEntity.advertLocations;
    persistenceEntity.oTopAdvert = domainEntity.oTopAdvert;
    persistenceEntity.oTopDoubleAdvert = domainEntity.oTopDoubleAdvert;
    persistenceEntity.oDoubleAdvert = domainEntity.oDoubleAdvert;
    persistenceEntity.oAvailableNow = domainEntity.oAvailableNow;
    persistenceEntity.oReactivate = domainEntity.oReactivate;
    persistenceEntity.oTopStories = domainEntity.oTopStories;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
