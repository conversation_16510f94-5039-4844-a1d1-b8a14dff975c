import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { AdvertEntity } from '../../../../../adverts/infrastructure/persistence/relational/entities/advert.entity';
import { Advert } from '../../../../../adverts/domain/advert';
import { SubscriptionEntity } from '../../../../../subscriptions/infrastructure/persistence/relational/entities/subscription.entity';
import { Subscription } from '../../../../../subscriptions/domain/subscription';

@Entity({
  name: 'advert_file',
})
export class AdvertFileEntity extends EntityRelationalHelper {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiResponseProperty({ type: () => AdvertEntity })
  @ManyToOne(() => AdvertEntity, (advert) => advert.advertFiles)
  @ApiProperty({ type: () => Advert })
  advert: Advert;

  @ApiResponseProperty({ type: Number })
  @Column({ default: 1 })
  type: number;

  @ApiResponseProperty({ type: String })
  @Column({ default: '' })
  mimeType: string;

  @ApiResponseProperty({ type: String })
  @Column({ default: '' })
  file: string;

  @ApiResponseProperty({ type: Number })
  @Column({ default: 0 })
  order: number;

  @ApiResponseProperty({ type: Boolean })
  @Column({ default: false })
  main: boolean;

  @ApiResponseProperty({ type: Boolean })
  @Column({ default: false })
  validated: boolean;

  @ApiResponseProperty({ type: Date })
  @Column({ default: null })
  validatedAt: Date;

  @ApiResponseProperty({ type: () => SubscriptionEntity })
  @ManyToOne(
    () => SubscriptionEntity,
    (subscription) => subscription.advertFiles,
  )
  @ApiProperty({ type: () => Advert })
  subscription: Subscription;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
