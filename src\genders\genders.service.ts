import { Inject, Injectable } from '@nestjs/common';
import { CreateGenderDto } from './dto/create-gender.dto';
import { UpdateGenderDto } from './dto/update-gender.dto';
import { GenderRepository } from './infrastructure/persistence/gender.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { Gender } from './domain/gender';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class GendersService {
  constructor(
    private readonly genderRepository: GenderRepository,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createGenderDto: CreateGenderDto) {
    return this.genderRepository.create(createGenderDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.genderRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  async findAll() {
    const _key = 'CACHE-GENDERS-ALL';
    const cachedData = await this.cacheService.get<{ name: string }>(_key);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.genderRepository.findAll();
    if (data) {
      return await this.cacheService.set(_key, data);
    }
  }

  findOne(id: Gender['id']) {
    return this.genderRepository.findById(id);
  }

  update(id: Gender['id'], updateGenderDto: UpdateGenderDto) {
    return this.genderRepository.update(id, updateGenderDto);
  }

  remove(id: Gender['id']) {
    return this.genderRepository.remove(id);
  }
}
