import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { Gender } from '../../domain/gender';

export abstract class GenderRepository {
  abstract create(
    data: Omit<Gender, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Gender>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<Gender[]>;

  abstract findAll(): Promise<Gender[]>;

  abstract findById(id: Gender['id']): Promise<NullableType<Gender>>;

  abstract update(
    id: Gender['id'],
    payload: DeepPartial<Gender>,
  ): Promise<Gender | null>;

  abstract remove(id: Gender['id']): Promise<void>;
}
