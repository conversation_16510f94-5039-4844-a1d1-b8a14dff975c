import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { HairColorsService } from './hair-colors.service';
import { CreateHairColorDto } from './dto/create-hair-color.dto';
import { UpdateHairColorDto } from './dto/update-hair-color.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { HairColor } from './domain/hair-color';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllHairColorsDto } from './dto/find-all-hair-colors.dto';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiTags('HairColors')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'hair-colors',
  version: '1',
})
export class HairColorsController {
  constructor(private readonly hair_colorsService: HairColorsService) {}

  @Post()
  @ApiCreatedResponse({
    type: HairColor,
  })
  create(@Body() createHairColorDto: CreateHairColorDto) {
    return this.hair_colorsService.create(createHairColorDto);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(HairColor),
  })
  async findAll(
    @Query() query: FindAllHairColorsDto,
  ): Promise<InfinityPaginationResponseDto<HairColor>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.hair_colorsService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('all')
  @ApiOkResponse({
    type: HairColor,
  })
  async findAllWithoutPagination() {
    return await this.hair_colorsService.findAll();
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.hair_colorsService.findOne(id);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: HairColor,
  })
  update(
    @Param('id') id: number,
    @Body() updateHairColorDto: UpdateHairColorDto,
  ) {
    return this.hair_colorsService.update(id, updateHairColorDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.hair_colorsService.remove(id);
  }
}
