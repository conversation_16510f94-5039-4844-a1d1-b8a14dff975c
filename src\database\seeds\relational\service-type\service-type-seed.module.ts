import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ServiceTypeSeedService } from './service-type-seed.service';
import { ServiceTypeEntity } from '../../../../service-types/infrastructure/persistence/relational/entities/service-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ServiceTypeEntity])],
  providers: [ServiceTypeSeedService],
  exports: [ServiceTypeSeedService],
})
export class ServiceTypeSeedModule {}
