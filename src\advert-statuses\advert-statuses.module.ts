import { Module } from '@nestjs/common';
import { AdvertStatusesService } from './advert-statuses.service';
import { AdvertStatusesController } from './advert-statuses.controller';
import { RelationalAdvertStatusPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [RelationalAdvertStatusPersistenceModule],
  controllers: [AdvertStatusesController],
  providers: [AdvertStatusesService],
  exports: [AdvertStatusesService, RelationalAdvertStatusPersistenceModule],
})
export class AdvertStatusesModule {}
