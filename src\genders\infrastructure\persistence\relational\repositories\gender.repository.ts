import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GenderEntity } from '../entities/gender.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { Gender } from '../../../../domain/gender';
import { GenderRepository } from '../../gender.repository';
import { GenderMapper } from '../mappers/gender.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class GenderRelationalRepository implements GenderRepository {
  constructor(
    @InjectRepository(GenderEntity)
    private readonly genderRepository: Repository<GenderEntity>,
  ) {}

  async create(data: Gender): Promise<Gender> {
    const persistenceModel = GenderMapper.toPersistence(data);
    const newEntity = await this.genderRepository.save(
      this.genderRepository.create(persistenceModel),
    );
    return GenderMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<Gender[]> {
    const entities = await this.genderRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((user) => GenderMapper.toDomain(user));
  }

  async findAll(): Promise<Gender[]> {
    const entities = await this.genderRepository.find();

    return entities.map((user) => GenderMapper.toDomain(user));
  }

  async findById(id: Gender['id']): Promise<NullableType<Gender>> {
    const entity = await this.genderRepository.findOne({
      where: { id },
    });

    return entity ? GenderMapper.toDomain(entity) : null;
  }

  async update(id: Gender['id'], payload: Partial<Gender>): Promise<Gender> {
    const entity = await this.genderRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.genderRepository.save(
      this.genderRepository.create(
        GenderMapper.toPersistence({
          ...GenderMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return GenderMapper.toDomain(updatedEntity);
  }

  async remove(id: Gender['id']): Promise<void> {
    await this.genderRepository.softDelete(id);
  }
}
