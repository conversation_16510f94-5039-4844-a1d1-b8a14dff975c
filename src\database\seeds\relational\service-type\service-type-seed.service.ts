import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceTypeEntity } from '../../../../service-types/infrastructure/persistence/relational/entities/service-type.entity';

@Injectable()
export class ServiceTypeSeedService {
  constructor(
    @InjectRepository(ServiceTypeEntity)
    private repository: Repository<ServiceTypeEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({ id: 1, name: 'SERVICE', slug: 'service' }),
        this.repository.create({ id: 2, name: 'MASSAGE', slug: 'massage' }),
        this.repository.create({ id: 3, name: 'BDSM', slug: 'bdsm' }),
        this.repository.create({ id: 99, name: 'OTH<PERSON>', slug: 'other' }),
      ]);
    }
  }
}
