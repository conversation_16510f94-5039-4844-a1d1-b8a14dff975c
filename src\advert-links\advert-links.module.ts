import { Module } from '@nestjs/common';
import { AdvertLinksService } from './advert-links.service';
import { AdvertLinksController } from './advert-links.controller';
import { RelationalAdvertLinkPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [RelationalAdvertLinkPersistenceModule],
  controllers: [AdvertLinksController],
  providers: [AdvertLinksService],
  exports: [AdvertLinksService, RelationalAdvertLinkPersistenceModule],
})
export class AdvertLinksModule {}
