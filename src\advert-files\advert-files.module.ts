import { Module } from '@nestjs/common';
import { AdvertFilesService } from './advert-files.service';
import { ImageService } from '../helpers/sharp/sharp.service';
import { MediaUploadService } from '../helpers/cloudfare/video-upload.service';
import { AdvertFilesController } from './advert-files.controller';
import { RelationalAdvertFilePersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';
import { AdvertsModule } from '../adverts/adverts.module';

@Module({
  imports: [RelationalAdvertFilePersistenceModule, AdvertsModule],
  controllers: [AdvertFilesController],
  providers: [AdvertFilesService, ImageService, MediaUploadService],
  exports: [
    AdvertFilesService,
    ImageService,
    MediaUploadService,
    RelationalAdvertFilePersistenceModule,
  ],
})
export class AdvertFilesModule {}
