import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HairColorEntity } from '../entities/hair-color.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { HairColor } from '../../../../domain/hair-color';
import { HairColorRepository } from '../../hair-color.repository';
import { HairColorMapper } from '../mappers/hair-color.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class HairColorRelationalRepository implements HairColorRepository {
  constructor(
    @InjectRepository(HairColorEntity)
    private readonly hair_colorRepository: Repository<HairColorEntity>,
  ) {}

  async create(data: HairColor): Promise<HairColor> {
    const persistenceModel = HairColorMapper.toPersistence(data);
    const newEntity = await this.hair_colorRepository.save(
      this.hair_colorRepository.create(persistenceModel),
    );
    return HairColorMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<HairColor[]> {
    const entities = await this.hair_colorRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((user) => HairColorMapper.toDomain(user));
  }

  async findAll(): Promise<HairColor[]> {
    const entities = await this.hair_colorRepository.find();

    return entities.map((user) => HairColorMapper.toDomain(user));
  }

  async findById(id: HairColor['id']): Promise<NullableType<HairColor>> {
    const entity = await this.hair_colorRepository.findOne({
      where: { id },
    });

    return entity ? HairColorMapper.toDomain(entity) : null;
  }

  async update(
    id: HairColor['id'],
    payload: Partial<HairColor>,
  ): Promise<HairColor> {
    const entity = await this.hair_colorRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.hair_colorRepository.save(
      this.hair_colorRepository.create(
        HairColorMapper.toPersistence({
          ...HairColorMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return HairColorMapper.toDomain(updatedEntity);
  }

  async remove(id: HairColor['id']): Promise<void> {
    await this.hair_colorRepository.delete(id);
  }
}
