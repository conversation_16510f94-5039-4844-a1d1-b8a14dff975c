import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertTypeEntity } from '../entities/advert-type.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { AdvertType } from '../../../../domain/advert-type';
import { AdvertTypeRepository } from '../../advert-type.repository';
import { AdvertTypeMapper } from '../mappers/advert-type.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class AdvertTypeRelationalRepository implements AdvertTypeRepository {
  constructor(
    @InjectRepository(AdvertTypeEntity)
    private readonly advert_typeRepository: Repository<AdvertTypeEntity>,
  ) {}

  async create(data: AdvertType): Promise<AdvertType> {
    const persistenceModel = AdvertTypeMapper.toPersistence(data);
    const newEntity = await this.advert_typeRepository.save(
      this.advert_typeRepository.create(persistenceModel),
    );
    return AdvertTypeMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertType[]> {
    const entities = await this.advert_typeRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((advertType) => AdvertTypeMapper.toDomain(advertType));
  }

  async findAll(): Promise<AdvertType[]> {
    const entities = await this.advert_typeRepository.find();

    return entities.map((advertType) => AdvertTypeMapper.toDomain(advertType));
  }

  async findAllAndCountQuery(queryParams: any): Promise<any> {
    const query = this.advert_typeRepository
      .createQueryBuilder('advertType') // Tabla principal: AdvertType
      .innerJoinAndSelect('advertType.adverts', 'advert') // Relación 1:N entre AdvertType y Advert
      .innerJoinAndSelect('advert.services', 'advertService') // Relación 1:N entre Advert y AdvertServices
      .select('advertType.id', 'advertTypeId') // Selecciona el ID del tipo de anuncio
      .addSelect('advertType.name', 'advertTypeName') // Selecciona el nombre del tipo de anuncio
      .addSelect('COUNT(advertService.id)', 'advertTypeCount') // Cuenta los servicios distintos de cada tipo de anuncio
      .where('advert.status = :status', { status: 1 });
    if (queryParams !== '') {
      if (queryParams?.slug) {
        query.andWhere('advertService.slug = :slug', {
          slug: queryParams?.slug,
        });
      }
    }
    query
      .groupBy('advertType.id')
      .addGroupBy('advertType.name')
      .orderBy('COUNT(advertService.id)', 'DESC');

    return await query.getRawMany();
  }

  async findById(id: AdvertType['id']): Promise<NullableType<AdvertType>> {
    const entity = await this.advert_typeRepository.findOne({
      where: { id },
    });

    return entity ? AdvertTypeMapper.toDomain(entity) : null;
  }

  async update(
    id: AdvertType['id'],
    payload: Partial<AdvertType>,
  ): Promise<AdvertType> {
    const entity = await this.advert_typeRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.advert_typeRepository.save(
      this.advert_typeRepository.create(
        AdvertTypeMapper.toPersistence({
          ...AdvertTypeMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return AdvertTypeMapper.toDomain(updatedEntity);
  }

  async remove(id: AdvertType['id']): Promise<void> {
    await this.advert_typeRepository.softDelete(id);
  }
}
