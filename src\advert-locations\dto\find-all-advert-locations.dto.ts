import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindAllAdvertLocationsDto {
  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 1))
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @Transform(({ value }) => (value ? Number(value) : 50))
  @IsNumber()
  @IsOptional()
  limit?: number;

  @IsOptional()
  name?: string;

  @IsOptional()
  url?: string;

  @IsOptional()
  googleApiResponse?: string;

  @IsOptional()
  neighborhood: number;

  @IsOptional()
  city: number;

  @IsOptional()
  region: number;

  @IsOptional()
  country: number;
}

export const StringFields = ['name', 'url'];
export const RelationalFields = ['neighborhood', 'city', 'region', 'country'];
export const NumberFields = [''];
export const DateFields = [''];
