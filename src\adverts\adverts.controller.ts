import {
  Body,
  ConflictException,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AdvertsService } from './adverts.service';
import { CreateAdvertDto } from './dto/create-advert.dto';
import { UpdateAdvertDto } from './dto/update-advert.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { Advert, AdvertSiteMap } from './domain/advert';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { plainToInstance } from 'class-transformer';
import { FindAllAdvertLocationsDto } from './dto/find-all-advert-locations.dto';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@ApiTags('Adverts')
@Controller({
  path: 'adverts',
  version: '1',
})
export class AdvertsController {
  constructor(
    private readonly advertsService: AdvertsService,
    private readonly cacheServiceHelper: CacheServiceHelper,
  ) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiCreatedResponse({
    type: Advert,
  })
  async create(@Body() createAdvertDto: any) {
    const data = plainToInstance(CreateAdvertDto, createAdvertDto);
    const createData = await this.advertsService.create(data);
    if (!createData) {
      throw new ConflictException({
        status: HttpStatus.CONFLICT,
        errors: {
          slug: 'duplicated-slug',
        },
      });
    }
    return createData;
  }

  @Get('cache/reset')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  async resetCache(@Query() query: any) {
    const _advertId = query?.advertId ? query.advertId.toString() : '';
    await this.cacheServiceHelper.resetCacheByAdvertId(`'${_advertId}'`);
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('locationhome')
  @ApiOkResponse({
    type: InfinityPaginationResponse(Advert),
  })
  async findAllByLocationHome(
    @Query() query: FindAllAdvertLocationsDto,
  ): Promise<InfinityPaginationResponseDto<Advert>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }
    const result =
      await this.advertsService.findAllWithPaginationByLocationHome(query);

    return infinityPagination(result.data, { page, limit }, result.totalCount);
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('slug/:slug')
  @ApiParam({
    name: 'slug',
    type: String,
    required: true,
  })
  async findOneBySlug(@Param('slug') slug: string) {
    return await this.advertsService.findOneBySlug(slug);
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  async findOne(@Param('id') id: string) {
    return await this.advertsService.findOne(id);
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('user/:userId')
  @ApiParam({
    name: 'userId',
    type: String,
    required: true,
  })
  async findAllByUser(@Param('userId') userId: string): Promise<Advert[]> {
    return await this.advertsService.findAllByUser(userId);
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(-1)
  @Get('sitemap/all')
  async findAllSiteMap(): Promise<AdvertSiteMap[]> {
    return await this.advertsService.findAllSiteMap();
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: Advert,
  })
  async update(@Param('id') id: string, @Body() updateAdvertDto: any) {
    const data = plainToInstance(UpdateAdvertDto, updateAdvertDto);
    return await this.advertsService.update(id, data);
  }

  @Patch('status/:id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: Advert,
  })
  async updateStatus(@Param('id') id: string, @Body() updateAdvertDto: any) {
    const data = plainToInstance(UpdateAdvertDto, updateAdvertDto);
    const updatedAdvert = await this.advertsService.updateStatus(id, data);

    await this.cacheServiceHelper.resetCacheByAdvertId(`'${id}'`);

    return updatedAdvert;
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.advertsService.remove(id);
  }

  /*
import { FindAllAdvertsDto } from './dto/find-all-adverts.dto';

@Get()
@ApiOkResponse({
  type: InfinityPaginationResponse(Advert),
})
async findAll(
  @Query() query: FindAllAdvertsDto,
): Promise<InfinityPaginationResponseDto<Advert>> {
  const page = query?.page ?? 1;
  let limit = query?.limit ?? 50;
  if (limit > 50) {
    limit = 50;
  }

  const result = await this.advertsService.findAllWithPagination(
    {
      paginationOptions: {
        page,
        limit,
      },
    },
    query,
  );

  return infinityPagination(result.data, { page, limit }, result.totalCount);
}
*/

  /*
  @Get('location')
  @ApiOkResponse({
    type: InfinityPaginationResponse(Advert),
  })
  async findAllByLocation(
    @Query() query: FindAllAdvertLocationsDto,
  ): Promise<InfinityPaginationResponseDto<Advert>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    const result = await this.advertsService.findAllWithPaginationByLocation(
      {
        paginationOptions: {
          page,
          limit,
        },
      },
      query,
    );

    return infinityPagination(result.data, { page, limit }, result.totalCount);
  }
  */
}
