import { DeepPartial } from '../../../utils/types/deep-partial.type';
import { NullableType } from '../../../utils/types/nullable.type';
import { IPaginationOptions } from '../../../utils/types/pagination-options';
import { AdvertLink } from '../../domain/advert-link';

export abstract class AdvertLinkRepository {
  abstract create(
    data: Omit<AdvertLink, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<AdvertLink>;

  abstract findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertLink[]>;

  abstract findAll(): Promise<AdvertLink[]>;

  abstract findById(id: AdvertLink['id']): Promise<NullableType<AdvertLink>>;

  abstract findMany(advertId: string): Promise<NullableType<AdvertLink[]>>;

  abstract update(
    id: AdvertLink['id'],
    payload: DeepPartial<AdvertLink>,
  ): Promise<AdvertLink | null>;

  abstract remove(id: AdvertLink['id']): Promise<void>;
}
