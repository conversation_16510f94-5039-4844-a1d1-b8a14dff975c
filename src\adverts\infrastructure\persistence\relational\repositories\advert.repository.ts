import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertEntity } from '../entities/advert.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { Advert } from '../../../../domain/advert';
import { AdvertRepository } from '../../advert.repository';
import { AdvertMapper } from '../mappers/advert.mapper';
import { FindAllAdvertLocationsDto } from '../../../../dto/find-all-advert-locations.dto';
import { GoogleApiLocationDto } from '../../../../../helpers/google-api/google-api.dto';
import { AdvertFile } from '../../../../../advert-files/domain/advert-file';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
//import { CacheServiceHelper } from '../../../../../helpers/cache/cache.service';

type ReelType = {
  hour: string;
  files: AdvertFile[];
};

type AdvertWithReels = Advert & { reels: ReelType[] };

@Injectable()
export class AdvertRelationalRepository implements AdvertRepository {
  constructor(
    @InjectRepository(AdvertEntity)
    private readonly advertRepository: Repository<AdvertEntity>,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
    //private readonly cacheServiceHelper: CacheServiceHelper,
  ) {}

  async create(
    data: Advert,
    googleData: GoogleApiLocationDto,
  ): Promise<NullableType<Advert>> {
    const advertSlug = await this.generateSlug(
      data.profileName,
      data.phoneNumber,
    );
    if (!advertSlug) {
      return null;
    } else {
      data.slug = advertSlug;
    }
    const persistenceModel = AdvertMapper.toPersistence(data);
    if (googleData) {
      persistenceModel.location = googleData.formattedAddress.toString();
    }
    const newEntity = await this.advertRepository.save(
      this.advertRepository.create(persistenceModel),
    );
    if (data.advertLinks) {
      for (const link of data.advertLinks) {
        await this.advertRepository
          .createQueryBuilder()
          .insert()
          .into('advert_link')
          .values([
            {
              name: link.name.toString(),
              advert: {
                id: newEntity.id.toString(),
              },
              rrssType: {
                id: link.rrssType.id,
              },
            },
          ])
          .execute();
      }
    }
    if (data.advertRates) {
      for (const rate of data.advertRates) {
        await this.advertRepository
          .createQueryBuilder()
          .insert()
          .into('advert_rate')
          .values([
            {
              amount: rate.amount,
              advert: {
                id: newEntity.id.toString(),
              },
              rateType: {
                id: rate.rateType.id,
              },
            },
          ])
          .execute();
      }
    }
    if (googleData) {
      await this.advertRepository
        .createQueryBuilder()
        .insert()
        .into('advert_location')
        .values([
          {
            name: googleData.formattedAddress.toString(),
            url: googleData.placeId,
            googleApiResponse: googleData.googleApiResponse,
            advert: {
              id: newEntity.id.toString(),
            },
            country: { id: googleData.countryId ? googleData.countryId : null },
            region: { id: googleData.regionId ? googleData.regionId : null },
            city: { id: googleData.cityId ? googleData.cityId : null },
            neighborhood: {
              id: googleData.neighborhoodId ? googleData.neighborhoodId : null,
            },
          },
        ])
        .execute();
    }
    /*
    const startDate = new Date();
    const endDate = new Date(startDate); // Clonamos la fecha para no modificar startDate
    endDate.setMonth(endDate.getMonth() + 3); // TODO - leer el campo durationType y añadir la fecha final en función
    const query = `INSERT INTO subscription ("startDate", "endDate", "status", "userId", "productId", "advertId") VALUES (
          '${this.formatDate(startDate)}',
          '${this.formatDate(endDate)}',
          'inactive',
          '${newEntity.user.id}',
          1, 
          '${newEntity.id}'
        )`;
    await this.datasource.query(query);
     */

    return AdvertMapper.toDomain(newEntity);
  }

  async findAllWithPaginationByLocationHome(
    queryParams: FindAllAdvertLocationsDto,
  ) {
    let cacheKey = 'CACHE-LOCATION-HOME';
    const page = queryParams?.page ?? 1;
    cacheKey += '-PAGE-' + page.toString();
    let limit = queryParams?.limit ?? 20;
    if (limit > 20) {
      limit = 20;
    }
    cacheKey += '-LIMIT-' + limit.toString();

    const { orders, ...filters } = queryParams;
    const query = this.advertRepository.createQueryBuilder('advert');
    query.leftJoinAndSelect('advert.advertType', 'advertType');
    query.leftJoinAndSelect('advert.status', 'advertStatus');
    query.leftJoinAndSelect('advert.user', 'advertUser');
    //query.leftJoinAndSelect('advert.advertFiles', 'advertFiles');
    query.leftJoinAndSelect(
      'advert.advertFiles',
      'advertFiles',
      'advertFiles.main = :main AND advertFiles.type = :type',
      { main: true, type: 1 },
    );
    query.leftJoinAndSelect('advert.advertRates', 'advertRates');
    query.leftJoinAndSelect('advertRates.rateType', 'rateType');
    query.leftJoinAndSelect('advert.advertLocations', 'advertLocations');
    query.leftJoinAndSelect('advertLocations.country', 'country');
    query.leftJoinAndSelect('advertLocations.region', 'region');
    query.leftJoinAndSelect('advertLocations.city', 'city');
    query.leftJoinAndSelect('advertLocations.neighborhood', 'neighborhood');
    query.leftJoinAndSelect('advert.services', 'service');
    query.leftJoinAndSelect(
      'advert.subscriptions',
      'subscription',
      `subscription.status = 'active'`,
    );
    query.leftJoinAndSelect('subscription.product', 'product');

    if (filters?.user) {
      query.andWhere(`advertUser.id = '${filters?.user}'`);
      cacheKey += '-USER-' + filters.user;
    }
    if (filters?.advertType) {
      query.andWhere(`advertType.id = ${filters?.advertType}`);
      cacheKey += '-ADVERTTYPE-' + filters.advertType;
    }
    if (filters?.status) {
      query.andWhere(`advertStatus.id = ${filters?.status}`);
      cacheKey += '-ADVERTSTATUS-' + filters.status;
    }
    if (filters?.featured) {
      query.andWhere(`advert.featured = ${filters?.featured}`);
      cacheKey += '-FEATURED-' + filters.featured;
    }
    if (filters?.profileName) {
      query.andWhere(
        `LOWER(advert.profileName) LIKE LOWER('%${filters.profileName}%')`,
      );
      cacheKey += '-PROFILENAME-' + filters.profileName;
    }
    if (filters?.advertSlug) {
      query.andWhere(
        `LOWER(advert.slug) LIKE LOWER('%${filters.advertSlug}%')`,
      );
      cacheKey += '-ADVERTSLUG-' + filters.advertSlug;
    }
    if (filters?.country) {
      if (filters?.country != 99) {
        query.andWhere(`advertLocations.country.id = ${filters?.country}`);
        cacheKey += '-COUNTRY-' + filters.country;
      }
    }
    if (filters?.countrySlug) {
      query.andWhere(
        `LOWER(country.slug) LIKE LOWER('%${filters.countrySlug}%')`,
      );
      cacheKey += '-COUNTRYSLUG-' + filters.countrySlug;
    }
    if (filters?.region) {
      query.andWhere(`advertLocations.region.id = ${filters?.region}`);
      cacheKey += '-REGION-' + filters.region;
    }
    if (filters?.regionSlug) {
      query.andWhere(
        `LOWER(region.slug) LIKE LOWER('%${filters.regionSlug}%')`,
      );
      cacheKey += '-REGIONSLUG-' + filters.regionSlug;
    }
    if (filters?.city) {
      query.andWhere(`advertLocations.city.id = ${filters?.city}`);
      cacheKey += '-CITY-' + filters.city;
    }
    if (filters?.citySlug) {
      query.andWhere(`LOWER(city.slug) LIKE LOWER('%${filters.citySlug}%')`);
      cacheKey += '-CITYSLUG-' + filters.citySlug;
    }
    if (filters?.neighborhood) {
      query.andWhere(
        `advertLocations.neighborhood.id = ${filters?.neighborhood}`,
      );
      cacheKey += '-NEIGHBORHOOD-' + filters.neighborhood;
    }
    if (filters?.neighborhoodSlug) {
      query.andWhere(
        `LOWER(neighborhood.slug) LIKE LOWER('%${filters.neighborhoodSlug}%')`,
      );
      cacheKey += '-NEIGHBORHOODSLUG-' + filters.neighborhoodSlug;
    }
    if (filters?.serviceSlug) {
      query.andWhere(
        `LOWER(service.slug) LIKE LOWER('%${filters.serviceSlug}%')`,
      );
      cacheKey += '-SERVICESLUG-' + filters.serviceSlug;
    }

    //const totalCount = await query.getCount();
    let totalCount: number;

    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertRepository.findAllWithPaginationByLocationHome.NOORDER',
      cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const totalCountCachedData = await this.cacheService.get<{ name: string }>(
      cacheKey,
    );
    if (totalCountCachedData) {
      totalCount = 20;
    } else {
      totalCount = await query.getCount();
      await this.cacheService.set(cacheKey, totalCount);
    }

    // Configuramos la paginación
    query.skip((page - 1) * limit).take(limit);

    let parsedOrders: { field: string; direction: 'ASC' | 'DESC' }[] = [];
    if (typeof orders === 'string') {
      try {
        const tempOrders = JSON.parse(orders); // Intentamos parsear el string a JSON
        if (Array.isArray(tempOrders)) {
          parsedOrders = tempOrders
            .map((option) => {
              if (
                typeof option === 'object' &&
                option !== null &&
                'field' in option &&
                'direction' in option &&
                typeof option.field === 'string' &&
                (option.direction === 'ASC' || option.direction === 'DESC')
              ) {
                return { field: option.field, direction: option.direction };
              }
              return null; // Ignorar objetos incorrectos
            })
            .filter(
              (order): order is { field: string; direction: 'ASC' | 'DESC' } =>
                Boolean(order),
            );
        }
      } catch (error) {
        //
      }
    } else if (Array.isArray(orders)) {
      parsedOrders = orders.filter(
        (option): option is { field: string; direction: 'ASC' | 'DESC' } =>
          typeof option === 'object' &&
          'field' in option &&
          'direction' in option &&
          (option.direction === 'ASC' || option.direction === 'DESC'),
      );
    }

    if (parsedOrders.length > 0) {
      parsedOrders.forEach(({ field, direction }) => {
        query.addOrderBy(`advert.${field}`, direction);
        cacheKey += `-ORDERBY-${field}-${direction}`;
      });
    } else {
      // Si no se pasa un parámetro de ordenación, puedes establecer un orden predeterminado
      query.addOrderBy('advert.oTopAdvert', 'DESC');
      query.addOrderBy('advert.oTopDoubleAdvert', 'DESC');
      query.addOrderBy('advert.oDoubleAdvert', 'DESC');
      query.addOrderBy('advert.oAvailableNow', 'DESC');
      query.addOrderBy('advert.oReactivate', 'DESC');
      query.addOrderBy('advert.updatedAt', 'DESC');
      // TODO - atención
      // es lo mismo que updatedAt, porque lo que hacemos es actualizar la fecha de updateAt
      // cuidado con el TRUCO de actualizar el anuncio que tb sube, lo que el anuncio en si
      // si se actualiza queda en un estado pendiente de revisión, pero si lo activamos es como
      // si se reactivara.

      cacheKey +=
        '-ORDERBY-oTopAdvert-DESC-oTopDoubleAdvert-DESC-oDoubleAdvert-DESC-oAvailableNow-DESC-oReactivate-DESC-updatedAt-DESC';
    }
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertRepository.findAllWithPaginationByLocationHome',
      cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    const adverts = await query.getMany();
    if (adverts) {
      const data = adverts.map((advert) => AdvertMapper.toDomain(advert));
      const _return = {
        data: data,
        page: page,
        limit: limit,
        totalCount: totalCount,
      };
      await this.cacheService.set(cacheKey, _return);
      return _return;
    }
  }

  async findAllByUser(userId: string) {
    const query = this.advertRepository.createQueryBuilder('advert');
    query.leftJoinAndSelect('advert.status', 'advertStatus');
    query.leftJoinAndSelect(
      'advert.advertFiles',
      'advertFiles',
      'advertFiles.main = :main AND advertFiles.type = :type',
      { main: true, type: 1 },
    );
    query.leftJoinAndSelect('advert.advertLocations', 'advertLocations');
    query.leftJoinAndSelect('advertLocations.city', 'city');
    query.where({ status: { id: 1 } });
    query.andWhere({ user: { id: userId } });
    query.addOrderBy('advert.createdAt', 'DESC');
    const adverts = await query.getMany();

    return adverts.map((advert) => AdvertMapper.toDomain(advert));
  }

  async findAllSiteMap() {
    const entities = await this.advertRepository.find({
      select: ['profileName', 'slug'],
      order: { rating: 'desc' },
      where: { status: { id: 1 } },
      relations: ['advertLocations', 'advertLocations.city'],
    });

    return entities.map((entity) => ({
      slugName: entity.slug,
      slugCity: entity.advertLocations[0]?.city?.slug ?? '', // Solo el name de la primera ciudad de advertLocations
    }));
  }

  async findBySlug(
    slug: Advert['slug'],
  ): Promise<NullableType<AdvertWithReels>> {
    const entity = await this.advertRepository.findOne({
      relations: [
        'services',
        'services.serviceType',
        'languages',
        'advertLinks',
        'advertLinks.rrssType',
        'advertRates',
        'advertRates.rateType',
        'advertFiles',
        'advertLocations',
        'advertLocations.country',
        'advertLocations.region',
        'advertLocations.city',
        'advertLocations.neighborhood',
      ],
      where: { slug, status: { id: 1 } },
    });

    //const reels = await this.findManyReels(id);

    if (entity) {
      const reels = await this.findManyReels(entity?.id);
      return {
        ...AdvertMapper.toDomain(entity), // Esto transforma el entity a tu dominio
        reels: reels || [], // Aquí agregas los reels
      };
    } else {
      return null;
    }
  }

  async findById(id: Advert['id']): Promise<NullableType<AdvertWithReels>> {
    const entity = await this.advertRepository.findOne({
      relations: [
        'services',
        'services.serviceType',
        'languages',
        'advertLinks',
        'advertLinks.rrssType',
        'advertRates',
        'advertRates.rateType',
        'advertFiles',
        'advertLocations',
        'advertLocations.country',
        'advertLocations.region',
        'advertLocations.city',
        'advertLocations.neighborhood',
      ],
      where: { id },
    });

    //const reels = await this.findManyReels(id);

    const reels = await this.findManyReels(id);

    if (!entity) return null;

    return {
      ...AdvertMapper.toDomain(entity), // Esto transforma el entity a tu dominio
      reels: reels || [], // Aquí agregas los reels
    };
  }

  async update(
    id: Advert['id'],
    data: Advert,
    googleData: GoogleApiLocationDto,
  ): Promise<Advert> {
    const entity = await this.advertRepository.findOne({
      where: { id },
      relations: [
        'services',
        'languages',
        'advertLinks',
        'advertRates',
        'advertFiles',
        'advertLocations',
        'advertLocations.country',
        'advertLocations.region',
        'advertLocations.city',
        'advertLocations.neighborhood',
      ],
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    if (entity.advertLinks) {
      await this.advertRepository
        .createQueryBuilder()
        .delete()
        .from('advert_link')
        .where('advertId = :id', { id })
        .execute();
    }
    if (entity.advertRates) {
      await this.advertRepository
        .createQueryBuilder()
        .delete()
        .from('advert_rate')
        .where('advertId = :id', { id })
        .execute();
    }
    if (googleData) {
      data.location = googleData.formattedAddress.toString();
      entity.location = googleData.formattedAddress.toString();
      await this.advertRepository
        .createQueryBuilder()
        .update('advert_location')
        .set({ deletedAt: new Date() })
        .where('advertId = :id', { id })
        .execute();
    }
    const updatedEntity = await this.advertRepository.save(
      this.advertRepository.create(
        AdvertMapper.toPersistence({
          ...AdvertMapper.toDomain(entity),
          ...data,
          advertLinks: [],
          advertRates: [],
          advertLocations: [],
        }),
      ),
    );

    if (data.advertLinks) {
      for (const link of data.advertLinks) {
        await this.advertRepository
          .createQueryBuilder()
          .insert()
          .into('advert_link')
          .values([
            {
              name: link.name.toString(),
              advert: {
                id: id.toString(),
              },
              rrssType: {
                id: link.rrssType.id,
              },
            },
          ])
          .execute();
      }
      updatedEntity.advertLinks = data.advertLinks;
    }
    if (data.advertRates) {
      for (const rate of data.advertRates) {
        await this.advertRepository
          .createQueryBuilder()
          .insert()
          .into('advert_rate')
          .values([
            {
              amount: rate.amount,
              advert: {
                id: id.toString(),
              },
              rateType: {
                id: rate.rateType.id,
              },
            },
          ])
          .execute();
      }
      updatedEntity.advertRates = data.advertRates;
    }
    if (googleData) {
      await this.advertRepository
        .createQueryBuilder()
        .insert()
        .into('advert_location')
        .values([
          {
            name: googleData.formattedAddress.toString(),
            url: googleData.placeId,
            googleApiResponse: googleData.googleApiResponse,
            advert: {
              id: id.toString(),
            },
            country: { id: googleData.countryId ? googleData.countryId : null },
            region: { id: googleData.regionId ? googleData.regionId : null },
            city: { id: googleData.cityId ? googleData.cityId : null },
            neighborhood: {
              id: googleData.neighborhoodId ? googleData.neighborhoodId : null,
            },
          },
        ])
        .execute();
    }

    return AdvertMapper.toDomain(updatedEntity);
  }

  async updateStatus(id: Advert['id'], data: Advert): Promise<Advert> {
    const entity = await this.advertRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.advertRepository.save(
      this.advertRepository.create(
        AdvertMapper.toPersistence({
          ...AdvertMapper.toDomain(entity),
          ...data,
        }),
      ),
    );
    await this.cacheService.del(updatedEntity.slug.toString());
    return AdvertMapper.toDomain(updatedEntity);
  }

  async remove(id: Advert['id']): Promise<void> {
    await this.advertRepository.softDelete(id);
  }

  async findManyReels(advertId: string): Promise<ReelType[]> {
    const data = await this.advertRepository
      .createQueryBuilder('advert')
      .leftJoinAndSelect('advert.advertFiles', 'advert_file')
      .where('advert.id = :advertId', { advertId })
      .andWhere('advert.status = 1')
      .andWhere('advert_file.type = :type', { type: 3 })
      .orderBy("DATE_TRUNC('hour', advert_file.createdAt)", 'DESC')
      .addOrderBy('advert_file.order', 'ASC')
      .getMany();

    const groupedByAdvert = data.reduce(
      (acc, advert) => {
        const advertId = advert?.id;
        const advertName = advert?.profileName;
        if (!advertId) return acc;

        if (!acc[advertId]) {
          acc[advertId] = { profileName: advertName, hours: [] }; // Almacenamos profileName y un array para las horas
        }

        advert.advertFiles.forEach((file) => {
          const creationHour = new Date(file.createdAt);
          creationHour.setMinutes(0, 0, 0);
          const hourKey = creationHour.toISOString();

          const hourIndex = acc[advertId].hours.findIndex(
            (entry) => entry.hour === hourKey,
          );

          if (hourIndex === -1) {
            acc[advertId].hours.push({ hour: hourKey, files: [file] });
          } else {
            acc[advertId].hours[hourIndex].files.push(file);
          }
        });

        return acc;
      },
      {} as Record<string, { profileName: string; hours: ReelType[] }>, // Usamos el tipo adecuado
    );

    return Object.values(groupedByAdvert).flatMap(({ hours }) => hours);
  }

  async generateSlug(
    profileName: string,
    phone: string,
  ): Promise<string | null> {
    const rData = profileName
      .normalize('NFD') // Normaliza el texto para quitar acentos
      .replace(/[\u0300-\u036f]/g, '') // Elimina los caracteres de acento
      .toLowerCase() // Convierte todo a minúsculas
      .replace(/[^a-z0-9.,]/g, '') // Elimina cualquier carácter que no sea letra, número, punto o coma
      .replace(/[.,]/g, '') // Elimina puntos (.) y comas (,)
      .slice(0, 10); // Toma solo los primeros 10 caracteres

    const advertSlug = rData.slice(0, 10) + '-' + phone.slice(2); // + '-' + citySlug;
    const isDuplicated = await this.advertRepository.findOne({
      where: { slug: advertSlug },
    });
    if (isDuplicated) {
      return null;
    } else {
      return advertSlug;
    }
  }

  /*

  async findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllAdvertsDto,
  ) {
    const query = this.advertRepository.createQueryBuilder('advert');
    query.leftJoinAndSelect('advert.advertType', 'advertType');
    query.leftJoinAndSelect('advert.gender', 'gender');
    query.leftJoinAndSelect('advert.chestSize', 'chestSize');
    query.leftJoinAndSelect('advert.nationality', 'nationality');
    query.leftJoinAndSelect('advert.status', 'advertStatus');
    query.leftJoinAndSelect('advert.orientation', 'orientation');
    query.leftJoinAndSelect('advert.eyeColor', 'eyeColor');
    query.leftJoinAndSelect('advert.hairColor', 'hairColor');
    query.leftJoinAndSelect('advert.race', 'race');
    query.leftJoinAndSelect('advert.services', 'services');
    query.leftJoinAndSelect('services.serviceType', 'serviceType');
    query.leftJoinAndSelect('advert.languages', 'languages');
    query.leftJoinAndSelect('advert.user', 'user');
    query.leftJoinAndSelect('advert.advertLinks', 'advertLinks');
    query.leftJoinAndSelect('advertLinks.rrssType', 'rrssType');
    query.leftJoinAndSelect('advert.advertRates', 'advertRates');
    query.leftJoinAndSelect('advertRates.rateType', 'rateType');
    query.leftJoinAndSelect('advert.advertFiles', 'advertFiles');
    query.leftJoinAndSelect('advert.advertLocations', 'advertLocations');
    query.leftJoinAndSelect('advertLocations.country', 'country');
    query.leftJoinAndSelect('advertLocations.region', 'region');
    query.leftJoinAndSelect('advertLocations.city', 'city');
    query.leftJoinAndSelect('advertLocations.neighborhood', 'neighborhood');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page, limit, ...updatedDto } = filters;
    if (updatedDto) {
      Object.entries(updatedDto).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (RelationalFields.includes(key)) {
            query.andWhere(`${key}.id = :${key}`, { [key]: value });
          } else if (StringFields.includes(key)) {
            query.andWhere(`LOWER(advert.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          } else if (NumberFields.includes(key)) {
            query.andWhere(`advert.${key} = :${key}`, {
              [key]: Number(value),
            });
          } else if (BooleanFields.includes(key)) {
            query.andWhere(`advert.${key} = :${key}`, {
              [key]: value,
            });
          } else if (DateFields.includes(key)) {
              //query.andWhere(`advert.${key} = :${key}`, {
              //  [key]: new Date(value),
              //});
          }
        }
      });
    }

    const [, totalCount] = await query.getManyAndCount();

    // Configuramos la paginación
    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);

    const adverts = await query.getMany();

    return {
      data: adverts.map((advert) => AdvertMapper.toDomain(advert)),
      totalCount: totalCount,
    };
  }

    async findAllWithPaginationByLocation(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllAdvertLocationsDto,
  ) {
    const query = this.advertRepository.createQueryBuilder('advert');
    query.leftJoinAndSelect('advert.advertType', 'advertType');
    query.leftJoinAndSelect('advert.gender', 'gender');
    query.leftJoinAndSelect('advert.chestSize', 'chestSize');
    query.leftJoinAndSelect('advert.nationality', 'nationality');
    query.leftJoinAndSelect('advert.status', 'advertStatus');
    query.leftJoinAndSelect('advert.orientation', 'orientation');
    query.leftJoinAndSelect('advert.eyeColor', 'eyeColor');
    query.leftJoinAndSelect('advert.hairColor', 'hairColor');
    query.leftJoinAndSelect('advert.race', 'race');
    query.leftJoinAndSelect('advert.services', 'services');
    query.leftJoinAndSelect('services.serviceType', 'serviceType');
    query.leftJoinAndSelect('advert.languages', 'languages');
    query.leftJoinAndSelect('advert.user', 'user');
    query.leftJoinAndSelect('advert.advertLinks', 'advertLinks');
    query.leftJoinAndSelect('advertLinks.rrssType', 'rrssType');
    query.leftJoinAndSelect('advert.advertRates', 'advertRates');
    query.leftJoinAndSelect('advertRates.rateType', 'rateType');
    query.leftJoinAndSelect('advert.advertFiles', 'advertFiles');
    query.leftJoinAndSelect('advert.advertLocations', 'advertLocations');
    query.leftJoinAndSelect('advertLocations.country', 'country');
    query.leftJoinAndSelect('advertLocations.region', 'region');
    query.leftJoinAndSelect('advertLocations.city', 'city');
    query.leftJoinAndSelect('advertLocations.neighborhood', 'neighborhood');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page, limit, ...updatedDto } = filters;
    if (filters?.advertType) {
      query.andWhere(`advertType.id = ${filters?.advertType}`);
    }
    if (filters?.status) {
      query.andWhere(`advertStatus.id = ${filters?.status}`);
    }
    if (filters?.featured) {
      query.andWhere(`advert.featured = ${filters?.featured}`);
    }
    if (filters?.country) {
      if (filters?.country != 99) {
        query.andWhere(`advertLocations.country.id = ${filters?.country}`);
      }
    }
    if (filters?.region) {
      query.andWhere(`advertLocations.region.id = ${filters?.region}`);
    }
    if (filters?.city) {
      query.andWhere(`advertLocations.city.id = ${filters?.city}`);
    }
    if (filters?.neighborhood) {
      query.andWhere(
        `advertLocations.neighborhood.id = ${filters?.neighborhood}`,
      );
    }

    const totalCount = await query.getCount();

    // Configuramos la paginación
    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);

    const adverts = await query.getMany();

    return {
      data: adverts.map((advert) => AdvertMapper.toDomain(advert)),
      totalCount: totalCount,
    };
  }

  import { IPaginationOptions } from '../../../../../utils/types/pagination-options';
import {
  BooleanFields,
  DateFields,
  FindAllAdvertsDto,
  NumberFields,
  RelationalFields,
  StringFields,
} from '../../../../dto/find-all-adverts.dto';
   */
}
