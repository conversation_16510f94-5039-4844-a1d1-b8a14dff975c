import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertRateEntity } from '../entities/advert-rate.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { AdvertRate } from '../../../../domain/advert-rate';
import { AdvertRateRepository } from '../../advert-rate.repository';
import { AdvertRateMapper } from '../mappers/advert-rate.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class AdvertRateRelationalRepository implements AdvertRateRepository {
  constructor(
    @InjectRepository(AdvertRateEntity)
    private readonly advert_rateRepository: Repository<AdvertRateEntity>,
  ) {}

  async create(data: AdvertRate): Promise<AdvertRate> {
    const persistenceModel = AdvertRateMapper.toPersistence(data);
    const newEntity = await this.advert_rateRepository.save(
      this.advert_rateRepository.create(persistenceModel),
    );
    return AdvertRateMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<AdvertRate[]> {
    const entities = await this.advert_rateRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((element) => AdvertRateMapper.toDomain(element));
  }

  async findAll(): Promise<AdvertRate[]> {
    const entities = await this.advert_rateRepository.find();

    return entities.map((element) => AdvertRateMapper.toDomain(element));
  }

  async findById(id: AdvertRate['id']): Promise<NullableType<AdvertRate>> {
    const entity = await this.advert_rateRepository.findOne({
      where: { id },
    });

    return entity ? AdvertRateMapper.toDomain(entity) : null;
  }

  async findMany(advertId: string): Promise<NullableType<AdvertRate[]>> {
    return await this.advert_rateRepository.find({
      where: { advert: { id: advertId } },
    });
  }

  async update(
    id: AdvertRate['id'],
    payload: Partial<AdvertRate>,
  ): Promise<AdvertRate> {
    const entity = await this.advert_rateRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.advert_rateRepository.save(
      this.advert_rateRepository.create(
        AdvertRateMapper.toPersistence({
          ...AdvertRateMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return AdvertRateMapper.toDomain(updatedEntity);
  }

  async remove(id: AdvertRate['id']): Promise<void> {
    await this.advert_rateRepository.softDelete(id);
  }
}
