import { BaseEntityMapper } from '../../../../../utils/mappers/base-entity-mapper';
import { EyeColor } from '../../../../domain/eye-color';
import { EyeColorEntity } from '../entities/eye-color.entity';

export class EyeColorMapper {
  private static baseMapper = new (class extends BaseEntityMapper<
    EyeColor,
    EyeColorEntity
  > {})();

  static toPersistence(domainEntity: EyeColor): EyeColorEntity {
    const persistenceEntity = new EyeColorEntity();
    return this.baseMapper.mapToPersistenceCommon(
      domainEntity,
      persistenceEntity,
    );
  }

  static toDomain(raw: EyeColorEntity): EyeColor {
    const domainEntity = new EyeColor();
    return this.baseMapper.mapToDomainCommon(raw, domainEntity);
  }
}
