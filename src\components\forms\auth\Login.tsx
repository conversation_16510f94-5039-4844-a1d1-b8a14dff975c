'use client';

import { SignIn, SignInResponse } from '@/models/auth';
import { regexEmail } from '@/utils/regex';
import Cookies from 'js-cookie';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import React, { useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { loginAuth } from '@/services/AuthService';

import ButtonPrimary from '@/components/commons/ButtonPrimary';
import Checkbox from '@/components/commons/Checkbox';
import FieldError from '@/components/commons/FieldError';
import FieldInput from '@/components/commons/FieldInput';
import useSubmit from '@/hooks/useSubmit';
import { useAppDispatch } from '@/hooks/useDispatch';
import { setUser } from '@/reducers/userSlice';

const LoginForm = () => {
  const [rememberMeState, setRememberMe] = useState(false);
  const tAuth = useTranslations('auth');
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useAppDispatch();
  const router = useRouter();

  const {
    register,
    formState: { errors },
    setError,
    clearErrors,
    handleSubmit,
    setValue,
  } = useForm<SignIn>();

  const roleRoutes = {
    1: '/cpanel',
    3: '/dashboard',
    default: '/',
  };

  useEffect(() => {
    const email = Cookies.get('email') || '';
    const rememberMe = Cookies.get('rememberMe') === 'true';
    setValue('email', email);
    setRememberMe(rememberMe);
  }, [setValue]);

  const { isLoading, doSubmit } = useSubmit<SignIn, SignInResponse>();

  const onSubmit: SubmitHandler<SignIn> = async data => {
    clearErrors('root');
    try {
      if (rememberMeState) {
        Cookies.set('email', data.email, { expires: 130 });
        Cookies.set('rememberMe', 'true', { expires: 130 });
      } else {
        Cookies.remove('email');
        Cookies.set('rememberMe', 'false');
      }

      const dataIn = await doSubmit({ data, callback: loginAuth });
      if ((dataIn?.user?.status?.id || 0) === 1) {
        dispatch(setUser(dataIn.user));

        const roleIndex = dataIn.user?.role?.id?.toString() ?? 'default';
        const route =
          roleRoutes[roleIndex as keyof typeof roleRoutes] ||
          roleRoutes.default;
        router.push(route);

        enqueueSnackbar(tAuth('login-success'), {
          variant: 'success',
        });
      } else {
        enqueueSnackbar(tAuth('errors.inactiveUser'), {
          variant: 'warning',
        });
      }
    } catch (error) {
      if (typeof error === 'string') {
        setError('root', { message: error });
      }
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="pt-5 mt-5 form-inicial"
      suppressHydrationWarning
    >
      <FieldInput
        label={tAuth('input.email')}
        type="email"
        id="email"
        name="email"
        error={errors.email?.message}
        register={register}
        rules={{
          required: {
            value: true,
            message: tAuth('validates.email'),
          },
          pattern: {
            value: regexEmail,
            message: tAuth('validates.invalidEmail'),
          },
        }}
        isRequired={true}
        placeholder="<EMAIL>"
      />
      <div className="p-3"></div>
      <FieldInput
        label={tAuth('input.password')}
        type="password"
        id="password"
        name="password"
        error={errors.password?.message}
        register={register}
        rules={{
          required: {
            value: true,
            message: tAuth('validates.password.required'),
          },
        }}
        isRequired={true}
        placeholder="••••••••••"
      />
      <div className="flex mt-3" suppressHydrationWarning>
        <div className="flex items-center">
          <Checkbox
            id="rememberMe"
            label={tAuth('rememberMe')}
            checked={rememberMeState}
            onChange={checked => setRememberMe(checked)}
          />
        </div>
        <Link
          href={'/forgot-password'}
          className="ml-auto transition-opacity hover:opacity-80"
        >
          <small>{tAuth('forgot.title')}</small>
        </Link>
      </div>
      <ButtonPrimary type="submit" loading={isLoading}>
        {tAuth('login')}
      </ButtonPrimary>
      <div className="w-full text-center">
        <FieldError
          error={errors.root ? tAuth(`errors.${errors.root.message}`) : ''}
        />
      </div>
    </form>
  );
};

export default LoginForm;
