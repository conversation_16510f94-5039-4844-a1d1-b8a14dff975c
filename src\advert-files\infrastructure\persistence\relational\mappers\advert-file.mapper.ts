import { AdvertFile } from '../../../../domain/advert-file';
import { AdvertFileEntity } from '../entities/advert-file.entity';

export class AdvertFileMapper {
  static toDomain(raw: AdvertFileEntity): AdvertFile {
    const domainEntity = new AdvertFile();
    domainEntity.id = raw.id;
    domainEntity.type = raw.type;
    domainEntity.mimeType = raw.mimeType;
    domainEntity.advert = raw.advert;
    domainEntity.file = raw.file;
    domainEntity.order = raw.order;
    domainEntity.main = raw.main;
    domainEntity.validated = raw.validated;
    domainEntity.validatedAt = raw.validatedAt;
    domainEntity.subscription = raw.subscription;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: AdvertFile): AdvertFileEntity {
    const persistenceEntity = new AdvertFileEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.type = domainEntity.type;
    persistenceEntity.mimeType = domainEntity.mimeType;
    persistenceEntity.advert = domainEntity.advert;
    persistenceEntity.file = domainEntity.file;
    persistenceEntity.order = domainEntity.order;
    persistenceEntity.main = domainEntity.main;
    persistenceEntity.validatedAt = domainEntity.validatedAt;
    persistenceEntity.validated = domainEntity.validated;
    persistenceEntity.subscription = domainEntity.subscription;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;

    return persistenceEntity;
  }
}
