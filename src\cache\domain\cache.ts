import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { User } from '../../users/domain/user';

export class CacheData {
  @ApiProperty({ type: String })
  id: string;

  @ApiResponseProperty({ type: Date })
  cacheDate: Date;

  @ApiResponseProperty({ type: String })
  description: string;

  @ApiResponseProperty({ type: String })
  cacheKey: string;

  @ApiResponseProperty({ type: () => User })
  user: User;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
