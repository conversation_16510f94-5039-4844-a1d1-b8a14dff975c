import { Inject, Injectable } from '@nestjs/common';
import { CreateAdvertFileDto } from './dto/create-advert-file.dto';
import { UpdateAdvertFileDto } from './dto/update-advert-file.dto';
import { AdvertFileRepository } from './infrastructure/persistence/advert-file.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { AdvertFile } from './domain/advert-file';
import { AdvertRepository } from '../adverts/infrastructure/persistence/advert.repository';
import { ImageService } from '../helpers/sharp/sharp.service';
import { MediaUploadService } from '../helpers/cloudfare/video-upload.service';
import { fileType } from './dto/advert-file-type.dto';
import { FindAllAdvertFilesVideoDto } from './dto/find-all-advert-files.dto';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Injectable()
export class AdvertFilesService {
  constructor(
    private readonly advert_fileRepository: AdvertFileRepository,
    private readonly advertRepository: AdvertRepository,
    private readonly cacheServiceHelper: CacheServiceHelper,
    private readonly imageService: ImageService,
    private readonly mediaUploadService: MediaUploadService,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createAdvertFileDto: CreateAdvertFileDto) {
    return this.advert_fileRepository.create(createAdvertFileDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.advert_fileRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  findAllVideosWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllAdvertFilesVideoDto,
  ) {
    return this.advert_fileRepository.findAllVideosWithPagination(
      {
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      },
      filters,
    );
  }

  findOne(id: AdvertFile['id']) {
    return this.advert_fileRepository.findById(id);
  }

  async findMany(advertId: string, updateAdvertFileDto: any) {
    const _cacheKey = `CACHE-ADVERT-FILES-ALL-BY-ADVERT-ID${advertId}`;
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertFilesService.findMany',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
     */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.advert_fileRepository.findMany(
      advertId,
      updateAdvertFileDto,
    );
    if (data) {
      await this.cacheService.set(_cacheKey, data);
      return data;
    }
  }

  findManyMain(advertId: string) {
    return this.advert_fileRepository.findManyMain(advertId);
  }

  async findReels() {
    const _cacheKey = 'CACHE-ADVERT-FILES-REELS-ALL';
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertFilesService.findReels',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.advert_fileRepository.findManyReels();
    if (data) {
      await this.cacheService.set(_cacheKey, data);
      return data;
    }
  }

  async findVideos(query: FindAllAdvertFilesVideoDto) {
    let _cacheKey = 'CACHE-ADVERT-FILES-VIDEOS-ALL';
    if (query?.page) {
      _cacheKey += `-PAGE-${query?.page}`;
    }
    if (query?.limit) {
      _cacheKey += `-LIMIT-${query?.limit}`;
    }
    if (query?.citySlug) {
      _cacheKey += `-CITYSLUG-${query?.citySlug}`;
    }
    /*
    this.cacheServiceHelper.addUsedCacheKey(
      'advertFilesService.findVideos',
      _cacheKey,
      '162fff65-e440-402d-91eb-649400b79052',
    );
    */
    const cachedData = await this.cacheService.get<{ name: string }>(_cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.advert_fileRepository.findManyVideos(query);
    if (data) {
      await this.cacheService.set(_cacheKey, data);
      return data;
    }
  }

  update(id: AdvertFile['id'], updateAdvertFileDto: UpdateAdvertFileDto) {
    return this.advert_fileRepository.update(id, updateAdvertFileDto);
  }

  remove(id: AdvertFile['id']) {
    return this.advert_fileRepository.remove(id);
  }

  async upload(
    advertId: string,
    dataFile: Express.Multer.File,
    uploadFileData: any,
  ): Promise<AdvertFile | null> {
    const advert = await this.advertRepository.findById(advertId);
    if (!advert) {
      return null;
    } else {
      let uploadResult;
      let uploadFile = '';
      if (
        uploadFileData.type == fileType.IMAGE ||
        uploadFileData.type == fileType.REEL_IMAGE
      ) {
        // Paso 1: Obtener direct upload URL de Cloudflare
        const uploadURL = await this.mediaUploadService.getDirectUploadUrl();

        /*
        const imageWaterMark = await this.imageService.addWatermark(
          dataFile.buffer,
        );
        */

        if (uploadURL) {
          // Paso 2: Subir la imagen usando el direct upload URL
          const uploadResult =
            await this.mediaUploadService.uploadImageToDirectUrl(
              uploadURL,
              dataFile.buffer,
              dataFile.originalname,
            );
          if (uploadResult) {
            uploadFile = uploadResult?.result?.variants[0];
          } else {
            uploadFile = '';
          }
        } else {
          uploadFile = '';
        }
      } else if (
        uploadFileData.type == fileType.VIDEO ||
        uploadFileData.type == fileType.REEL_VIDEO
      ) {
        uploadResult = await this.mediaUploadService.uploadVideo(dataFile);
        if (uploadResult) {
          uploadFile = uploadResult;
        } else {
          uploadFile = '';
        }
      } else {
        uploadResult = null;
      }
      if (uploadFile) {
        await this.cacheService.del('CACHE-ADVERT-FILES-REELS-ALL');
        const createAdvertFileDto = new CreateAdvertFileDto();
        if (advert) {
          createAdvertFileDto.type = uploadFileData.type;
          if (
            uploadFileData.type == fileType.REEL_IMAGE ||
            uploadFileData.type == fileType.REEL_VIDEO
          ) {
            createAdvertFileDto.validated = true;
            createAdvertFileDto.validatedAt = new Date();
          }
          createAdvertFileDto.mimeType = dataFile.mimetype;
          createAdvertFileDto.advert = advert;
          createAdvertFileDto.file = uploadFile;
          createAdvertFileDto.order = uploadFileData.order;
          createAdvertFileDto.main = uploadFileData.main == 1;
          return await this.create(createAdvertFileDto);
        } else {
          return null;
        }
      } else {
        return null;
      }
    }
  }
}
