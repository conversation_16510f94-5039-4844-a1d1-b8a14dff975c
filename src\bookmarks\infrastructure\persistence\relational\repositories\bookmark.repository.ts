import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BookmarkEntity } from '../entities/bookmark.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { Bookmark } from '../../../../domain/bookmark';
import { BookmarkRepository } from '../../bookmark.repository';
import { BookmarkMapper } from '../mappers/bookmark.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class BookmarkRelationalRepository implements BookmarkRepository {
  constructor(
    @InjectRepository(BookmarkEntity)
    private readonly bookmarkRepository: Repository<BookmarkEntity>,
  ) {}

  async create(data: Bookmark): Promise<Bookmark> {
    const persistenceModel = BookmarkMapper.toPersistence(data);
    const newEntity = await this.bookmarkRepository.save(
      this.bookmarkRepository.create(persistenceModel),
    );
    return BookmarkMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<Bookmark[]> {
    const entities = await this.bookmarkRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((element) => BookmarkMapper.toDomain(element));
  }

  async findAll(): Promise<Bookmark[]> {
    const entities = await this.bookmarkRepository.find();

    return entities.map((element) => BookmarkMapper.toDomain(element));
  }

  async findById(id: Bookmark['id']): Promise<NullableType<Bookmark>> {
    const entity = await this.bookmarkRepository.findOne({
      where: { id },
    });

    return entity ? BookmarkMapper.toDomain(entity) : null;
  }

  async findMany(userId: string): Promise<NullableType<Bookmark[]>> {
    return await this.bookmarkRepository.find({
      where: { user: { id: userId } },
    });
    /*
    return await this.bookmarkRepository
      .createQueryBuilder('bookmark')
      .innerJoinAndSelect('bookmark.user', 'user')
      .innerJoinAndSelect(
        'bookmark.advert',
        'advert',
        'advert.deletedAt IS NULL',
      ) // Filtra Adverts que no están soft deleted
      .where('bookmark.userId = :userId', { userId })
      .addSelect([
        'advert.id',
        'advert.profileName',
      ])
      .getMany();
    */
  }

  async update(
    id: Bookmark['id'],
    payload: Partial<Bookmark>,
  ): Promise<Bookmark> {
    const entity = await this.bookmarkRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.bookmarkRepository.save(
      this.bookmarkRepository.create(
        BookmarkMapper.toPersistence({
          ...BookmarkMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return BookmarkMapper.toDomain(updatedEntity);
  }

  async remove(id: Bookmark['id']): Promise<void> {
    await this.bookmarkRepository.softDelete(id);
  }
}
