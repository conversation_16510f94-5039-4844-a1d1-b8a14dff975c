import { Module } from '@nestjs/common';
import { CacheDataService } from './cache.service';
import { CacheDataController } from './cache.controller';
import { RelationalCacheDataPersistenceModule } from './infrastructure/persistence/relational/relational-persistence.module';

@Module({
  imports: [RelationalCacheDataPersistenceModule],
  controllers: [CacheDataController],
  providers: [CacheDataService],
  exports: [CacheDataService, RelationalCacheDataPersistenceModule],
})
export class CacheDataModule {}
