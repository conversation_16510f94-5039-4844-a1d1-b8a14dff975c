import { AdvertLocation } from '../../../../domain/advert-location';
import { AdvertLocationEntity } from '../entities/advert-location.entity';

export class AdvertLocationMapper {
  static toDomain(raw: AdvertLocationEntity): AdvertLocation {
    const domainEntity = new AdvertLocation();
    domainEntity.id = raw.id;
    domainEntity.advert = raw.advert;
    domainEntity.name = raw.name;
    domainEntity.url = raw.url;
    domainEntity.googleApiResponse = raw.googleApiResponse;
    domainEntity.neighborhood = raw.neighborhood;
    domainEntity.city = raw.city;
    domainEntity.region = raw.region;
    domainEntity.country = raw.country;
    domainEntity.createdAt = raw.createdAt;
    domainEntity.updatedAt = raw.updatedAt;
    domainEntity.deletedAt = raw.deletedAt;

    return domainEntity;
  }

  static toPersistence(domainEntity: AdvertLocation): AdvertLocationEntity {
    const persistenceEntity = new AdvertLocationEntity();
    if (domainEntity.id) {
      persistenceEntity.id = domainEntity.id;
    }
    persistenceEntity.advert = domainEntity.advert;
    persistenceEntity.name = domainEntity.name;
    persistenceEntity.url = domainEntity.url;
    persistenceEntity.googleApiResponse = domainEntity.googleApiResponse;
    persistenceEntity.neighborhood = domainEntity.neighborhood;
    persistenceEntity.city = domainEntity.city;
    persistenceEntity.region = domainEntity.region;
    persistenceEntity.country = domainEntity.country;
    persistenceEntity.createdAt = domainEntity.createdAt;
    persistenceEntity.updatedAt = domainEntity.updatedAt;
    persistenceEntity.deletedAt = domainEntity.deletedAt;

    return persistenceEntity;
  }
}
