import { Module } from '@nestjs/common';
import { AdvertLinkRepository } from '../advert-link.repository';
import { AdvertLinkRelationalRepository } from './repositories/advert-link.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertLinkEntity } from './entities/advert-link.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdvertLinkEntity])],
  providers: [
    {
      provide: AdvertLinkRepository,
      useClass: AdvertLinkRelationalRepository,
    },
  ],
  exports: [AdvertLinkRepository],
})
export class RelationalAdvertLinkPersistenceModule {}
