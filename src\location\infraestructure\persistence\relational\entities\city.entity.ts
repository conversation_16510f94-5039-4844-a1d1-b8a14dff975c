import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { RegionEntity } from './region.entity';
import { NeighborhoodEntity } from './neighborhood.entity';
import { AdvertLocationEntity } from '../../../../../advert-locations/infrastructure/persistence/relational/entities/advert-location.entity';

@Entity({
  name: 'city',
})
export class CityEntity extends EntityRelationalHelper {
  @ApiResponseProperty({
    type: Number,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  name: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  code: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: null })
  slug: string;

  @ApiResponseProperty({
    type: String,
  })
  @Column({ default: '' })
  defaultLocation: string;

  @ManyToOne(() => RegionEntity, (region) => region.cities)
  @JoinColumn({ name: 'region_id' })
  region: RegionEntity;

  @Column('int', { name: 'region_id' })
  regionId: number;

  @OneToMany(() => NeighborhoodEntity, (neighborhood) => neighborhood.city)
  neighborhoods: NeighborhoodEntity[];

  @OneToMany(
    () => AdvertLocationEntity,
    (advertLocation) => advertLocation.city,
  )
  advertLocations: AdvertLocationEntity[];

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
