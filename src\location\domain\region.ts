import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { CountryEntity } from '../infraestructure/persistence/relational/entities/country.entity';

export class Region {
  @ApiResponseProperty({ type: Number })
  id: number;

  @ApiResponseProperty({ type: String })
  name: string;

  @ApiResponseProperty({ type: String })
  code: string;

  @ApiResponseProperty({ type: String })
  slug: string;

  @ApiResponseProperty({ type: () => CountryEntity })
  country: CountryEntity;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;
}
