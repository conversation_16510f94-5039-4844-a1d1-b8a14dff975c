import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RateTypeEntity } from '../../../../rate-types/infrastructure/persistence/relational/entities/rate-type.entity';
import { Repository } from 'typeorm';

@Injectable()
export class RateTypeSeedService {
  constructor(
    @InjectRepository(RateTypeEntity)
    private repository: Repository<RateTypeEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (count === 0) {
      await this.repository.save([
        this.repository.create({ name: '$ 0-100' }),
        this.repository.create({ name: '$$ 100-200' }),
        this.repository.create({ name: '$$$ 200-300' }),
        this.repository.create({ name: '$$$$ 350 <' }),
      ]);
    }
  }
}
