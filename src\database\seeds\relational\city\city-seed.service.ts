import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CityEntity } from '../../../../location/infraestructure/persistence/relational/entities/city.entity';

@Injectable()
export class CitySeedService {
  constructor(
    @InjectRepository(CityEntity)
    private repository: Repository<CityEntity>,
  ) {}

  async run() {
    const count = await this.repository.count();

    if (!count) {
      await this.repository.save([
        this.repository.create({
          name: `Álava`,
          region: { id: 18 },
          slug: `alava`,
          defaultLocation: ``,
          code: `VI`,
        }),
        this.repository.create({
          name: `Albacete`,
          region: { id: 7 },
          slug: `albacete`,
          defaultLocation: ``,
          code: `AB`,
        }),
        this.repository.create({
          name: `Alicante`,
          region: { id: 11 },
          slug: `alicante`,
          defaultLocation: ``,
          code: `A`,
        }),
        this.repository.create({
          name: `Almería`,
          region: { id: 3 },
          slug: `almeria`,
          defaultLocation: ``,
          code: `AL`,
        }),
        this.repository.create({
          name: `Oviedo`,
          region: { id: 5 },
          slug: `asturias`,
          defaultLocation: ``,
          code: `O`,
        }),
        this.repository.create({
          name: `Ávila`,
          region: { id: 8 },
          slug: `avila`,
          defaultLocation: ``,
          code: `AV`,
        }),
        this.repository.create({
          name: `Badajoz`,
          region: { id: 12 },
          slug: `badajoz`,
          defaultLocation: ``,
          code: `BA`,
        }),
        this.repository.create({
          name: `Barcelona`,
          region: { id: 1 },
          slug: `barcelona`,
          defaultLocation: `ChIJ5TCOcRaYpBIRCmZHTz37sEQ`,
          code: `B`,
        }),
        this.repository.create({
          name: `Burgos`,
          region: { id: 8 },
          slug: `burgos`,
          defaultLocation: ``,
          code: `BU`,
        }),
        this.repository.create({
          name: `Cáceres`,
          region: { id: 12 },
          slug: `caceres`,
          defaultLocation: ``,
          code: `CC`,
        }),
        this.repository.create({
          name: `Cádiz`,
          region: { id: 3 },
          slug: `cadiz`,
          defaultLocation: ``,
          code: `CA`,
        }),
        this.repository.create({
          name: `Cantabria`,
          region: { id: 6 },
          slug: `cantabria`,
          defaultLocation: ``,
          code: `S`,
        }),
        this.repository.create({
          name: `Castellón`,
          region: { id: 11 },
          slug: `castellon`,
          defaultLocation: ``,
          code: `CS`,
        }),
        this.repository.create({
          name: `CEUTA`,
          region: { id: 10 },
          slug: `ceuta`,
          defaultLocation: ``,
          code: `CE`,
        }),
        this.repository.create({
          name: `Ciudad Real`,
          region: { id: 7 },
          slug: `ciudadreal`,
          defaultLocation: ``,
          code: `CR`,
        }),
        this.repository.create({
          name: `Córdoba`,
          region: { id: 3 },
          slug: `cordoba`,
          defaultLocation: ``,
          code: `CO`,
        }),
        this.repository.create({
          name: `Cuenca`,
          region: { id: 7 },
          slug: `cuenca`,
          defaultLocation: ``,
          code: `CU`,
        }),
        this.repository.create({
          name: `Girona`,
          region: { id: 1 },
          slug: `girona`,
          defaultLocation: ``,
          code: `GI`,
        }),
        this.repository.create({
          name: `Granada`,
          region: { id: 3 },
          slug: `granada`,
          defaultLocation: ``,
          code: `GR`,
        }),
        this.repository.create({
          name: `Guadalajara`,
          region: { id: 7 },
          slug: `guadalajara`,
          defaultLocation: ``,
          code: `GU`,
        }),
        this.repository.create({
          name: `Guipúzcoa`,
          region: { id: 18 },
          slug: `guipuzcoa`,
          defaultLocation: ``,
          code: `SS`,
        }),
        this.repository.create({
          name: `Huelva`,
          region: { id: 3 },
          slug: `huelva`,
          defaultLocation: ``,
          code: `H`,
        }),
        this.repository.create({
          name: `Huesca`,
          region: { id: 4 },
          slug: `huesca`,
          defaultLocation: ``,
          code: `HU`,
        }),
        this.repository.create({
          name: `Illes Balears`,
          region: { id: 19 },
          slug: `illesbalears`,
          defaultLocation: ``,
          code: `PM`,
        }),
        this.repository.create({
          name: `Jaén`,
          region: { id: 3 },
          slug: `jaen`,
          defaultLocation: ``,
          code: `J`,
        }),
        this.repository.create({
          name: `La Coruña`,
          region: { id: 13 },
          slug: `lacoruna`,
          defaultLocation: ``,
          code: `C`,
        }),
        this.repository.create({
          name: `La Rioja`,
          region: { id: 14 },
          slug: `larioja`,
          defaultLocation: ``,
          code: `LO`,
        }),
        this.repository.create({
          name: `Las Palmas`,
          region: { id: 9 },
          slug: `laspalmas`,
          defaultLocation: ``,
          code: `GC`,
        }),
        this.repository.create({
          name: `León`,
          region: { id: 8 },
          slug: `leon`,
          defaultLocation: ``,
          code: `LE`,
        }),
        this.repository.create({
          name: `Lérida`,
          region: { id: 1 },
          slug: `lleida`,
          defaultLocation: ``,
          code: `L`,
        }),
        this.repository.create({
          name: `Lugo`,
          region: { id: 13 },
          slug: `lugo`,
          defaultLocation: ``,
          code: `LU`,
        }),
        this.repository.create({
          name: `Madrid`,
          region: { id: 2 },
          slug: `madrid`,
          defaultLocation: `ChIJgTwKgJcpQg0RaSKMYcHeNsQ`,
          code: `M`,
        }),
        this.repository.create({
          name: `Málaga`,
          region: { id: 3 },
          slug: `malaga`,
          defaultLocation: ``,
          code: `MA`,
        }),
        this.repository.create({
          name: `MELILLA`,
          region: { id: 15 },
          slug: `melilla`,
          defaultLocation: ``,
          code: `ML`,
        }),
        this.repository.create({
          name: `Murcia`,
          region: { id: 16 },
          slug: `murcia`,
          defaultLocation: ``,
          code: `MU`,
        }),
        this.repository.create({
          name: `Navarra`,
          region: { id: 17 },
          slug: `navarra`,
          defaultLocation: ``,
          code: `NA`,
        }),
        this.repository.create({
          name: `Ourense`,
          region: { id: 13 },
          slug: `ourense`,
          defaultLocation: ``,
          code: `OR`,
        }),
        this.repository.create({
          name: `Palencia`,
          region: { id: 8 },
          slug: `palencia`,
          defaultLocation: ``,
          code: `P`,
        }),
        this.repository.create({
          name: `Pontevedra`,
          region: { id: 13 },
          slug: `pontevedra`,
          defaultLocation: ``,
          code: `PO`,
        }),
        this.repository.create({
          name: `Salamanca`,
          region: { id: 8 },
          slug: `salamanca`,
          defaultLocation: ``,
          code: `SA`,
        }),
        this.repository.create({
          name: `Santa Cruz de Tenerife`,
          region: { id: 9 },
          slug: `santacruztenerife`,
          defaultLocation: ``,
          code: `TF`,
        }),
        this.repository.create({
          name: `Segovia`,
          region: { id: 8 },
          slug: `segovia`,
          defaultLocation: ``,
          code: `SG`,
        }),
        this.repository.create({
          name: `Sevilla`,
          region: { id: 3 },
          slug: `sevilla`,
          defaultLocation: ``,
          code: `SE`,
        }),
        this.repository.create({
          name: `Soria`,
          region: { id: 8 },
          slug: `soria`,
          defaultLocation: ``,
          code: `SO`,
        }),
        this.repository.create({
          name: `Tarragona`,
          region: { id: 1 },
          slug: `tarragona`,
          defaultLocation: ``,
          code: `T`,
        }),
        this.repository.create({
          name: `Teruel`,
          region: { id: 4 },
          slug: `teruel`,
          defaultLocation: ``,
          code: `TE`,
        }),
        this.repository.create({
          name: `Toledo`,
          region: { id: 7 },
          slug: `toledo`,
          defaultLocation: ``,
          code: `TO`,
        }),
        this.repository.create({
          name: `Valencia`,
          region: { id: 11 },
          slug: `valencia`,
          defaultLocation: ``,
          code: `V`,
        }),
        this.repository.create({
          name: `Valladolid`,
          region: { id: 8 },
          slug: `valladolid`,
          defaultLocation: ``,
          code: `VA`,
        }),
        this.repository.create({
          name: `Vizcaya`,
          region: { id: 18 },
          slug: `vizcaya`,
          defaultLocation: ``,
          code: `BI`,
        }),
        this.repository.create({
          name: `Zamora`,
          region: { id: 8 },
          slug: `zamora`,
          defaultLocation: ``,
          code: `ZA`,
        }),
        this.repository.create({
          name: `Zaragoza`,
          region: { id: 4 },
          slug: `zaragoza`,
          defaultLocation: ``,
          code: `Z`,
        }),
      ]);
    }
  }
}
