import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOfferPackTablesBalance1749037554722
  implements MigrationInterface
{
  name = 'UpdateOfferPackTablesBalance1749037554722';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ADD "available" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ADD "balance" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ALTER COLUMN "active" SET DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "offer_pack" ALTER COLUMN "active" DROP DEFAULT`,
    );
    await queryRunner.query(`ALTER TABLE "offer_pack" DROP COLUMN "balance"`);
    await queryRunner.query(`ALTER TABLE "offer_pack" DROP COLUMN "available"`);
  }
}
