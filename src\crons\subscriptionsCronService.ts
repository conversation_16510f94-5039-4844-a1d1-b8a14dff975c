import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { DataSource } from 'typeorm';
import { SubscriptionStatus } from '../subscriptions/domain/subscription';
import { MailService } from '../mail/mail.service';
import { CacheServiceHelper } from '../helpers/cache/cache.service';

@Injectable()
export class ComprobacionesService {
  private readonly logger = new Logger(ComprobacionesService.name);

  constructor(
    private readonly datasource: DataSource,
    private readonly mailService: MailService,
    private readonly cacheServiceHelper: CacheServiceHelper,
  ) {}

  @Cron('0 */5 * * * *') // Ejecuta la función cada x minutos
  //@Cron('*/30 * * * * *') // Se ejecuta cada x segundos
  async handleComprobaciones() {
    this.logger.debug('Ejecutando comprobaciones cada 5 minutos...');
    let affected = 0;

    // CADUCADAS
    affected += await this.expiredSubscriptionsBase();
    affected += await this.expiredSubscriptions(2, ['oDoubleAdvert']);
    affected += await this.expiredSubscriptions(11, ['oTopAdvert']);
    affected += await this.expiredSubscriptions(12, [
      'oTopDoubleAdvert',
      'oTopAdvert',
      'oDoubleAdvert',
    ]);
    affected += await this.expiredSubscriptions(50, ['oAvailableNow']);
    affected += await this.expiredSubscriptions(60, ['oTopStories']);
    affected += await this.expiredSubscriptions(98, ['oReactivate']);

    // ACTIVAR
    affected += await this.activatedSubscriptionsBase();
    affected += await this.activatedSubscriptions(2, ['oDoubleAdvert']);
    affected += await this.activatedSubscriptions(11, ['oTopAdvert']);
    affected += await this.activatedSubscriptions(12, [
      'oTopDoubleAdvert',
      'oTopAdvert',
      'oDoubleAdvert',
    ]);
    affected += await this.activatedSubscriptions(50, ['oAvailableNow']);
    affected += await this.activatedSubscriptions(60, ['oTopStories']);
    affected += await this.activatedSubscriptions(98, ['oReactivate']);

    console.info(affected);
  }

  async expiredSubscriptionsBase(): Promise<number> {
    const itemQuery = `SELECT * FROM subscription WHERE ("status" = 'active') AND "productId" = 1 AND "endDate" <= NOW()`;
    const itemsSubscriptions = await this.datasource.query(itemQuery);
    if (itemsSubscriptions.length > 0) {
      const subscriptionsIdS = itemsSubscriptions.map((item) => `'${item.id}'`);
      const advertIdS = itemsSubscriptions.map((item) => `'${item.advertId}'`);
      const updateSubscriptionQuery = `
            UPDATE "subscription" 
               SET "status" = '${SubscriptionStatus.EXPIRED.toLowerCase()}',
                   "updatedAt" = NOW()
             WHERE "id" IN (${subscriptionsIdS.join(',')})`;
      await this.datasource.query(updateSubscriptionQuery);
      const updateAdvertQuery = `
            UPDATE "advert" 
               SET "statusId" = 99,
                   "updatedAt" = NOW()  
             WHERE "id" IN (${advertIdS.join(',')})`;
      await this.datasource.query(updateAdvertQuery);

      for (const _id of advertIdS) {
        await this.cacheServiceHelper.resetCacheByAdvertId(_id);
      }
      this.logger.log(
        `[S-CRON] (BASE) ${itemsSubscriptions.length} Expired Subscriptions`,
      );
      return itemsSubscriptions.length;
    }
    return 0;
  }

  async activatedSubscriptionsBase(): Promise<number> {
    const itemQuery = `
        SELECT s."id", s."advertId"
          FROM subscription s JOIN advert a on s."advertId" = a.id 
         WHERE (s."status" = 'inactive') 
           AND s."productId" = 1 
           AND s."startDate" <= NOW()
           AND a."statusId" IN (1, 2, 11, 99)  /* 1:ACTIVE 2:INACTIVE 11: CAN ACTIVATED 99:EXPIRED */
        `;
    /* STATUS NO > 3: REVIEW  10: REVIEW ACTIVATION */
    const itemsSubscriptions = await this.datasource.query(itemQuery);
    if (itemsSubscriptions.length > 0) {
      const subscriptionsIdS = itemsSubscriptions.map((item) => `'${item.id}'`);
      const advertIdS = itemsSubscriptions.map((item) => `'${item.advertId}'`);
      const updateSubscriptionQuery = `
            UPDATE "subscription" 
               SET "status" = '${SubscriptionStatus.ACTIVE.toLowerCase()}',
                   "updatedAt" = NOW()
             WHERE "id" IN (${subscriptionsIdS.join(',')})`;
      await this.datasource.query(updateSubscriptionQuery);
      const updateAdvertQuery = `
            UPDATE "advert" 
               SET "statusId" = 1,
                   "updatedAt" = NOW()  
             WHERE "id" IN (${advertIdS.join(',')})
               AND "advert"."statusId" IN (11, 99)`;
      await this.datasource.query(updateAdvertQuery);
      for (const _id of advertIdS) {
        await this.cacheServiceHelper.resetCacheByAdvertId(_id);
      }
      this.logger.log(
        `[S-CRON] (BASE) ${itemsSubscriptions.length} Expired Subscriptions`,
      );
      return itemsSubscriptions.length;
    }
    return 0;
  }

  async expiredSubscriptions(
    _productId: number,
    _type: string[],
  ): Promise<number> {
    const itemQuery = `SELECT * FROM subscription WHERE ("status" = 'active') AND "productId" = ${_productId} AND "endDate" <= NOW()`;
    const expiredAdverts = await this.datasource.query(itemQuery);
    if (expiredAdverts.length > 0) {
      const subscriptionsIdS = expiredAdverts.map((item) => `'${item.id}'`);
      const advertIdS = expiredAdverts.map((item) => `'${item.advertId}'`);
      await this.updateSubscriptionField('status', 'expired', subscriptionsIdS);
      for (const _item of _type) {
        await this.updateAdvertField(_item, false, advertIdS);
        await this.mailService.expiresSubscription({
          to: '<EMAIL>',
          data: {
            hash: '',
          },
        });
      }
      for (const _id of advertIdS) {
        await this.cacheServiceHelper.resetCacheByAdvertId(_id);
      }
      this.logger.log(
        `[S-CRON] (${_productId}-${_type}) ${expiredAdverts.length} Expired Subscriptions`,
      );
      return expiredAdverts.length;
    }
    return 0;
  }

  async activatedSubscriptions(
    _productId: number,
    _type: string[],
  ): Promise<number> {
    const itemQuery = `SELECT * FROM subscription WHERE ("status" = 'inactive') AND "productId" = ${_productId} AND "startDate" <= NOW()`;
    const activatedAdverts = await this.datasource.query(itemQuery);
    if (activatedAdverts.length > 0) {
      const subscriptionsIdS = activatedAdverts.map((item) => `'${item.id}'`);
      const advertIdS = activatedAdverts.map((item) => `'${item.advertId}'`);
      await this.updateSubscriptionField('status', 'active', subscriptionsIdS);
      for (const _item of _type) {
        await this.updateAdvertField(_item, true, advertIdS);
      }
      for (const _id of advertIdS) {
        await this.cacheServiceHelper.resetCacheByAdvertId(_id);
      }
      this.logger.log(
        `[S-CRON] (${_productId}-${_type}) ${activatedAdverts.length} Activated Subscriptions`,
      );
      return activatedAdverts.length;
    }
    return 0;
  }

  async updateAdvertField(
    _field: string,
    _value: number | string | boolean,
    _idIN: any,
  ) {
    const updateQuery = `
        UPDATE "advert" 
           SET "${_field}" = '${_value}',
               "updatedAt" = NOW() 
         WHERE "id" IN (${_idIN.join(',')})`;
    await this.datasource.query(updateQuery);
  }

  async updateSubscriptionField(
    _field: string,
    _value: number | string | boolean,
    _idIN: any,
  ) {
    const updateQuery = `
        UPDATE "subscription" 
           SET "${_field}" = '${_value}',
               "updatedAt" = NOW()
         WHERE "id" IN (${_idIN.join(',')})`;
    await this.datasource.query(updateQuery);
  }
}
