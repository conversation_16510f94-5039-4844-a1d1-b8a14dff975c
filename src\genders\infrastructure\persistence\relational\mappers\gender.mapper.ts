import { BaseEntityMapper } from '../../../../../utils/mappers/base-entity-mapper';
import { Gender } from '../../../../domain/gender';
import { GenderEntity } from '../entities/gender.entity';

export class GenderMapper {
  private static baseMapper = new (class extends BaseEntityMapper<
    Gender,
    GenderEntity
  > {})();

  static toPersistence(domainEntity: Gender): GenderEntity {
    const persistenceEntity = new GenderEntity();
    return this.baseMapper.mapToPersistenceCommon(
      domainEntity,
      persistenceEntity,
    );
  }

  static toDomain(raw: GenderEntity): Gender {
    const domainEntity = new Gender();
    return this.baseMapper.mapToDomainCommon(raw, domainEntity);
  }
}
