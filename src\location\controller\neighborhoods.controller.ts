import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { NeighborhoodsService } from '../service/neighborhoods.service';
import { CreateNeighborhoodDto } from '../dto/create-neighborhood.dto';
import { UpdateNeighborhoodDto } from '../dto/update-neighborhood.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { Neighborhood } from '../domain/neighborhood';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../../utils/infinity-pagination';
import { FindAllNeighborhoodsDto } from '../dto/find-all-neighborhoods.dto';
import { plainToInstance } from 'class-transformer';

@ApiTags('Neighborhoods')
@Controller({
  path: 'neighborhoods',
  version: '1',
})
export class NeighborhoodsController {
  constructor(private readonly neighborhoodsService: NeighborhoodsService) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiCreatedResponse({
    type: Neighborhood,
  })
  create(@Body() createNeighborhoodDto: any) {
    const data = plainToInstance(CreateNeighborhoodDto, createNeighborhoodDto);
    return this.neighborhoodsService.create(data);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(Neighborhood),
  })
  async findAll(
    @Query() query: FindAllNeighborhoodsDto,
  ): Promise<InfinityPaginationResponseDto<Neighborhood>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.neighborhoodsService.findAllWithPagination(
        {
          paginationOptions: {
            page,
            limit,
          },
        },
        query,
      ),
      { page, limit },
    );
  }

  @Get('all')
  @ApiOkResponse({
    type: Neighborhood,
  })
  async findAllWithoutPagination(): Promise<Neighborhood[]> {
    return await this.neighborhoodsService.findAll();
  }

  @Get('allcount')
  async findAllCountWithoutPagination(@Query() query: any): Promise<any[]> {
    if (query?.city) {
      return await this.neighborhoodsService.findAllAndCountByCity(query?.city);
    } else {
      return await this.neighborhoodsService.findAllAndCount();
    }
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.neighborhoodsService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  @ApiOkResponse({
    type: Neighborhood,
  })
  update(@Param('id') id: number, @Body() updateNeighborhoodDto: any) {
    const data = plainToInstance(UpdateNeighborhoodDto, updateNeighborhoodDto);

    return this.neighborhoodsService.update(id, data);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard('jwt'))
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.neighborhoodsService.remove(id);
  }
}
