import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NeighborhoodEntity } from '../entities/neighborhood.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { Neighborhood } from '../../../../domain/neighborhood';
import { NeighborhoodRepository } from '../../neighborhood.repository';
import { NeighborhoodMapper } from '../mappers/neighborhood.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';
import {
  DateFields,
  FindAllNeighborhoodsDto,
  NumberFields,
  RelationalFields,
  StringFields,
} from '../../../../dto/find-all-neighborhoods.dto';

@Injectable()
export class NeighborhoodRelationalRepository
  implements NeighborhoodRepository
{
  constructor(
    @InjectRepository(NeighborhoodEntity)
    private readonly neighborhoodRepository: Repository<NeighborhoodEntity>,
  ) {}

  async create(data: Neighborhood): Promise<Neighborhood> {
    const persistenceModel = NeighborhoodMapper.toPersistence(data);
    const newEntity = await this.neighborhoodRepository.save(
      this.neighborhoodRepository.create(persistenceModel),
    );
    return NeighborhoodMapper.toDomain(newEntity);
  }

  async findAllWithPagination(
    {
      paginationOptions,
    }: {
      paginationOptions: IPaginationOptions;
    },
    filters: FindAllNeighborhoodsDto,
  ): Promise<Neighborhood[]> {
    const query =
      this.neighborhoodRepository.createQueryBuilder('neighborhood');
    query.leftJoinAndSelect('neighborhood.city', 'city');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page, limit, ...updatedDto } = filters;
    if (updatedDto) {
      Object.entries(updatedDto).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (RelationalFields.includes(key)) {
            query.andWhere(`${key}.id = :${key}`, { [key]: value });
          } else if (StringFields.includes(key)) {
            query.andWhere(`LOWER(neighborhood.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          } else if (NumberFields.includes(key)) {
            query.andWhere(`neighborhood.${key} = :${key}`, {
              [key]: Number(value),
            });
          } else if (DateFields.includes(key)) {
            query.andWhere(`neighborhood.${key} = :${key}`, {
              [key]: new Date(value),
            });
          }
        }
      });
    }
    query
      .skip((paginationOptions.page - 1) * paginationOptions.limit)
      .take(paginationOptions.limit);

    const entities = await query.getMany();

    return entities.map((entity) => NeighborhoodMapper.toDomain(entity));
  }

  async findAll(): Promise<Neighborhood[]> {
    const entities = await this.neighborhoodRepository.find({
      relations: ['city'],
    });

    return entities.map((element) => NeighborhoodMapper.toDomain(element));
  }

  async findAllAndCount(): Promise<any> {
    return await this.neighborhoodRepository
      .createQueryBuilder('neighborhood')
      .innerJoinAndSelect('neighborhood.advertLocations', 'advert_location')
      .innerJoinAndSelect('advert_location.advert', 'advert')
      .select('neighborhood.id', 'neighborhoodId')
      .addSelect('neighborhood.name', 'neighborhoodName')
      .addSelect('neighborhood.slug', 'neighborhoodSlug')
      .addSelect('COUNT(advert_location.id)', 'locationCount')
      .addSelect("CONCAT(neighborhood.name, ' (', COUNT(*), ')')", 'title')
      .where('advert.status = :status', { status: 1 })
      .groupBy('neighborhood.id')
      .addGroupBy('neighborhood.name')
      .getRawMany();
  }

  async findAllAndCountByCity(cityId: string): Promise<any> {
    return await this.neighborhoodRepository
      .createQueryBuilder('neighborhood')
      .innerJoinAndSelect('neighborhood.advertLocations', 'advert_location')
      .select('neighborhood.id', 'neighborhoodId')
      .addSelect('neighborhood.name', 'neighborhoodName')
      .addSelect('COUNT(advert_location.id)', 'locationCount')
      .addSelect("CONCAT(neighborhood.name, ' (', COUNT(*), ')')", 'title')
      .andWhere(`neighborhood.city = :cityId`, {
        cityId: cityId,
      })
      .groupBy('neighborhood.id')
      .addGroupBy('neighborhood.name')
      .getRawMany();
  }

  async findById(id: Neighborhood['id']): Promise<NullableType<Neighborhood>> {
    const entity = await this.neighborhoodRepository.findOne({
      where: { id },
      relations: ['city'],
    });

    return entity ? NeighborhoodMapper.toDomain(entity) : null;
  }

  async update(
    id: Neighborhood['id'],
    payload: Partial<Neighborhood>,
  ): Promise<Neighborhood> {
    const entity = await this.neighborhoodRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.neighborhoodRepository.save(
      this.neighborhoodRepository.create(
        NeighborhoodMapper.toPersistence({
          ...NeighborhoodMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return NeighborhoodMapper.toDomain(updatedEntity);
  }

  async remove(id: Neighborhood['id']): Promise<void> {
    await this.neighborhoodRepository.softDelete(id);
  }
}
