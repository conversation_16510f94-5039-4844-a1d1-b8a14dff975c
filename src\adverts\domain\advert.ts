import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { User } from '../../users/domain/user';
import { Gender } from '../../genders/domain/gender';
import { AdvertType } from '../../advert-types/domain/advert-type';
import { Nationality } from '../../nationalities/domain/nationality';
import { ChestSize } from '../../chest-sizes/domain/chest-size';
import { AdvertStatus } from '../../advert-statuses/domain/advert-status';
import { Service } from '../../services/domain/service';
import { Language } from '../../languages/domain/language';
import { Orientation } from '../../orientations/domain/orientation';
import { EyeColor } from '../../eye-colors/domain/eye-color';
import { HairColor } from '../../hair-colors/domain/hair-color';
import { Race } from '../../races/domain/race';
import { AdvertLink } from '../../advert-links/domain/advert-link';
import { AdvertRate } from '../../advert-rates/domain/advert-rate';
import { AdvertFile } from '../../advert-files/domain/advert-file';
import { AdvertLocation } from '../../advert-locations/domain/advert-location';
import { Expose } from 'class-transformer';
import { Subscription } from 'src/subscriptions/domain/subscription';

export class Advert {
  @ApiProperty({
    type: String,
  })
  id: string;

  @ApiResponseProperty({
    type: String,
  })
  @Expose()
  profileName: string;

  @ApiResponseProperty({
    type: String,
  })
  @Expose()
  slug: string;

  @ApiResponseProperty({
    type: String,
  })
  phoneNumber: string;

  @ApiResponseProperty({
    type: Boolean,
  })
  whatsapp: boolean;

  @ApiResponseProperty({
    type: String,
  })
  introduction: string;

  @ApiResponseProperty({
    type: () => Gender,
  })
  gender: Gender;

  @ApiResponseProperty({
    type: () => AdvertType,
  })
  advertType: AdvertType;

  @ApiResponseProperty({
    type: Number,
  })
  birthDate: number;

  @ApiResponseProperty({
    type: Number,
  })
  height: number;

  @ApiResponseProperty({
    type: Number,
  })
  breastSize: number;

  @ApiResponseProperty({
    type: () => ChestSize,
  })
  chestSize: ChestSize;

  @ApiResponseProperty({
    type: Number,
  })
  waist: number;

  @ApiResponseProperty({
    type: Number,
  })
  hips: number;

  @ApiResponseProperty({
    type: () => Orientation,
  })
  orientation: Orientation;

  @ApiResponseProperty({
    type: () => EyeColor,
  })
  eyeColor: EyeColor;

  @ApiResponseProperty({
    type: () => HairColor,
  })
  hairColor: HairColor;

  @ApiResponseProperty({
    type: () => Race,
  })
  race: Race;

  @ApiResponseProperty({
    type: Boolean,
  })
  smoke: boolean;

  @ApiResponseProperty({
    type: String,
  })
  location: string;

  @ApiResponseProperty({
    type: String,
  })
  email: string;

  @ApiResponseProperty({
    type: String,
  })
  www: string;

  @ApiResponseProperty({
    type: String,
  })
  timetable: string;

  @ApiResponseProperty({
    type: () => Nationality,
  })
  nationality: Nationality;

  @ApiResponseProperty({
    type: () => AdvertStatus,
  })
  status: AdvertStatus;

  @ApiResponseProperty({
    type: Boolean,
  })
  featured: boolean;

  @ApiResponseProperty({
    type: Number,
  })
  rating: number;

  @ApiResponseProperty({
    type: () => User,
  })
  user: User;

  @ApiResponseProperty({
    type: () => AdvertLink,
  })
  advertLinks: AdvertLink[];

  @ApiResponseProperty({
    type: () => AdvertRate,
  })
  advertRates: AdvertRate[];

  @ApiResponseProperty({
    type: () => AdvertFile,
  })
  advertFiles: AdvertFile[];

  @ApiResponseProperty({
    type: () => AdvertLocation,
  })
  advertLocations: AdvertLocation[];

  @ApiResponseProperty({
    type: () => Subscription,
  })
  subscriptions: Subscription[];

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  deletedAt: Date;

  @ApiResponseProperty({
    type: () => Service,
  })
  services: Service[];

  @ApiResponseProperty({
    type: () => Language,
  })
  languages: Language[];

  @ApiResponseProperty({ type: Boolean })
  oTopAdvert: boolean;

  @ApiResponseProperty({ type: Boolean })
  oTopDoubleAdvert: boolean;

  @ApiResponseProperty({ type: Boolean })
  oDoubleAdvert: boolean;

  @ApiResponseProperty({ type: Boolean })
  oAvailableNow: boolean;

  @ApiResponseProperty({ type: Boolean })
  oReactivate: boolean;

  @ApiResponseProperty({ type: Boolean })
  oTopStories: boolean;
}

export class AdvertSiteMap {
  @ApiResponseProperty({
    type: String,
  })
  @Expose()
  slugName: string;

  @ApiResponseProperty({
    type: String,
  })
  @Expose()
  slugCity: string;
}
