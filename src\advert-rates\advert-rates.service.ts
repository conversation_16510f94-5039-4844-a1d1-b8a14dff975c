import { Injectable } from '@nestjs/common';
import { CreateAdvertRateDto } from './dto/create-advert-rate.dto';
import { UpdateAdvertRateDto } from './dto/update-advert-rate.dto';
import { AdvertRateRepository } from './infrastructure/persistence/advert-rate.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { AdvertRate } from './domain/advert-rate';

@Injectable()
export class AdvertRatesService {
  constructor(private readonly advert_rateRepository: AdvertRateRepository) {}

  create(createAdvertRateDto: CreateAdvertRateDto) {
    return this.advert_rateRepository.create(createAdvertRateDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.advert_rateRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  findAll() {
    return this.advert_rateRepository.findAll();
  }

  findOne(id: AdvertRate['id']) {
    return this.advert_rateRepository.findById(id);
  }

  findMany(advertId: string) {
    return this.advert_rateRepository.findMany(advertId);
  }

  update(id: AdvertRate['id'], updateAdvertRateDto: UpdateAdvertRateDto) {
    return this.advert_rateRepository.update(id, updateAdvertRateDto);
  }

  remove(id: AdvertRate['id']) {
    return this.advert_rateRepository.remove(id);
  }
}
