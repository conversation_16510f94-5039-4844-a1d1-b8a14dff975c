import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { AdvertStatusesService } from './advert-statuses.service';
import { CreateAdvertStatusDto } from './dto/create-advert-status.dto';
import { UpdateAdvertStatusDto } from './dto/update-advert-status.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { AdvertStatus } from './domain/advert-status';
import { AuthGuard } from '@nestjs/passport';
import {
  InfinityPaginationResponse,
  InfinityPaginationResponseDto,
} from '../utils/dto/infinity-pagination-response.dto';
import { infinityPagination } from '../utils/infinity-pagination';
import { FindAllAdvertStatusesDto } from './dto/find-all-advert-statuses.dto';

@ApiTags('AdvertStatuses')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'advert-statuses',
  version: '1',
})
export class AdvertStatusesController {
  constructor(private readonly advert_statusesService: AdvertStatusesService) {}

  @Post()
  @ApiCreatedResponse({
    type: AdvertStatus,
  })
  create(@Body() createAdvertStatusDto: CreateAdvertStatusDto) {
    return this.advert_statusesService.create(createAdvertStatusDto);
  }

  @Get()
  @ApiOkResponse({
    type: InfinityPaginationResponse(AdvertStatus),
  })
  async findAll(
    @Query() query: FindAllAdvertStatusesDto,
  ): Promise<InfinityPaginationResponseDto<AdvertStatus>> {
    const page = query?.page ?? 1;
    let limit = query?.limit ?? 50;
    if (limit > 50) {
      limit = 50;
    }

    return infinityPagination(
      await this.advert_statusesService.findAllWithPagination({
        paginationOptions: {
          page,
          limit,
        },
      }),
      { page, limit },
    );
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  findOne(@Param('id') id: number) {
    return this.advert_statusesService.findOne(id);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  @ApiOkResponse({
    type: AdvertStatus,
  })
  update(
    @Param('id') id: number,
    @Body() updateAdvertStatusDto: UpdateAdvertStatusDto,
  ) {
    return this.advert_statusesService.update(id, updateAdvertStatusDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  remove(@Param('id') id: number) {
    return this.advert_statusesService.remove(id);
  }
}
