import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EyeColorEntity } from '../entities/eye-color.entity';
import { NullableType } from '../../../../../utils/types/nullable.type';
import { EyeColor } from '../../../../domain/eye-color';
import { EyeColorRepository } from '../../eye-color.repository';
import { EyeColorMapper } from '../mappers/eye-color.mapper';
import { IPaginationOptions } from '../../../../../utils/types/pagination-options';

@Injectable()
export class EyeColorRelationalRepository implements EyeColorRepository {
  constructor(
    @InjectRepository(EyeColorEntity)
    private readonly eye_colorRepository: Repository<EyeColorEntity>,
  ) {}

  async create(data: EyeColor): Promise<EyeColor> {
    const persistenceModel = EyeColorMapper.toPersistence(data);
    const newEntity = await this.eye_colorRepository.save(
      this.eye_colorRepository.create(persistenceModel),
    );
    return EyeColorMapper.toDomain(newEntity);
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }): Promise<EyeColor[]> {
    const entities = await this.eye_colorRepository.find({
      skip: (paginationOptions.page - 1) * paginationOptions.limit,
      take: paginationOptions.limit,
    });

    return entities.map((user) => EyeColorMapper.toDomain(user));
  }

  async findAll(): Promise<EyeColor[]> {
    const entities = await this.eye_colorRepository.find();

    return entities.map((user) => EyeColorMapper.toDomain(user));
  }

  async findById(id: EyeColor['id']): Promise<NullableType<EyeColor>> {
    const entity = await this.eye_colorRepository.findOne({
      where: { id },
    });

    return entity ? EyeColorMapper.toDomain(entity) : null;
  }

  async update(
    id: EyeColor['id'],
    payload: Partial<EyeColor>,
  ): Promise<EyeColor> {
    const entity = await this.eye_colorRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new Error('Record not found');
    }

    const updatedEntity = await this.eye_colorRepository.save(
      this.eye_colorRepository.create(
        EyeColorMapper.toPersistence({
          ...EyeColorMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return EyeColorMapper.toDomain(updatedEntity);
  }

  async remove(id: EyeColor['id']): Promise<void> {
    await this.eye_colorRepository.softDelete(id);
  }
}
