import {
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
} from 'typeorm';
import { EntityRelationalHelper } from '../../../../../utils/relational-entity-helper';
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger';
import { AdvertEntity } from '../../../../../adverts/infrastructure/persistence/relational/entities/advert.entity';
import { Advert } from '../../../../../adverts/domain/advert';
import { RateTypeEntity } from '../../../../../rate-types/infrastructure/persistence/relational/entities/rate-type.entity';
import { RateType } from '../../../../../rate-types/domain/rate-type';

@Entity({
  name: 'advert_rate',
})
export class AdvertRateEntity extends EntityRelationalHelper {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiResponseProperty({
    type: () => AdvertEntity,
  })
  @ManyToOne(() => AdvertEntity, (advert) => advert.advertRates)
  @ApiProperty({ type: () => Advert })
  advert: Advert;

  @ApiResponseProperty({
    type: () => RateTypeEntity,
  })
  @ManyToOne(() => RateTypeEntity, {
    eager: true,
  })
  rateType: RateType;

  @ApiResponseProperty({
    type: Number,
  })
  @Column({ default: 0 })
  amount: number;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty()
  @DeleteDateColumn()
  deletedAt: Date;
}
