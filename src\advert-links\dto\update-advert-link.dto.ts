import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateAdvertLinkDto } from './create-advert-link.dto';
import { Advert } from '../../adverts/domain/advert';
import { rrssType } from '../../rrss-types/domain/rrss-type';

export class UpdateAdvertLinkDto extends PartialType(CreateAdvertLinkDto) {
  @ApiProperty({
    type: Advert,
  })
  advert: Advert;

  @ApiProperty({
    type: rrssType,
  })
  rrssType: rrssType;

  @ApiProperty({
    type: String,
  })
  name: string;
}
