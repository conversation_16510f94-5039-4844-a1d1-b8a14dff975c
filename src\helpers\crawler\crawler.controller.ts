import { Controller, Get, Query } from '@nestjs/common';
import { CrawlerService } from './crawler.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Crawler')
@Controller({
  path: 'crawler',
  version: '1',
})
export class CrawlerController {
  constructor(private readonly crawlerService: CrawlerService) {}

  @Get()
  async getURL(@Query() query: any): Promise<any> {
    return await this.crawlerService.scrapeErosGuia(query);
  }
}
