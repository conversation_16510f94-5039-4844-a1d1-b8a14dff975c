import { Inject, Injectable } from '@nestjs/common';
import { CreateLanguageDto } from './dto/create-language.dto';
import { UpdateLanguageDto } from './dto/update-language.dto';
import { LanguageRepository } from './infrastructure/persistence/language.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { Language } from './domain/language';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class LanguagesService {
  constructor(
    private readonly languageRepository: LanguageRepository,
    @Inject(CACHE_MANAGER) private cacheService: Cache,
  ) {}

  create(createLanguageDto: CreateLanguageDto) {
    return this.languageRepository.create(createLanguageDto);
  }

  findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    return this.languageRepository.findAllWithPagination({
      paginationOptions: {
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      },
    });
  }

  async findAll() {
    const _key = 'CACHE-LANGUAGES-ALL';
    const cachedData = await this.cacheService.get<{ name: string }>(_key);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.languageRepository.findAll();
    if (data) {
      return await this.cacheService.set(_key, data);
    }
  }

  findOne(id: Language['id']) {
    return this.languageRepository.findById(id);
  }

  update(id: Language['id'], updateLanguageDto: UpdateLanguageDto) {
    return this.languageRepository.update(id, updateLanguageDto);
  }

  remove(id: Language['id']) {
    return this.languageRepository.remove(id);
  }
}
